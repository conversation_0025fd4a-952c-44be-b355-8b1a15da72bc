<svg width="600" height="400" viewBox="0 0 600 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="600" height="400" fill="url(#gradient)"/>
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect x="50" y="50" width="500" height="300" rx="20" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.2)" stroke-width="2"/>
  <circle cx="300" cy="150" r="40" fill="rgba(255,255,255,0.2)"/>
  <rect x="200" y="220" width="200" height="20" rx="10" fill="rgba(255,255,255,0.15)"/>
  <rect x="220" y="260" width="160" height="15" rx="7" fill="rgba(255,255,255,0.1)"/>
  <text x="300" y="320" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-family="Arial, sans-serif" font-size="16">Influencer slika će biti ovdje</text>
</svg>
