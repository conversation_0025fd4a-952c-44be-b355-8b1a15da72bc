'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { supabase } from '@/lib/supabase';

interface UsernameStepProps {
  value: string;
  onChange: (value: string) => void;
  onNext: () => void;
  title?: string;
  description?: string;
}

export function UsernameStep({
  value,
  onChange,
  onNext,
  title = 'Korisničko ime',
  description = 'Ovo korisničko ime će biti vidljivo na platformi',
}: UsernameStepProps) {
  const [isChecking, setIsChecking] = useState(false);
  const [isAvailable, setIsAvailable] = useState<boolean | null>(null);
  const [error, setError] = useState<string | null>(null);

  const checkUsernameAvailability = async (username: string) => {
    if (!username || username.length < 3) {
      setIsAvailable(null);
      setError(null);
      return;
    }

    // Basic validation
    const usernameRegex = /^[a-zA-Z0-9_]+$/;
    if (!usernameRegex.test(username)) {
      setIsAvailable(false);
      setError('Username može sadržavati samo slova, brojeve i _');
      return;
    }

    setIsChecking(true);
    setError(null);

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('username')
        .eq('username', username);

      if (error) {
        setIsAvailable(false);
        setError('Greška pri proveri username-a');
      } else if (data && data.length > 0) {
        // Username exists
        setIsAvailable(false);
        setError('Username je već zauzet');
      } else {
        // No rows returned - username is available
        setIsAvailable(true);
      }
    } catch (err) {
      setIsAvailable(false);
      setError('Greška pri proveri username-a');
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (value) {
        checkUsernameAvailability(value);
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [value]);

  const handleNext = () => {
    if (isAvailable && value.length >= 3) {
      onNext();
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-2">{title}</h2>
        <p className="text-white/70">{description}</p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="username" className="text-white">
            Username
          </Label>
          <div className="relative">
            <Input
              id="username"
              type="text"
              value={value}
              onChange={e => onChange(e.target.value)}
              placeholder="npr. marko_petrovic"
              className="pr-10 bg-white/10 border-white/30 text-white placeholder:text-white/50 focus:border-white/50 focus:ring-white/20"
              minLength={3}
              maxLength={30}
            />
            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
              {isChecking && (
                <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
              )}
              {!isChecking && isAvailable === true && (
                <CheckCircle className="h-4 w-4 text-green-500" />
              )}
              {!isChecking && isAvailable === false && (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
            </div>
          </div>
          {error && <p className="text-sm text-red-300 mt-1">{error}</p>}
          {isAvailable === true && (
            <p className="text-sm text-green-300 mt-1">Username je dostupan!</p>
          )}
        </div>

        <div className="text-sm text-white/60">
          <p>Username mora:</p>
          <ul className="list-disc list-inside mt-1 space-y-1">
            <li>Imati najmanje 3 karaktera</li>
            <li>Sadržavati samo slova, brojeve i _</li>
            <li>Biti jedinstven na platformi</li>
          </ul>
        </div>
      </div>

      <Button
        onClick={handleNext}
        disabled={!isAvailable || value.length < 3}
        className="w-full bg-white text-instagram-purple hover:bg-white/90 font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
      >
        Dalje
      </Button>
    </div>
  );
}
