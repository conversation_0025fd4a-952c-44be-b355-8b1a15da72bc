'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import Footer from '@/components/footer';

export default function BrandPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#E02F75] via-[#6700A3] to-[#050C38] relative overflow-hidden">
      {/* Header */}
      <header className="relative z-50 p-6">
        <div className="container mx-auto flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center border border-white/30">
              <span className="text-white font-bold text-lg">🔗</span>
            </div>
            <span className="text-xl font-bold text-white">INFLUEXUS</span>
          </Link>
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              className="text-white hover:bg-white/10"
              asChild
            >
              <Link href="/prijava">Prijava</Link>
            </Button>
            <Button
              className="bg-white text-purple-700 hover:bg-white/90"
              asChild
            >
              <Link href="/registracija">Registracija</Link>
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 py-20 px-4">
        <div className="container mx-auto max-w-4xl">
          {/* Page Title */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Za
              <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
                {' '}
                brendove
              </span>
            </h1>
            <p className="text-xl text-white/80 max-w-2xl mx-auto">
              Dosegnite svoju ciljnu publiku kroz autentične influencer kampanje
              sa garantovanim rezultatima
            </p>
          </div>

          {/* Hero Section */}
          <section className="mb-20">
            <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-12 border border-white/20 text-center">
              <div className="w-24 h-24 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-3xl flex items-center justify-center mx-auto mb-8">
                <span className="text-4xl">🚀</span>
              </div>
              <h2 className="text-3xl font-bold text-white mb-6">
                Povećajte prodaju kroz influencer marketing
              </h2>
              <p className="text-xl text-white/80 max-w-3xl mx-auto">
                INFLUEXUS vam omogućava da pronađete idealne influencere za vaš
                brend. Bez rizika, bez skrivenih troškova - plaćate samo
                rezultate koje vidite.
              </p>
            </div>
          </section>

          {/* Kako funkcioniše proces */}
          <section className="mb-20">
            <h2 className="text-3xl font-bold text-white mb-12 text-center">
              Vaš put do uspješne kampanje
            </h2>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="bg-white/10 backdrop-blur-sm border-white/20 text-white">
                <CardHeader>
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-xl flex items-center justify-center mb-4">
                    <span className="text-2xl">📝</span>
                  </div>
                  <CardTitle>1. Kreirajte kampanju</CardTitle>
                  <CardDescription className="text-white/70">
                    Opišite vaš proizvod, ciljnu grupu i željene rezultate
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="text-sm text-white/80 space-y-2">
                    <li>• Postavite budžet i timeline</li>
                    <li>• Definirajte tip sadržaja</li>
                    <li>• Opišite vašu ciljnu publiku</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-white/10 backdrop-blur-sm border-white/20 text-white">
                <CardHeader>
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-500 rounded-xl flex items-center justify-center mb-4">
                    <span className="text-2xl">🔍</span>
                  </div>
                  <CardTitle>2. Primite prijave</CardTitle>
                  <CardDescription className="text-white/70">
                    Influenceri se prijavljuju sa svojim kreativnim idejama
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="text-sm text-white/80 space-y-2">
                    <li>• Pregledajte profile i statistike</li>
                    <li>• Analizirajte predložene koncepte</li>
                    <li>• Uporedite cijene i timeline</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-white/10 backdrop-blur-sm border-white/20 text-white">
                <CardHeader>
                  <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-emerald-500 rounded-xl flex items-center justify-center mb-4">
                    <span className="text-2xl">✅</span>
                  </div>
                  <CardTitle>3. Odaberite partnera</CardTitle>
                  <CardDescription className="text-white/70">
                    Birajte influencera koji najbolje odgovara vašem brendu
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="text-sm text-white/80 space-y-2">
                    <li>• Proverite engagement rate</li>
                    <li>• Analizirajte demografiju publike</li>
                    <li>• Procenite brand fit</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-white/10 backdrop-blur-sm border-white/20 text-white">
                <CardHeader>
                  <div className="w-12 h-12 bg-gradient-to-r from-orange-400 to-red-500 rounded-xl flex items-center justify-center mb-4">
                    <span className="text-2xl">🤝</span>
                  </div>
                  <CardTitle>4. Finalizirajte detalje</CardTitle>
                  <CardDescription className="text-white/70">
                    Dogovorite finalne specifikacije i rokove
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="text-sm text-white/80 space-y-2">
                    <li>• Definirajte brand guidelines</li>
                    <li>• Postavite ključne poruke</li>
                    <li>• Dogovorite deliverables</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-white/10 backdrop-blur-sm border-white/20 text-white">
                <CardHeader>
                  <div className="w-12 h-12 bg-gradient-to-r from-indigo-400 to-purple-500 rounded-xl flex items-center justify-center mb-4">
                    <span className="text-2xl">📊</span>
                  </div>
                  <CardTitle>5. Pratite napredak</CardTitle>
                  <CardDescription className="text-white/70">
                    Real-time praćenje kreiranja i objave sadržaja
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="text-sm text-white/80 space-y-2">
                    <li>• Live notifikacije o napretku</li>
                    <li>• Preview sadržaja pre objave</li>
                    <li>• Direktna komunikacija</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-white/10 backdrop-blur-sm border-white/20 text-white">
                <CardHeader>
                  <div className="w-12 h-12 bg-gradient-to-r from-teal-400 to-cyan-500 rounded-xl flex items-center justify-center mb-4">
                    <span className="text-2xl">📈</span>
                  </div>
                  <CardTitle>6. Analizirajte rezultate</CardTitle>
                  <CardDescription className="text-white/70">
                    Detaljni izvještaji o performansama kampanje
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="text-sm text-white/80 space-y-2">
                    <li>• Reach i impression metrici</li>
                    <li>• Engagement rate analiza</li>
                    <li>• ROI kalkulacije</li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </section>

          {/* Prednosti za brendove */}
          <section className="mb-20">
            <h2 className="text-3xl font-bold text-white mb-12 text-center">
              Zašto brendovi biraju
              <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
                {' '}
                INFLUEXUS?
              </span>
            </h2>

            <div className="grid md:grid-cols-2 gap-8">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-2xl flex items-center justify-center mb-6">
                  <span className="text-3xl">🎯</span>
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">
                  Precizno ciljanje
                </h3>
                <p className="text-white/80 text-lg">
                  Naši algoritmi analiziraju publiku influencera i povezuju vas
                  sa onima čija publika se poklapa sa vašom ciljnom grupom.
                </p>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                <div className="w-16 h-16 bg-gradient-to-r from-green-400 to-emerald-500 rounded-2xl flex items-center justify-center mb-6">
                  <span className="text-3xl">💰</span>
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">
                  Kontrola budžeta
                </h3>
                <p className="text-white/80 text-lg">
                  Postavite budžet koji vam odgovara. Plaćate tek kada je posao
                  završen i kada ste zadovoljni rezultatima.
                </p>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-400 to-pink-500 rounded-2xl flex items-center justify-center mb-6">
                  <span className="text-3xl">⚡</span>
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">
                  Brži time-to-market
                </h3>
                <p className="text-white/80 text-lg">
                  Standardizovani procesi i pre-vetted influenceri omogućavaju
                  vam da pokrenete kampanje za nekoliko dana, ne meseci.
                </p>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                <div className="w-16 h-16 bg-gradient-to-r from-orange-400 to-red-500 rounded-2xl flex items-center justify-center mb-6">
                  <span className="text-3xl">📊</span>
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">
                  Merljivi rezultati
                </h3>
                <p className="text-white/80 text-lg">
                  Sve metrici su transparentni i verifikovani. Vidite tačno
                  koliko ljudi je dosegnuto i kakav je bio engagement.
                </p>
              </div>
            </div>
          </section>

          {/* Tipovi kampanja */}
          <section className="mb-20">
            <h2 className="text-3xl font-bold text-white mb-12 text-center">
              Tipovi kampanja koje podržavamo
            </h2>

            <div className="grid md:grid-cols-3 gap-6">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 text-center">
                <div className="w-12 h-12 bg-gradient-to-r from-pink-400 to-purple-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">📱</span>
                </div>
                <h3 className="text-lg font-bold text-white mb-2">
                  Sponsored posts
                </h3>
                <p className="text-white/70 text-sm">
                  Feed objave sa vašim proizvodom ili uslugom
                </p>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 text-center">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">📹</span>
                </div>
                <h3 className="text-lg font-bold text-white mb-2">
                  Story campaigns
                </h3>
                <p className="text-white/70 text-sm">
                  Instagram/TikTok stories sa autentičnim preporakama
                </p>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 text-center">
                <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-teal-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🎥</span>
                </div>
                <h3 className="text-lg font-bold text-white mb-2">
                  Video reviews
                </h3>
                <p className="text-white/70 text-sm">
                  Detaljni video pregledi vaših proizvoda
                </p>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 text-center">
                <div className="w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🎁</span>
                </div>
                <h3 className="text-lg font-bold text-white mb-2">
                  Product launches
                </h3>
                <p className="text-white/70 text-sm">
                  Koordinirane kampanje za lansiranje proizvoda
                </p>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 text-center">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-indigo-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">📸</span>
                </div>
                <h3 className="text-lg font-bold text-white mb-2">
                  UGC kreiranje
                </h3>
                <p className="text-white/70 text-sm">
                  User-generated content za vaše marketing potrebe
                </p>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 text-center">
                <div className="w-12 h-12 bg-gradient-to-r from-red-400 to-pink-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🎪</span>
                </div>
                <h3 className="text-lg font-bold text-white mb-2">
                  Event promotion
                </h3>
                <p className="text-white/70 text-sm">
                  Promocija događaja kroz influencer mreže
                </p>
              </div>
            </div>
          </section>

          {/* Pricing */}
          <section className="mb-20">
            <h2 className="text-3xl font-bold text-white mb-12 text-center">
              Transparentno cijenje
            </h2>

            <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-8 border border-white/20 text-center">
              <div className="max-w-2xl mx-auto">
                <div className="w-20 h-20 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <span className="text-3xl">💳</span>
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">
                  Samo 5% platformske naknade
                </h3>
                <p className="text-white/80 text-lg mb-6">
                  Naplaćujemo samo 5% od ukupne vrednosti kampanje. Bez setup
                  naknada, bez mesečnih pretplata.
                </p>
                <div className="bg-white/5 rounded-xl p-6 border border-white/10">
                  <p className="text-white/70 text-sm mb-2">Primer:</p>
                  <p className="text-2xl font-bold text-white">
                    Budžet kampanje <span className="text-blue-400">1000€</span>{' '}
                    → Ukupno plaćate{' '}
                    <span className="text-blue-400">1050€</span>
                  </p>
                  <p className="text-white/60 text-sm mt-2">
                    Uključuje sve - platformu, zaštićeno plaćanje, podršku
                  </p>
                </div>
              </div>
            </div>
          </section>

          {/* CTA Section */}
          <section className="text-center">
            <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-12 border border-white/20">
              <h2 className="text-3xl font-bold text-white mb-6">
                Spremni za vašu prvu kampanju?
              </h2>
              <p className="text-xl text-white/80 mb-8 max-w-2xl mx-auto">
                Pridružite se brendovima koji već koriste INFLUEXUS za uspešne
                influencer kampanje
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Button
                  className="bg-gradient-to-r from-blue-500 to-cyan-600 hover:from-blue-600 hover:to-cyan-700 text-white font-bold text-lg px-8 py-4 rounded-xl shadow-xl"
                  asChild
                >
                  <Link href="/registracija">Pokrenite kampanju</Link>
                </Button>
                <Button
                  variant="outline"
                  className="border-white/30 text-white hover:bg-white/10 font-bold text-lg px-8 py-4 rounded-xl"
                  asChild
                >
                  <Link href="/kako-funkcionise">Saznajte o sigurnosti</Link>
                </Button>
              </div>
            </div>
          </section>
        </div>
      </main>

      <Footer />
    </div>
  );
}
