# 📋 TASKOVI I PRIORITETI - INFLUENCER MARKETING PLATFORMA

**Kreiran**: 10.08.2025
**Status**: AKTIVAN
**Izvor**: Reorganizacija otvoreni-zadaci.md (935 linija)

---

## 🔥 **TRENUTNO RADIMO NA**

### ✅ **PROFILE IMAGE UPLOAD SA KOMPRESIJOM - ZAVRŠENO** 📸
**Prioritet**: VISOK
**Procjena**: 6-8 sati
**Status**: ZAVRŠENO (16.08.2025)

**Cilj**: Implementirati upload profile slike sa automatskom kompresijom u WebP format

**Implementirane funkcionalnosti**:
- [x] **Client-side kompresija** - kompresija prije upload-a na Supabase
- [x] **Multiple verzije slika** - 4 različite dimenzije za različite use case-ove:
  - **Navbar**: 40x40px (mala ikonica)
  - **Card**: 200x200px (marketplace kartice)
  - **Profile**: 400x400px (profile stranica)
  - **Preview**: 600x600px (business preview sekcija)
- [x] **WebP format** - optimizacija za storage i brzinu
- [x] **Drag & Drop upload** - moderna UX
- [x] **Progress indicator** - tokom kompresije i upload-a
- [x] **Fallback logika** - ako kompresija ne uspije
- [x] **Database schema** - dodane kolone za multiple avatar URL-ove
- [x] **Storage bucket** - kreiran avatars bucket sa folder strukturom
- [x] **React komponenta** - AvatarUpload komponenta sa preview funkcionalnosti
- [x] **Integration** - dodano na influencer profile stranicu

**Tehnički detalji**:
- Koristi `browser-image-compression` biblioteku za kompresiju
- Automatski konvertuje u WebP format
- Organizovana folder struktura: `{userId}/navbar/`, `{userId}/card/`, etc.
- Backward compatibility sa postojećim avatar_url poljem
- Kompresija ratio prikazuje koliko je prostora uštjeđeno
- RLS policies omogućavaju sigurno upload-ovanje
- Next.js Image konfiguracija za Supabase hostname

**Testiranje rezultati**:
- ✅ Upload i kompresija rade savršeno
- ✅ Sve 4 verzije se kreiraju i čuvaju
- ✅ InfluencerCard koristi card verziju (200x200)
- ✅ Navbar koristi navbar verziju (40x40)
- ✅ Storage policies rade ispravno
- ✅ Database se ažurira sa svim URL-ovima

**Poboljšanja kvaliteta (16.08.2025)**:
- ✅ **Card verzija povećana** sa 200x200 na 300x300px (quality 0.85→0.92)
- ✅ **Veličina fajla** povećana sa 7.92KB na 17.54KB za bolje kvalitet
- ✅ **Marketplace kartice** sada imaju značajno bolji kvalitet slika
- ✅ **Preview sekcija** na profile stranici radi ispravno

**Storage analiza (ažurirano 16.08.2025)**:
- **Navbar**: 1.54 KB (40x40px @ 0.95 quality) - optimalno
- **Card**: 92.47 KB (800x800px @ 0.95 quality) - vrhunski kvalitet
- **Profile**: 92.47 KB (800x800px @ 0.95 quality) - vrhunski kvalitet
- **Preview**: 92.47 KB (800x800px @ 0.95 quality) - vrhunski kvalitet
- **Ukupno**: ~280KB za sve verzije (vs 574KB original)
- **Kompresija**: 51% uštede prostora (vs 84% ranije)

**UI poboljšanja**:
- ✅ **Preview sekcija** - riješen problem sa praznim avatar_url
- ✅ **Square profile image** - zamijenjen kružni Avatar sa square Image komponentom
- ✅ **Full-width mobile layout** - slika preko cijele širine screen-a (1:1 aspect ratio)
- ✅ **Centered desktop layout** - slika centrirana (320x320px) sa informacijama ispod
- ✅ **Responsive design** - savršeno radi na svim uređajima
- ✅ **High-quality images** - koristi profile_avatar_url (800x800px @ 0.95 quality)
- ✅ **Optimized layout** - informacije centrirane na desktop, prirodno na mobile

---

## 🎉 **NEDAVNO RIJEŠENI PROBLEMI**

### ✅ **JOB COMPLETION NOTIFICATION ERROR - RIJEŠENO** (16.08.2025)
- **Problem**: Error creating notification: Error: supabaseKey is required
- **Uzrok**: createServerClient se pozivao u browser environment-u
- **Riješeno**: Refaktorisan kod da koristi RPC funkcije umjesto server client-a
- **Rezultat**: Job completion notifikacije sada rade ispravno

### ✅ **REAL-TIME CHAT MESSAGES - RIJEŠENO** (10.08.2025)
- **Problem**: Poruke se nisu prikazivale u real-time između korisnika
- **Uzrok**: chat_messages tabela nije bila u Supabase Realtime publikaciji
- **Riješeno**: Dodana tabela u publikaciju + poboljšan event handling
- **Rezultat**: Chat sada radi potpuno u real-time

### ✅ **BUSINESS ONBOARDING - ZAVRŠENO** (06.08.2025)
- Kreiran potpuni business onboarding flow (7 koraka)
- Popravljena CityStep greška + ažurirani tekstovi
- Business profil stranica kompletno ažurirana

### ✅ **AUTHENTICATION & PROFILE SYSTEM - RIJEŠENO** (03.08.2025)
- Database types problem riješen (339→1099 linija)
- Authentication flow potpuno funkcionalan
- Influencer onboarding sistem implementiran (9 koraka)
- Profile stranice ažurirane sa ispravnim podacima

---

## 🚨 **HITNI ZADACI - POTREBNO ODMAH**

### **1. PASSWORD RESET FLOW - HITNO** �
**Prioritet**: HITNO
**Procjena**: 3-4 sata
**Status**: POTREBNO IMPLEMENTIRATI

**Problem**: Korisnici nemaju mogućnost resetovanja lozinke

**Potrebno**:
- [ ] Implementirati forgot password komponentu
- [ ] Kreirati reset password stranicu
- [ ] Dodati email template za password reset
- [ ] Testirati kompletan flow
- [ ] Dodati proper error handling

**Fajlovi**: `src/app/auth/`, `src/components/auth/`

---

### **2. REGISTRACIJA POSTOJEĆIH KORISNIKA - HITNO** 👥
**Prioritet**: HITNO
**Procjena**: 2-3 sata
**Status**: POTREBNO PROVJERITI

**Problem**: Šta se dešava kada se korisnik pokušava registrovati a već ima account

**Potrebno**:
- [ ] Provjeriti trenutno ponašanje registracije
- [ ] Implementirati proper error handling za postojeće email-ove
- [ ] Dodati redirect na login sa porukom
- [ ] Testirati edge case-ove
- [ ] Dokumentovati ponašanje

**Fajlovi**: `src/app/auth/register/`, `src/lib/auth.ts`

---

### **3. DATABASE CLEANUP - HITNO** 🗑️
**Prioritet**: HITNO  
**Procjena**: 2-3 sata  
**Status**: POTREBNO RIJEŠITI

**Problem**: Duplikati i legacy polja u bazi podataka

**Potrebno**:
- [ ] **Ukloniti duplikate iz `influencers` tabele:**
  - `age` → koristiti samo `profiles.age`
  - `gender` → koristiti samo `profiles.gender`
  - Stare handle kolone → koristiti samo `influencer_platforms`
- [ ] **Standardizovati location polja:**
  - Ukloniti `profiles.location` → koristiti `city` + `country`
- [ ] **Očistiti `businesses` tabelu:**
  - `industry` → koristiti samo `business_target_categories`
  - `company_size`, `budget_range` → ukloniti ako se ne koriste
- [ ] **Ažurirati sve funkcije** da koriste nova polja

---

## 🔥 **VISOK PRIORITET**

### **4. FINAL SECURITY AUDIT - VISOK** 🔒
**Prioritet**: VISOK  
**Procjena**: 4-5 sati  
**Status**: POTREBNO RIJEŠITI

**Database security**:
- [ ] **RLS policies audit** - provjeriti sve tabele
- [ ] **Function permissions** - provjeriti sve database funkcije
- [ ] **Data validation** - server-side validation za sve inputs
- [ ] **SQL injection protection** - parameterized queries
- [ ] **Rate limiting** - dodati rate limiting na API endpoints

**Application security**:
- [ ] **Authentication flows** - provjeriti sve auth scenarije
- [ ] **Authorization checks** - user type validations
- [ ] **Input sanitization** - XSS protection
- [ ] **File upload security** - ako imamo file uploads
- [ ] **Environment variables** - provjeriti da nema exposed secrets

---

## 📝 **SREDNJI PRIORITET**

### **5. ESLINT CLEANUP - SREDNJI** 📝
**Prioritet**: SREDNJI  
**Procjena**: 2-3 sata  
**Status**: POTREBNO RIJEŠITI

**Preostale ESLint greške**:
- [ ] **Unused imports** - ukloniti nekorišćene importe kroz codebase
- [ ] **Unused variables** - ukloniti nekorišćene varijable
- [ ] **Remaining `any` types** - zamijeniti preostale `any` tipove
- [ ] **React hooks dependencies** - popraviti useEffect dependency warnings
- [ ] **Prettier formatting** - formatirati preostale fajlove

**Fajlovi sa najviše grešaka**:
- `src/lib/campaigns.ts` - 20+ unused imports/variables
- `src/lib/chat.ts` - 10+ `any` tipovi
- `src/lib/profiles.ts` - unused imports
- `src/app/profil/edit/page.tsx` - missing imports

---

### **6. MOBILE CHAT FULLSCREEN - SREDNJI** 📱
**Prioritet**: SREDNJI  
**Procjena**: 1-2 sata  
**Status**: POTREBNO RIJEŠITI

**Problem**: Chat na mobitelu mora biti fullscreen kada se otvori

**Potrebno**:
- [ ] Analizirati trenutni mobile chat layout
- [ ] Implementirati fullscreen mode za mobile chat
- [ ] Možda sakriti header/navigation na mobile u chat view
- [ ] Dodati back button koji je uvijek vidljiv
- [ ] Optimizovati chat height za mobile (100vh)
- [ ] Testirati na različitim mobile device-ima

---

### **7. USER FLOW ANALIZA I OPTIMIZACIJA - SREDNJI** 🔍
**Prioritet**: SREDNJI  
**Procjena**: 6-8 sati  
**Status**: POTREBNO RIJEŠITI

**Cilj**: Proći cijeli flow oba korisnika od registracije i vidjeti koja polja su zaista potrebna

**Analiza potrebna**:
- [ ] **Registracija flow** - koja polja su obavezna vs opciona
- [ ] **Profile setup** - minimalni vs kompletni profil
- [ ] **Onboarding** - voditi korisnike kroz setup
- [ ] **Required fields validation** - šta je stvarno potrebno za funkcionalnost
- [ ] **Progressive disclosure** - pokazivati polja postupno

**Korisnici za testiranje**:
- Influencer flow: registracija → profil → aplikacija na kampanju → chat → job completion
- Business flow: registracija → profil → kreiranje kampanje → pregled aplikacija → job completion

---

### **8. NOTIFICATION SISTEM POBOLJŠANJA - SREDNJI** 🔔
**Prioritet**: SREDNJI  
**Procjena**: 3-4 sata  
**Status**: POTREBNO RIJEŠITI

**Trenutni problemi**:
- Notifikacije se ne označavaju kao pročitane
- Nema real-time notifikacija
- Nema email notifikacija za važne eventi

**Potrebno**:
- [ ] Mark as read funkcionalnost
- [ ] Real-time notifications sa Supabase
- [ ] Email notifications za kritične eventi
- [ ] Notification preferences u profilu

---

### **9. SEARCH I FILTERING POBOLJŠANJA - SREDNJI** 🔍
**Prioritet**: SREDNJI  
**Procjena**: 4-5 sati  
**Status**: POTREBNO RIJEŠITI

**Marketplace search**:
- [ ] **Full-text search** - bolja pretraga influencera
- [ ] **Advanced filters** - kombinovanje više filtera
- [ ] **Search suggestions** - autocomplete
- [ ] **Search history** - zapamćene pretrage
- [ ] **Saved searches** - mogućnost čuvanja filtera

---

## 🔮 **NIZAK PRIORITET - BUDUĆE POBOLJŠANJA**

### **10. OAUTH ROLE INTEGRATION - NIZAK** 🔐
**Prioritet**: NIZAK  
**Procjena**: 2-3 sata (istraživanje)  
**Status**: ISTRAŽIVANJE POTREBNO

**Napomena**: Provjeriti da li u OAuth možemo ubaciti rolu za premium korisnika

**Potrebno istražiti**:
- [ ] Analizirati Supabase OAuth provider options
- [ ] Provjeriti da li možemo dodati custom claims u OAuth token
- [ ] Istražiti kako implementirati role-based OAuth
- [ ] Dokumentovati mogućnosti za buduće premium features

---

### **11. PRICING MODEL I FREE USER FEATURES - NIZAK** 💰
**Prioritet**: NIZAK  
**Procjena**: 3-4 sata  
**Status**: BUDUĆE PLANIRANJE

**Potrebno definirati**:
- [ ] **Free tier limitations** - koliko kampanja/aplikacija mjesečno
- [ ] **Premium features** - šta dobijaju plaćajući korisnici
- [ ] **Pricing tiers** - Basic, Pro, Enterprise
- [ ] **Payment integration** - Stripe subscription setup
- [ ] **Feature gating** - blokiranje premium funkcija za free usere

**Predlog pricing modela**:
- **Free**: 3 kampanje mjesečno, osnovni chat, osnovni profil
- **Pro**: Unlimited kampanje, advanced analytics, priority support
- **Enterprise**: Custom features, API access, dedicated support

---

### **12. ANALYTICS DASHBOARD - NIZAK** 📊
**Prioritet**: NIZAK  
**Procjena**: 8-10 sati  
**Status**: BUDUĆE FUNKCIONALNOST

**Business analytics**:
- Campaign performance metrics
- ROI tracking
- Influencer performance comparison
- Budget utilization

**Influencer analytics**:
- Earnings overview
- Application success rate
- Rating trends
- Portfolio performance

---

## 🎯 **PRIORITETNI REDOSLIJED IMPLEMENTACIJE**

### **🚨 HITNO (1-2 dana)**:
1. **Password Reset Flow** (3-4 sata) - HITNO
2. **Registracija postojećih korisnika** (2-3 sata) - HITNO
3. **Database Cleanup** (2-3 sata) - HITNO

### **🔥 VISOK PRIORITET (1 dan)**:
4. **Final Security Audit** (4-5 sati)

### **📝 SREDNJI PRIORITET (1-2 sedmice)**:
5. **ESLint cleanup** (2-3 sata)
6. **Mobile chat fullscreen** (1-2 sata)
7. **User Flow Analiza** (6-8 sati)
8. **Notification sistem poboljšanja** (3-4 sata)
9. **Search i filtering poboljšanja** (4-5 sati)

### **🔮 NIZAK PRIORITET (buduće)**:
10. **OAuth role integration** (istraživanje)
11. **Pricing model** (planiranje)
12. **Analytics dashboard** (buduća funkcionalnost)

---

## 📊 **PROCJENA VREMENA**

**HITNI ZADACI**: 7-10 sati
**VISOKI PRIORITET**: 4-5 sati
**SREDNJI PRIORITET**: 19-28 sati
**NIZAK PRIORITET**: 13-17 sati

**UKUPNO**: 43-60 sati za sve zadatke
**KRITIČNI PUT**: 11-15 sati za najvažnije bugove

---

*Poslednje ažuriranje: 16.08.2025 - Job completion notification riješen, dodani password reset i registracija taskovi*
