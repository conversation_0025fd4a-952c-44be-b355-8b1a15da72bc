'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Building2, ArrowLeft, ArrowRight } from 'lucide-react';

const businessNameSchema = z.object({
  businessName: z
    .string()
    .min(2, 'Naziv firme mora imati najmanje 2 karaktera')
    .max(100, 'Naziv firme može imati maksimalno 100 karaktera'),
});

type BusinessNameForm = z.infer<typeof businessNameSchema>;

interface BusinessNameStepProps {
  value: string;
  onNext: (businessName: string) => void;
  onBack: () => void;
}

export function BusinessNameStep({
  value,
  onNext,
  onBack,
}: BusinessNameStepProps) {
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<BusinessNameForm>({
    resolver: zodResolver(businessNameSchema),
    defaultValues: {
      businessName: value,
    },
  });

  const onSubmit = async (data: BusinessNameForm) => {
    setIsLoading(true);
    try {
      onNext(data.businessName);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto">
      <div className="space-y-6">
        <div className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-white/20">
            <Building2 className="h-6 w-6 text-white" />
          </div>
          <h2 className="text-2xl font-bold text-white mb-2">
            Naziv vašeg brenda
          </h2>
          <p className="text-white/70">Unesite naziv vaše firme ili brenda</p>
        </div>
        <div>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="businessName" className="text-white">
                Naziv firme/brenda
              </Label>
              <Input
                id="businessName"
                {...register('businessName')}
                placeholder="npr. Coca-Cola, Nike, Apple..."
                className={`bg-white/10 border-white/30 text-white placeholder:text-white/50 focus:border-white/50 focus:ring-white/20 ${errors.businessName ? 'border-red-400' : ''}`}
              />
              {errors.businessName && (
                <p className="text-sm text-red-300">
                  {errors.businessName.message}
                </p>
              )}
            </div>

            <div className="flex justify-between pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onBack}
                className="flex items-center space-x-2 border-white/30 text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm"
              >
                <ArrowLeft className="h-4 w-4" />
                <span>Nazad</span>
              </Button>

              <Button
                type="submit"
                disabled={isLoading}
                className="flex items-center space-x-2 bg-white text-instagram-purple hover:bg-white/90 font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <span>Dalje</span>
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
