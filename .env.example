# =================================
# INFLUENCER PLATFORM - ENVIRONMENT VARIABLES TEMPLATE
# =================================
# Copy this file to .env.local and fill in your actual values
# NEVER commit .env.local or any file containing real secrets to version control

# =================================
# SUPABASE CONFIGURATION
# =================================
# Get these values from your Supabase project dashboard
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.your-service-role-key-here

# =================================
# MCP (SUPABASE CLI) CONFIGURATION
# =================================
# Access token for Supabase CLI and management operations
SUPABASE_ACCESS_TOKEN=sbp_your-access-token-here

# =================================
# WEBHOOK SECRETS
# =================================
# Generate a secure random string for Supabase webhook verification
# You can use: openssl rand -hex 32
SUPABASE_WEBHOOK_SECRET=your-secure-webhook-secret-here

# =================================
# STRIPE CONFIGURATION
# =================================
# Get these from your Stripe dashboard (https://dashboard.stripe.com/apikeys)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your-publishable-key-here
STRIPE_SECRET_KEY=sk_test_your-secret-key-here
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret-here

# =================================
# APPLICATION CONFIGURATION
# =================================
# Base URL for your application
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Environment (development, production, staging)
NODE_ENV=development

# =================================
# EMAIL SERVICE CONFIGURATION (OPTIONAL)
# =================================
# If using a custom email service like SendGrid, Brevo, etc.
# EMAIL_SERVICE_API_KEY=your-email-service-api-key-here
# EMAIL_FROM_ADDRESS=<EMAIL>
# EMAIL_FROM_NAME="Influexus Platform"

# =================================
# DATABASE CONFIGURATION (OPTIONAL)
# =================================
# If using direct database connections (usually not needed with Supabase)
# DATABASE_URL=postgresql://username:password@host:port/database

# =================================
# MONITORING & ANALYTICS (OPTIONAL)
# =================================
# Google Analytics
# NEXT_PUBLIC_GA_TRACKING_ID=GA_MEASUREMENT_ID
# 
# Sentry (Error tracking)
# SENTRY_DSN=https://your-sentry-dsn-here
# 
# LogRocket (Session replay)
# NEXT_PUBLIC_LOGROCKET_APP_ID=your-logrocket-app-id

# =================================
# SECURITY CONFIGURATION (OPTIONAL)
# =================================
# JWT Secret (if using custom JWT implementation)
# JWT_SECRET=your-jwt-secret-here
# 
# Session Secret
# SESSION_SECRET=your-session-secret-here
# 
# CORS Allowed Origins (comma-separated)
# CORS_ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com

# =================================
# THIRD-PARTY INTEGRATIONS (OPTIONAL)
# =================================
# Social Media API Keys (for influencer verification)
# INSTAGRAM_CLIENT_ID=your-instagram-client-id
# INSTAGRAM_CLIENT_SECRET=your-instagram-client-secret
# 
# YouTube API
# YOUTUBE_API_KEY=your-youtube-api-key
# 
# TikTok API
# TIKTOK_CLIENT_KEY=your-tiktok-client-key
# TIKTOK_CLIENT_SECRET=your-tiktok-client-secret

# =================================
# FILE STORAGE CONFIGURATION (OPTIONAL)
# =================================
# If using external file storage (Supabase Storage is usually sufficient)
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_S3_BUCKET_NAME=your-s3-bucket
# AWS_S3_REGION=us-east-1

# =================================
# REDIS CONFIGURATION (OPTIONAL)
# =================================
# For production rate limiting and caching
# REDIS_URL=redis://localhost:6379
# REDIS_PASSWORD=your-redis-password

# =================================
# DEVELOPMENT ONLY
# =================================
# Set to true to enable debug logging
# DEBUG=true
# 
# Set to true to disable email sending in development
# DISABLE_EMAILS=true