'use client';

import { useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Chat } from '@/components/chat/Chat';

export default function ChatPage() {
  const searchParams = useSearchParams();
  const roomId = searchParams.get('room');
  const [isInChatRoom, setIsInChatRoom] = useState(!!roomId);

  return (
    <DashboardLayout hideHeader={false}>
      <div
        className={`${isInChatRoom ? 'fixed inset-0 z-40 pt-14 pb-16 md:relative md:pt-0 md:pb-0 md:h-full' : 'fixed inset-0 z-30 pt-14 pb-16 md:relative md:pt-0 md:pb-0 md:h-full'}`}
      >
        <Chat
          initialRoomId={roomId || undefined}
          onRoomStateChange={setIsInChatRoom}
        />
      </div>
    </DashboardLayout>
  );
}
