# =================================
# STAGING ENVIRONMENT CONFIGURATION
# =================================
# This file contains staging-specific environment variables
# Staging is used for testing before production deployment

# =================================
# ENVIRONMENT
# =================================
NODE_ENV=staging
DEBUG=true
DISABLE_EMAILS=false

# =================================
# APPLICATION CONFIGURATION
# =================================
# Replace with your staging domain
NEXT_PUBLIC_APP_URL=https://staging.yourdomain.com

# =================================
# SECURITY SETTINGS (MODERATE FOR STAGING)
# =================================
# CORS policy for staging
CORS_ALLOWED_ORIGINS=https://staging.yourdomain.com,http://localhost:3000

# Session settings (secure but not as strict as production)
SESSION_SECURE=true
SESSION_SAME_SITE=lax

# =================================
# LOGGING CONFIGURATION
# =================================
# Detailed logging for staging debugging
LOG_LEVEL=debug

# Enable request logging for staging analysis
LOG_REQUESTS=true

# =================================
# PERFORMANCE SETTINGS
# =================================
# Enable caching but allow cache clearing
DISABLE_CACHE=false
ENABLE_CACHE_CONTROL=true

# Enable some development tools for staging debugging
ENABLE_DEVTOOLS=true

# =================================
# TESTING & QA
# =================================
# Enable test user accounts
ENABLE_TEST_USERS=true

# Mock some external services for testing
# MOCK_PAYMENT_PROCESSING=true

# Enable feature flags testing
ENABLE_FEATURE_FLAGS=true

# =================================
# MONITORING & ANALYTICS
# =================================
# Error tracking for staging
# SENTRY_DSN=https://your-staging-sentry-dsn-here

# Staging-specific analytics
# NEXT_PUBLIC_GA_TRACKING_ID=G-STAGING-ID

# =================================
# API SETTINGS
# =================================
# Moderate rate limiting for staging
ENABLE_RATE_LIMITING=true
STRICT_RATE_LIMITING=false

# Longer API timeouts for staging debugging
API_TIMEOUT=60000

# =================================
# DATABASE SETTINGS
# =================================
# Enable database query logging for debugging
LOG_DATABASE_QUERIES=true

# Staging database pool settings
# DB_POOL_MIN=5
# DB_POOL_MAX=20

# =================================
# EMAIL SETTINGS
# =================================
# Staging email settings (often with test prefix)
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME="Influexus Platform (Staging)"

# =================================
# STRIPE SETTINGS (STAGING)
# =================================
# Use Stripe test mode in staging
# Stripe test keys should be used here
# NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
# STRIPE_SECRET_KEY=sk_test_...

# =================================
# FEATURE TOGGLES
# =================================
# Test new features in staging
# ENABLE_BETA_FEATURES=true
# ENABLE_EXPERIMENTAL_UI=true
# ENABLE_NEW_PAYMENT_FLOW=true

# =================================
# BACKUP & TESTING
# =================================
# Enable test data generation
ENABLE_TEST_DATA_GENERATION=true

# Reduced data retention for staging
USER_DATA_RETENTION_DAYS=90
SESSION_DATA_RETENTION_DAYS=7

# =================================
# COMPLIANCE (RELAXED FOR TESTING)
# =================================
# GDPR compliance testing
ENABLE_GDPR_COMPLIANCE=true
COOKIE_CONSENT_REQUIRED=false  # Relaxed for testing

# =================================
# PERFORMANCE TESTING
# =================================
# Enable performance monitoring
ENABLE_PERFORMANCE_MONITORING=true

# Load testing settings
# ENABLE_LOAD_TEST_ENDPOINTS=true
# MAX_CONCURRENT_USERS=100