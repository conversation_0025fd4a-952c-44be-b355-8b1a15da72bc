// Test script za influencer R<PERSON> funkcije
const { createClient } = require('@supabase/supabase-js');

// Kreiraj Supabase client (koristi svoje credentials)
const supabaseUrl = 'YOUR_SUPABASE_URL';
const supabaseKey = 'YOUR_SUPABASE_ANON_KEY'; 
const supabase = createClient(supabaseUrl, supabaseKey);

async function testInfluencerRPC() {
  console.log('🚀 Testiranje influencer RPC funkcija...\n');

  // Test mock influencer ID (zameniti sa pravim ID-om)
  const mockInfluencerId = 'test-uuid-here';

  try {
    // Test 1: Applications RPC
    console.log('📝 Testiranje get_influencer_applications...');
    const { data: applications, error: applicationsError } = await supabase.rpc('get_influencer_applications', {
      p_influencer_id: mockInfluencerId,
      p_limit: 10,
      p_offset: 0,
      p_status: null
    });

    if (applicationsError) {
      console.error('❌ Applications RPC error:', applicationsError);
    } else {
      console.log(`✅ Applications RPC: ${applications?.length || 0} records found`);
      if (applications && applications.length > 0) {
        console.log('   Sample record keys:', Object.keys(applications[0]));
      }
    }

    // Test 2: Offers RPC
    console.log('\n💌 Testiranje get_influencer_offers...');
    const { data: offers, error: offersError } = await supabase.rpc('get_influencer_offers', {
      p_influencer_id: mockInfluencerId,
      p_limit: 10,
      p_offset: 0,
      p_status: null
    });

    if (offersError) {
      console.error('❌ Offers RPC error:', offersError);
    } else {
      console.log(`✅ Offers RPC: ${offers?.length || 0} records found`);
      if (offers && offers.length > 0) {
        console.log('   Sample record keys:', Object.keys(offers[0]));
      }
    }

    // Test 3: Applications with status filter
    console.log('\n🔍 Testiranje get_influencer_applications sa filterom...');
    const { data: pendingApps, error: pendingError } = await supabase.rpc('get_influencer_applications', {
      p_influencer_id: mockInfluencerId,
      p_limit: 10,
      p_offset: 0,
      p_status: 'pending'
    });

    if (pendingError) {
      console.error('❌ Pending applications error:', pendingError);
    } else {
      console.log(`✅ Pending applications: ${pendingApps?.length || 0} records found`);
    }

  } catch (error) {
    console.error('💥 Test failed:', error);
  }

  console.log('\n📊 Test završen!');
}

// Pokreni test ako je fajl pokrenut direktno
if (require.main === module) {
  testInfluencerRPC();
}

module.exports = { testInfluencerRPC };