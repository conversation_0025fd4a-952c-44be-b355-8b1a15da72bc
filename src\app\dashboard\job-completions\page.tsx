'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { getCurrentUser } from '@/lib/profiles';
import { Loader2 } from 'lucide-react';

export default function JobCompletionsRedirectPage() {
  const router = useRouter();

  useEffect(() => {
    const redirectUser = async () => {
      try {
        const { data: user, error } = await getCurrentUser();
        if (error || !user) {
          router.push('/prijava');
          return;
        }

        // Redirect based on user type
        if (user.user_type === 'influencer') {
          router.push('/dashboard/influencer/zavrseni-poslovi');
        } else {
          router.push('/dashboard/biznis/zavrseni-poslovi');
        }
      } catch (error) {
        router.push('/prijava');
      }
    };

    redirectUser();
  }, [router]);

  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    </div>
  );
}
