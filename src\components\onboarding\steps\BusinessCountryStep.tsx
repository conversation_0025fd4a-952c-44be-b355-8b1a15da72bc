'use client';

import { Button } from '@/components/ui/button';
import { BusinessDropdown } from '@/components/ui/business-dropdown';

interface BusinessCountryStepProps {
  value: string;
  onChange: (value: string) => void;
  onNext: () => void;
  onBack: () => void;
  title?: string;
  description?: string;
}

const balkanCountries = [
  { value: 'Srbija', label: 'Srbija' },
  { value: 'Bosna i Hercegovina', label: 'Bosna i Hercegovina' },
  { value: 'Hrvatska', label: 'Hrvatska' },
  { value: 'Crna Gora', label: 'Crna Gora' },
  { value: 'Severna Makedonija', label: 'Severna Makedonija' },
  { value: 'Slovenija', label: 'Slovenija' },
  { value: 'Albanija', label: 'Albanija' },
  { value: 'Bugarska', label: 'Bugarska' },
  { value: 'Rumunija', label: '<PERSON><PERSON><PERSON><PERSON>' },
  { value: '<PERSON><PERSON><PERSON><PERSON>', label: '<PERSON><PERSON><PERSON><PERSON>' },
];

export function BusinessCountryStep({
  value,
  onChange,
  onNext,
  onBack,
  title = 'Iz koje ste države?',
  description = 'Ova informacija pomaže influencerima da pronađu lokalne brendove',
}: BusinessCountryStepProps) {
  const handleNext = () => {
    if (value) {
      onNext();
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-2">{title}</h2>
        <p className="text-white/70">{description}</p>
      </div>

      <div className="space-y-4">
        <BusinessDropdown
          label="Država"
          placeholder="Izaberite državu"
          value={value}
          onValueChange={onChange}
          options={balkanCountries}
        />

        <div className="text-sm text-white/60">
          <p>Trenutno podržavamo brendove iz Balkanskih zemalja.</p>
        </div>
      </div>

      <div className="flex gap-3">
        <Button
          variant="outline"
          onClick={onBack}
          className="flex-1 border-white/30 text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm"
        >
          Nazad
        </Button>
        <Button
          onClick={handleNext}
          disabled={!value}
          className="flex-1 bg-white text-instagram-purple hover:bg-white/90 font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
        >
          Dalje
        </Button>
      </div>
    </div>
  );
}
