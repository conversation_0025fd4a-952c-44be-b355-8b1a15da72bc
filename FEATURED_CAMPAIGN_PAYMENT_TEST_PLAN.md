# Featured Campaign Payment System - Test Plan

**Datum**: 9. septembar 2025  
**Status**: ✅ **IMPLEMENTIRANO**

## 🎯 Kreiran kompletan Featured Campaign Payment sistem

### ✅ Implementirane komponente:

#### 1. **Edge Function** 
- `supabase/functions/create-featured-campaign-payment/index.ts`
- Kreira Stripe checkout sessione
- Validira campaign ownership i business permissions
- Kalkuliše fees (10% platform fee)

#### 2. **API Proxy Route**
- `src/app/api/stripe/create-featured-campaign-payment/route.ts` 
- Poziva Edge Function sa proper auth headers
- Error handling i logging

#### 3. **Webhook Handler**
- Ažuriran `src/app/api/stripe/webhook/route.ts`
- `handleFeaturedCampaignPayment()` funkcija koristi **payments** tabelu
- Idempotency protection
- Updates `campaigns.is_featured` i `campaigns.featured_until`

#### 4. **Database Schema**
- `migrations/add_featured_campaign_payments.sql`
- Dodana `payments.campaign_id` kolona
- <PERSON><PERSON>a `campaigns.featured_until` kolona
- Ažurirane RLS policies

#### 5. **Helper Functions**
- `src/lib/featured-campaigns.ts` sa novim funkcijama:
  - `createFeaturedCampaignPaymentSession()`
  - `isCampaignFeaturedPaid()`  
  - `getFeaturedCampaignPaymentInfo()`

#### 6. **Frontend Modal**
- Ažuriran `src/components/modals/PromoteCampaignModal.tsx`
- Koristi novi Edge Function umesto starih direktnih Stripe poziva
- Uklonjen `stripePromise` dependency

## 🔧 Tehnički detalji

### Payment Flow:
1. **Modal** → `createFeaturedCampaignPaymentSession()`
2. **API Route** → Edge Function proxy sa auth
3. **Edge Function** → Stripe checkout sesija
4. **Stripe** → redirect na checkout
5. **Webhook** → `handleFeaturedCampaignPayment()` 
6. **Database** → payment record + campaign.is_featured = true

### Payment Metadata Structure:
```json
{
  "type": "featured_campaign",
  "campaignId": "uuid",
  "businessId": "uuid", 
  "durationDays": "7|14",
  "paymentAmount": "30.00|50.00",
  "platformFee": "3.00|5.00",
  "totalAmount": "30.00|50.00"
}
```

### Database Record (payments tabela):
```sql
{
  campaign_id: "uuid", 
  payment_type: "featured_campaign",
  payment_amount: 30.00,
  platform_fee: 3.00,
  total_paid: 30.00,
  currency: "EUR",
  payment_status: "completed",
  metadata: {
    duration_days: 7,
    starts_at: "2025-09-09T...",
    ends_at: "2025-09-16T..."
  }
}
```

## 🧪 Test Scenarios

### ✅ Manual Testing Required:

1. **Campaign Selection**
   - Open campaign za promociju
   - Click "Promoviši" button
   - Verify modal opens correctly

2. **Payment Options**
   - Select 7 days (€30.00) 
   - Select 14 days (€50.00)
   - Verify calculations are correct

3. **Payment Flow**
   - Click "Promoviši za €X.XX"
   - Verify redirected to Stripe checkout
   - Complete payment (test mode)
   - Verify redirect na success page

4. **Webhook Processing**
   - Check Edge Function logs
   - Verify payment record kreiran u payments tabeli
   - Verify campaign.is_featured = true
   - Verify campaign.featured_until postavljeno

5. **UI Updates**
   - Campaign show sa featured badge
   - Featured status visible u campaign listi
   - Modal ne dozvoljava duplikate (idempotency)

## 🚨 Potrebni sledeći koraci

1. **Deploy Edge Function** na Supabase
2. **Run migration** za database schema
3. **Manual testing** sa test Stripe account
4. **Production testing** sa real payment

## 📋 Workflow Overview

**Stari sistem** (create-checkout-session):
❌ API Route → Stripe direktno → featured_campaign_promotions tabela

**Novi sistem** (Edge Functions):
✅ Modal → API Route → Edge Function → Stripe → Webhook → **payments** tabela

---

**Ready for testing!** 🎉
