'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { GradientCard } from '@/components/ui/gradient-card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import PlatformIconSimple from '@/components/ui/platform-icon-simple';
import Image from 'next/image';
import { TabsWithBadge } from '@/components/ui/tabs-with-badge';
import { TabsContent } from '@/components/ui/tabs';
import { GradientTabs } from '@/components/ui/gradient-tabs';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  MapPin,
  Users,
  Star,
  Verified,
  MessageCircle,
  Calendar,
  Globe,
  Package,
  CheckCircle,
  ArrowLeft,
  X,
} from 'lucide-react';
import { DirectOfferForm } from '@/components/offers/DirectOfferForm';
import { PackageOrderModal } from '@/components/offers/PackageOrderModal';
import { ResponsiveNavigation } from '@/components/navigation/ResponsiveNavigation';
import { DesktopHeader } from '@/components/navigation/DesktopHeader';
import { UpgradeRequiredModal } from '@/components/modals/UpgradeRequiredModal';
import { useAuth } from '@/contexts/AuthContext';
import { getOrCreateProfile } from '@/lib/profiles';
import { useBackButton } from '@/hooks/useBackButton';
import { toast } from 'sonner';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import type { PublicInfluencerProfile } from '@/lib/marketplace';
import { Rating } from '@/components/ui/rating';
import { formatPrice } from '@/lib/currency';
import {
  checkCustomOfferLimit,
  formatResetDate,
  type CustomOfferLimitResult,
} from '@/lib/custom-offer-limits';

interface InfluencerProfileClientProps {
  profile: PublicInfluencerProfile;
  businessSubscriptionType?: 'free' | 'premium';
}

export function InfluencerProfileClient({
  profile,
  businessSubscriptionType = 'free',
}: InfluencerProfileClientProps) {
  const { user } = useAuth();
  const router = useRouter();
  const [showOfferForm, setShowOfferForm] = useState(false);
  const [showPackageOrderModal, setShowPackageOrderModal] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState<any>(null);
  const [businessProfile, setBusinessProfile] = useState<any>(null);
  const [activePackageTab, setActivePackageTab] = useState('sve');
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [customOfferLimitResult, setCustomOfferLimitResult] =
    useState<CustomOfferLimitResult | null>(null);
  const { shouldShowBackButton } = useBackButton();

  // Filter platforms data based on subscription type to completely hide handles from free users
  const processedProfile = {
    ...profile,
    platforms: profile.platforms.map(platform => ({
      ...platform,
      handle: businessSubscriptionType === 'premium' ? platform.handle : null,
    })),
  };

  useEffect(() => {
    if (user && user.user_metadata?.user_type === 'business') {
      loadBusinessProfile();
    }
  }, [user]);

  const loadBusinessProfile = async () => {
    if (!user) return;

    try {
      const { data, error } = await getOrCreateProfile(user.id);
      if (data && !error) {
        setBusinessProfile(data);
      }
    } catch (error) {
      console.error('Error loading business profile:', error);
    }
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    const starSize = 'w-4 h-4';

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <Star
            key={i}
            className={`${starSize} fill-amber-400 text-amber-400`}
          />
        );
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <div key={i} className={`relative ${starSize}`}>
            <Star className={`${starSize} text-gray-300 absolute`} />
            <Star
              className={`${starSize} fill-amber-400 text-amber-400 absolute overflow-hidden`}
              style={{ clipPath: 'inset(0 50% 0 0)' }}
            />
          </div>
        );
      } else {
        stars.push(<Star key={i} className={`${starSize} text-gray-300`} />);
      }
    }

    return stars;
  };

  const getInitials = (name: string | null) => {
    if (!name) return '??';
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const formatFollowers = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  const getGenderLabel = (gender: string) => {
    switch (gender) {
      case 'male':
        return 'Muški';
      case 'female':
        return 'Ženski';
      case 'other':
        return 'Ostalo';
      default:
        return '';
    }
  };

  const handleOfferSuccess = () => {
    setShowOfferForm(false);
    toast.success('Ponuda je uspješno poslana!');
  };

  const handlePackageOrderSuccess = () => {
    setShowPackageOrderModal(false);
    setSelectedPackage(null);
    toast.success('Narudžba je uspješno poslana!');
  };

  const handleSendOffer = async () => {
    if (!user) {
      toast.error('Morate biti prijavljeni da biste poslali ponudu');
      return;
    }

    if (!businessProfile) {
      toast.error('Profil nije učitan');
      return;
    }

    // Provjeri ograničenja za free business korisnike
    try {
      const limitResult = await checkCustomOfferLimit(businessProfile.id);

      if (!limitResult.canSend && limitResult.isFreePlan) {
        // Spremi rezultat limita za modal
        setCustomOfferLimitResult(limitResult);
        // Prikaži upgrade modal sa porukom o limitu
        setShowUpgradeModal(true);
        return;
      }
    } catch (error) {
      console.error('Greška pri provjeri limita:', error);
      // U slučaju greške, dozvoljavamo slanje ponude
    }

    setShowOfferForm(true);
  };

  const handleOrderPackage = (pkg: any) => {
    if (!user) {
      toast.error('Morate biti prijavljeni da biste naručili paket');
      return;
    }
    setSelectedPackage(pkg);
    setShowPackageOrderModal(true);
  };

  const handleUpgradeRequiredClick = () => {
    setShowUpgradeModal(true);
  };

  return (
    <div className="min-h-screen bg-background pb-16 md:pb-0">
      {/* Desktop Header */}
      <div className="hidden md:block">
        <DesktopHeader userType="business" profile={businessProfile} />
      </div>

      {/* Mobile Navigation */}
      <ResponsiveNavigation
        userType="business"
        profile={businessProfile}
        showBackButton={shouldShowBackButton}
        onBackClick={() => router.back()}
      />

      {/* Header */}
      <div className="border-b bg-card">
        <div className="container mx-auto">
          {/* Desktop: Back button */}
          <div className="hidden md:block pt-6 pb-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.back()}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Nazad
            </Button>
          </div>

          {/* Mobile: Stack layout, Desktop: Split layout */}
          <div className="flex flex-col lg:grid lg:grid-cols-5 lg:gap-8 lg:items-start">
            {/* Profile Image - Full width on mobile, left side on desktop */}
            <div className="w-full lg:col-span-2">
              <div className="relative w-full aspect-square lg:w-full lg:max-w-md lg:mx-auto lg:rounded-lg overflow-hidden bg-gray-100">
                <Image
                  src={profile.avatar_url || '/placeholder.svg'}
                  alt={profile.full_name || profile.username}
                  fill
                  quality={100}
                  className="object-cover"
                  sizes="(max-width: 1024px) 100vw, 400px"
                  priority
                />
              </div>
            </div>

            {/* Profile Info - Below image on mobile, right side on desktop */}
            <div className="px-4 py-6 lg:col-span-3 lg:px-0 lg:py-8">
              <div className="space-y-6 lg:space-y-8">
                {/* Name and Username */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2 flex-wrap">
                    <h1 className="text-2xl lg:text-4xl font-bold text-gray-900">
                      {profile.full_name || profile.username}
                    </h1>
                    {profile.subscription_type === 'premium' && (
                      <Badge
                        variant="secondary"
                        className="bg-gradient-to-r from-pink-500 to-purple-500 text-white border-0 px-2 py-1 shadow-lg"
                      >
                        <Image
                          src="/images/influexus_logo_white_small_300x300.webp"
                          alt="Verified"
                          width={12}
                          height={12}
                          className="w-3 h-3 mr-1"
                        />
                        <span className="text-xs">Verified</span>
                      </Badge>
                    )}
                  </div>
                  <p className="text-lg lg:text-xl text-muted-foreground">
                    @{profile.username}
                  </p>
                </div>

                {/* Rating */}
                <div className="flex items-center gap-1">
                  <div className="flex items-center gap-0.5">
                    {renderStars(profile.average_rating || 0)}
                  </div>
                  <span className="text-base lg:text-lg font-medium ml-2">
                    {(profile.average_rating || 0).toFixed(1)}
                  </span>
                  {profile.total_reviews > 0 && (
                    <span className="text-sm lg:text-base text-muted-foreground">
                      ({profile.total_reviews}{' '}
                      {profile.total_reviews === 1 ? 'recenzija' : 'recenzije'})
                    </span>
                  )}
                </div>

                {/* Lokacija i demografija */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-3 text-sm lg:text-base">
                  {profile.location && (
                    <div className="flex items-center gap-2 bg-gray-50 rounded-lg px-3 py-2">
                      <MapPin className="h-4 w-4 lg:h-5 lg:w-5 text-purple-500 flex-shrink-0" />
                      <span className="text-gray-700">{profile.location}</span>
                    </div>
                  )}
                  {profile.age && (
                    <div className="flex items-center gap-2 bg-gray-50 rounded-lg px-3 py-2">
                      <Calendar className="h-4 w-4 lg:h-5 lg:w-5 text-pink-500 flex-shrink-0" />
                      <span className="text-gray-700">
                        {profile.age} godina
                      </span>
                    </div>
                  )}
                  {profile.gender && (
                    <div className="flex items-center gap-2 bg-gray-50 rounded-lg px-3 py-2 sm:col-span-2 lg:col-span-1 xl:col-span-2">
                      <Users className="h-4 w-4 lg:h-5 lg:w-5 text-purple-500 flex-shrink-0" />
                      <span className="text-gray-700">
                        {getGenderLabel(profile.gender)}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          {/* Desktop: Two columns, Mobile: Single column */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column: Bio, Categories, Platforms, Portfolio */}
            <div className="space-y-6">
              {/* Bio */}
              {profile.bio && (
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 border border-purple-200/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 group hover:scale-[1.02]">
                  {/* Dreamy gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 opacity-60 group-hover:opacity-80 transition-opacity duration-300" />
                  {/* Subtle glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-300/20 via-pink-300/10 to-purple-400/20 blur-xl opacity-50 group-hover:opacity-70 transition-opacity duration-300" />

                  <div className="relative p-6">
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
                      O meni
                    </h3>
                    <p className="text-gray-700 leading-relaxed">
                      {profile.bio}
                    </p>
                  </div>
                </div>
              )}

              {/* Kategorije */}
              {profile.categories && profile.categories.length > 0 && (
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 border border-purple-200/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 group hover:scale-[1.02]">
                  {/* Dreamy gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 opacity-60 group-hover:opacity-80 transition-opacity duration-300" />
                  {/* Subtle glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-300/20 via-pink-300/10 to-purple-400/20 blur-xl opacity-50 group-hover:opacity-70 transition-opacity duration-300" />

                  <div className="relative p-6">
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
                      Kategorije sadržaja kojeg kreiram
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {profile.categories.map((category, index) => (
                        <Badge
                          key={index}
                          variant="secondary"
                          className="bg-gradient-to-r from-violet-100 to-purple-100 text-violet-700 border-violet-200 text-sm"
                        >
                          {category.name || category.category_name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Platforme */}
              {processedProfile.platforms &&
                processedProfile.platforms.length > 0 && (
                  <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 border border-purple-200/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 group hover:scale-[1.02]">
                    {/* Dreamy gradient overlay */}
                    <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 opacity-60 group-hover:opacity-80 transition-opacity duration-300" />
                    {/* Subtle glow effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-300/20 via-pink-300/10 to-purple-400/20 blur-xl opacity-50 group-hover:opacity-70 transition-opacity duration-300" />

                    <div className="relative p-6 space-y-4">
                      <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                        Društvene mreže
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {processedProfile.platforms.map((platform, index) => {
                          const getSocialMediaUrl = (
                            platformName: string,
                            handle: string
                          ) => {
                            const normalizedPlatform =
                              platformName.toLowerCase();
                            const cleanHandle = handle.replace('@', '');

                            switch (normalizedPlatform) {
                              case 'instagram':
                                return `https://www.instagram.com/${cleanHandle}`;
                              case 'tiktok':
                                return `https://www.tiktok.com/@${cleanHandle}`;
                              case 'youtube':
                                return `https://www.youtube.com/@${cleanHandle}`;
                              case 'facebook':
                                return `https://www.facebook.com/${cleanHandle}`;
                              case 'twitter':
                              case 'x':
                                return `https://www.twitter.com/${cleanHandle}`;
                              case 'linkedin':
                                return `https://www.linkedin.com/in/${cleanHandle}`;
                              default:
                                return null;
                            }
                          };

                          const socialUrl = platform.handle
                            ? getSocialMediaUrl(
                                platform.platform_name,
                                platform.handle
                              )
                            : null;

                          return (
                            <div
                              key={index}
                              className="bg-white/60 rounded-lg p-4 border border-purple-100/50 hover:bg-white/80 transition-all duration-300"
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                  <PlatformIconSimple
                                    platform={platform.platform_name}
                                    size="lg"
                                  />
                                  <div>
                                    <p className="font-medium text-gray-800">
                                      {platform.platform_name}
                                    </p>
                                    <div className="text-sm text-gray-600">
                                      {platform.handle ? (
                                        socialUrl ? (
                                          <a
                                            href={socialUrl}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-blue-600 hover:text-blue-800 hover:underline transition-colors"
                                          >
                                            @{platform.handle}
                                          </a>
                                        ) : (
                                          <span>@{platform.handle}</span>
                                        )
                                      ) : (
                                        <span
                                          className="text-blue-600 hover:text-blue-800 hover:underline cursor-pointer transition-colors"
                                          onClick={handleUpgradeRequiredClick}
                                        >
                                          Handle skriven
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                </div>
                                <div className="text-right">
                                  <p className="font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                                    {formatFollowers(platform.followers_count)}
                                  </p>
                                  <p className="text-xs text-gray-600">
                                    pratilaca
                                  </p>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                )}

              {/* Portfolio */}
              {profile.portfolio_urls && profile.portfolio_urls.length > 0 && (
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 border border-purple-200/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 group hover:scale-[1.02]">
                  {/* Dreamy gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 opacity-60 group-hover:opacity-80 transition-opacity duration-300" />
                  {/* Subtle glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-300/20 via-pink-300/10 to-purple-400/20 blur-xl opacity-50 group-hover:opacity-70 transition-opacity duration-300" />

                  <div className="relative p-6 space-y-4">
                    <div className="flex items-center gap-2">
                      <Globe className="h-5 w-5 text-purple-500" />
                      <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                        Portfolio
                      </h3>
                    </div>
                    <div className="space-y-3">
                      {profile.portfolio_urls.map((url, index) => (
                        <a
                          key={index}
                          href={url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="block bg-white/60 rounded-lg p-3 border border-purple-100/50 hover:bg-white/80 transition-all duration-300"
                        >
                          <div className="flex items-center gap-2">
                            <Globe className="h-4 w-4 text-purple-500" />
                            <span className="text-sm bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent hover:underline font-medium">
                              {url}
                            </span>
                          </div>
                        </a>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Right Column: Pricing Packages */}
            <div className="space-y-6">
              {/* Pricing Packages */}
              {profile.pricing && profile.pricing.length > 0 && (
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 border border-purple-200/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 group hover:scale-[1.02]">
                  {/* Dreamy gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 opacity-60 group-hover:opacity-80 transition-opacity duration-300" />
                  {/* Subtle glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-300/20 via-pink-300/10 to-purple-400/20 blur-xl opacity-50 group-hover:opacity-70 transition-opacity duration-300" />

                  <div className="relative p-6">
                    <div className="flex items-center gap-2 mb-6">
                      <Package className="h-5 w-5 text-purple-500" />
                      <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                        Paketi usluga
                      </h3>
                    </div>
                    {(() => {
                      // Group packages by platform
                      const packagesByPlatform = profile.pricing.reduce(
                        (acc, pkg) => {
                          const platformKey = pkg.platform_id.toString();
                          if (!acc[platformKey]) {
                            acc[platformKey] = {
                              platform_id: pkg.platform_id,
                              platform_name: pkg.platform_name,
                              platform_icon:
                                profile.platforms.find(
                                  p => p.platform_id === pkg.platform_id
                                )?.platform_icon || '📱',
                              packages: [],
                            };
                          }
                          acc[platformKey].packages.push(pkg);
                          return acc;
                        },
                        {} as Record<
                          string,
                          {
                            platform_id: number;
                            platform_name: string;
                            platform_icon: string;
                            packages: typeof profile.pricing;
                          }
                        >
                      );

                      const platforms = Object.values(packagesByPlatform);

                      if (platforms.length === 0) return null;

                      return (
                        <div className="space-y-6">
                          <GradientTabs
                            tabs={[
                              {
                                name: 'Sve',
                                value: 'sve',
                                count: profile.pricing.length,
                              },
                              ...platforms.map(platform => ({
                                name: platform.platform_name,
                                value: platform.platform_id.toString(),
                                count: platform.packages.length,
                              })),
                            ]}
                            activeTab={activePackageTab}
                            onTabChange={setActivePackageTab}
                          />

                          {/* All packages */}
                          {activePackageTab === 'sve' && (
                            <div className="space-y-3">
                              <h4 className="text-lg font-medium text-gray-800">
                                Svi paketi
                              </h4>
                              <p className="text-sm text-gray-600">
                                Pregled svih dostupnih paketa na svim
                                platformama
                              </p>
                              <div className="space-y-3">
                                {profile.pricing.map((pkg, index) => (
                                  <div
                                    key={index}
                                    className="bg-white/60 rounded-lg p-4 border border-purple-100/50 hover:bg-white/80 transition-all duration-300"
                                  >
                                    <div className="flex items-center justify-between">
                                      <div className="flex-1">
                                        <div className="flex items-center gap-2 mb-1">
                                          <PlatformIconSimple
                                            platform={pkg.platform_name}
                                            size="md"
                                          />
                                          <span className="font-medium text-sm text-gray-800">
                                            {pkg.auto_generated_name ||
                                              pkg.content_type_name}
                                          </span>
                                        </div>
                                        <p className="text-xs text-gray-600">
                                          {pkg.platform_name}
                                        </p>
                                      </div>
                                      <div className="text-right flex items-center gap-3">
                                        <span className="font-bold text-lg bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                                          {formatPrice(pkg.price)}
                                        </span>
                                        <Button
                                          size="sm"
                                          onClick={() =>
                                            handleOrderPackage(pkg)
                                          }
                                          className="bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 hover:from-purple-500 hover:via-pink-500 hover:to-purple-600 text-white transition-all duration-200 hover:shadow-lg"
                                        >
                                          Naruči
                                        </Button>
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Individual platform tabs */}
                          {platforms.map(
                            platform =>
                              activePackageTab ===
                                platform.platform_id.toString() && (
                                <div
                                  key={platform.platform_id}
                                  className="space-y-3"
                                >
                                  <div className="flex items-center gap-2">
                                    <PlatformIconSimple
                                      platform={platform.platform_name}
                                      size="md"
                                    />
                                    <h4 className="text-lg font-medium text-gray-800">
                                      {platform.platform_name}
                                    </h4>
                                  </div>
                                  <p className="text-sm text-gray-600">
                                    Paketi dostupni na {platform.platform_name}{' '}
                                    platformi
                                  </p>
                                  <div className="space-y-3">
                                    {platform.packages.map((pkg, index) => (
                                      <div
                                        key={index}
                                        className="bg-white/60 rounded-lg p-4 border border-purple-100/50 hover:bg-white/80 transition-all duration-300"
                                      >
                                        <div className="flex items-center justify-between">
                                          <div className="flex-1">
                                            <div className="flex items-center gap-2 mb-1">
                                              <span className="font-medium text-sm text-gray-800">
                                                {pkg.auto_generated_name ||
                                                  pkg.content_type_name}
                                              </span>
                                            </div>
                                            <p className="text-xs text-gray-600">
                                              {platform.platform_name}
                                            </p>
                                          </div>
                                          <div className="text-right flex items-center gap-3">
                                            <span className="font-bold text-lg bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                                              {formatPrice(pkg.price)}
                                            </span>
                                            <Button
                                              size="sm"
                                              onClick={() =>
                                                handleOrderPackage(pkg)
                                              }
                                              className="bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 hover:from-purple-500 hover:via-pink-500 hover:to-purple-600 text-white transition-all duration-200 hover:shadow-lg"
                                            >
                                              Naruči
                                            </Button>
                                          </div>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )
                          )}

                          {/* Custom Offer CTA - Only show if influencer is premium and has custom offers enabled */}
                          {profile.subscription_type === 'premium' && profile.custom_offers_enabled && (
                            <div className="mt-6 pt-4 border-t border-purple-200/50">
                              <div className="text-center space-y-3 bg-white/60 rounded-lg p-4">
                                <p className="text-sm text-gray-700 leading-relaxed">
                                  Niste pronašli odgovarajući paket? Napravite
                                  ovdje ponudu prema Vašim zahtjevima
                                </p>
                                <Button
                                  className="w-full bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 hover:from-purple-500 hover:via-pink-500 hover:to-purple-600 text-white transition-all duration-200 hover:shadow-lg"
                                  size="lg"
                                  onClick={handleSendOffer}
                                >
                                  <MessageCircle className="h-5 w-5 mr-2" />
                                  Pošaljite ponudu
                                </Button>
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })()}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Package Order Modal */}
      {selectedPackage && (
        <PackageOrderModal
          isOpen={showPackageOrderModal}
          onClose={() => {
            setShowPackageOrderModal(false);
            setSelectedPackage(null);
          }}
          onSuccess={handlePackageOrderSuccess}
          packageData={selectedPackage}
          influencerId={profile.id}
          influencerName={profile.full_name || profile.username}
        />
      )}

      {/* Direct Offer Modal */}
      <Dialog open={showOfferForm} onOpenChange={setShowOfferForm}>
        <DialogContent
          className="max-w-4xl max-h-[90vh] overflow-y-auto bg-instagram-subtle border border-white/20 backdrop-blur-sm"
          showCloseButton={false}
        >
          {/* Custom close button */}
          <button
            onClick={() => setShowOfferForm(false)}
            className="absolute top-4 right-4 z-50 w-8 h-8 rounded-full bg-white/20 hover:bg-white/30 text-white hover:text-white transition-all duration-200 flex items-center justify-center"
          >
            <X className="h-4 w-4" />
          </button>

          {/* Dreamy gradient overlay for custom offer modal */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-black/5 opacity-60 pointer-events-none" />
          {/* Subtle glow effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-purple-300/10 via-blue-300/10 to-purple-400/15 blur-xl opacity-50 pointer-events-none" />

          <div className="relative z-10 flex flex-col h-full">
            <DialogHeader className="flex-shrink-0 pb-4">
              <DialogTitle className="text-white font-bold">
                Pošaljite ponudu
              </DialogTitle>
            </DialogHeader>
            <div className="flex-1 overflow-y-auto">
              <DirectOfferForm
                influencerId={profile.id}
                influencerName={profile.full_name || profile.username}
                onSuccess={handleOfferSuccess}
                onCancel={() => setShowOfferForm(false)}
              />
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Upgrade Required Modal */}
      <UpgradeRequiredModal
        isOpen={showUpgradeModal}
        onClose={() => {
          setShowUpgradeModal(false);
          setCustomOfferLimitResult(null);
        }}
        feature={
          customOfferLimitResult ? 'custom_offer_limit' : 'handle_viewing'
        }
        currentCount={customOfferLimitResult?.totalOffersThisMonth}
        limit={3}
        resetDate={customOfferLimitResult?.resetDate}
      />
    </div>
  );
}
