'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Plus, X, Send, Loader2, Link, User, Euro } from 'lucide-react';
import { submitJobCompletion } from '@/lib/job-completions';
import { toast } from 'sonner';

interface CampaignJobSubmissionFormProps {
  campaignApplicationId: string;
  campaignTitle?: string;
  businessName?: string;
  businessAvatar?: string | null;
  proposedRate?: number;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function CampaignJobSubmissionForm({
  campaignApplicationId,
  campaignTitle,
  businessName,
  businessAvatar,
  proposedRate,
  onSuccess,
  onCancel,
}: CampaignJobSubmissionFormProps) {
  const [submissionMessage, setSubmissionMessage] = useState('');
  const [postLinks, setPostLinks] = useState<string[]>(['']);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const addLinkField = () => {
    setPostLinks(prev => [...prev, '']);
  };

  const removeLinkField = (index: number) => {
    if (postLinks.length > 1) {
      setPostLinks(prev => prev.filter((_, i) => i !== index));
    }
  };

  const updateLink = (index: number, value: string) => {
    setPostLinks(prev => prev.map((link, i) => (i === index ? value : link)));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validLinks = postLinks.filter(link => link.trim() !== '');
    if (validLinks.length === 0) {
      toast.error('Molimo dodajte bar jedan link do posta');
      return;
    }

    // Validate URLs
    for (const link of validLinks) {
      try {
        new URL(link);
      } catch {
        toast.error('Molimo unesite važeće URL linkove');
        return;
      }
    }

    setIsSubmitting(true);

    try {
      // Create submission data with links and optional message
      const submissionData = {
        post_links: validLinks,
        message: submissionMessage.trim() || null,
      };

      const { error } = await submitJobCompletion(
        campaignApplicationId,
        JSON.stringify(submissionData),
        null // No files needed anymore
      );

      if (error) {
        toast.error('Neuspješno slanje predaje');
        return;
      }

      toast.success('Predaja je uspješno poslana na pregled!');
      onSuccess?.();
    } catch (error) {
      console.error('Error submitting job completion:', error);
      toast.error('Dogodila se neočekivana greška');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-3">
          <Send className="h-5 w-5" />
          Predaja završenog posla
        </CardTitle>
        <CardDescription>
          Dodajte linkove objavljenih postova i pošaljite biznisu na pregled
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Business Info */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">
              Predaje se za
            </Label>
            <div className="flex items-center gap-3 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100">
              <Avatar className="h-12 w-12 ring-2 ring-blue-200">
                <AvatarImage src={businessAvatar || ''} />
                <AvatarFallback className="bg-blue-100 text-blue-600">
                  <User className="h-6 w-6" />
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <p className="font-semibold text-gray-900">{businessName}</p>
                <p className="text-sm text-blue-600">
                  {campaignTitle || 'Kampanja'}
                </p>
              </div>
              {proposedRate && (
                <div className="flex items-center gap-1 text-lg font-bold text-green-600 bg-green-50 px-3 py-1 rounded-full">
                  <Euro className="h-5 w-5" />
                  {proposedRate}
                </div>
              )}
            </div>
          </div>

          {/* Post Links */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium text-gray-700">
                Linkovi objavljenih postova *
              </Label>
              <Button
                type="button"
                onClick={addLinkField}
                variant="outline"
                size="sm"
                className="flex items-center gap-2 text-blue-600 border-blue-300 hover:bg-blue-50"
              >
                <Plus className="h-4 w-4" />
                Dodaj link
              </Button>
            </div>
            <div className="space-y-3">
              {postLinks.map((link, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div className="flex-1 relative">
                    <Link className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      type="url"
                      placeholder="https://instagram.com/p/..."
                      value={link}
                      onChange={e => updateLink(index, e.target.value)}
                      className="pl-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>
                  {postLinks.length > 1 && (
                    <Button
                      type="button"
                      onClick={() => removeLinkField(index)}
                      variant="ghost"
                      size="sm"
                      className="text-red-500 hover:text-red-700 hover:bg-red-50"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
            </div>
            <p className="text-xs text-gray-500">
              Dodajte linkove do svih objavljenih postova (Instagram, TikTok,
              Facebook, itd.)
            </p>
          </div>

          {/* Optional Message */}
          <div className="space-y-2">
            <Label
              htmlFor="submission-message"
              className="text-sm font-medium text-gray-700"
            >
              Dodatna poruka (opcionalno)
            </Label>
            <Textarea
              id="submission-message"
              placeholder="Dodajte bilo kakve dodatne informacije o završenom poslu..."
              value={submissionMessage}
              onChange={e => setSubmissionMessage(e.target.value)}
              rows={4}
              className="resize-none border-gray-300 focus:border-blue-500 focus:ring-blue-500"
            />
            <p className="text-xs text-gray-500">
              {submissionMessage.length}/500 karaktera
            </p>
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-6 border-t border-gray-200">
            <Button
              type="submit"
              disabled={isSubmitting || postLinks.every(link => !link.trim())}
              className="flex-1 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold py-3 rounded-lg transition-all duration-200"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  Šalje se...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-5 w-5" />
                  Pošalji na pregled
                </>
              )}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
              className="border-gray-300 text-gray-700 hover:bg-gray-50 px-6 py-3 rounded-lg"
            >
              Otkaži
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
