'use client';

import Link from 'next/link';

export default function Footer() {
  return (
    <footer className="relative border-t border-white/20 py-12 px-4">
      <div className="container mx-auto">
        <div className="flex flex-col lg:flex-row items-center lg:items-start space-y-8 lg:space-y-0 lg:space-x-16">
          {/* Logo, brand i društvene mreže - lijevo */}
          <div className="flex flex-col items-center lg:items-start">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center border border-white/30">
                <span className="text-white font-bold text-lg">🔗</span>
              </div>
              <span className="text-xl font-bold text-white">INFLUEXUS</span>
            </div>
            <p className="text-white/80 text-center lg:text-left max-w-sm mb-6">
              Povezujemo. Kreiramo. Uspijevamo.
            </p>

            {/* Pratite nas - ispod loga */}
            <div className="text-center lg:text-left">
              <h3 className="text-white font-semibold mb-3">Pratite nas</h3>
              <div className="flex items-center justify-center lg:justify-start space-x-3">
                <a
                  href="#"
                  className="w-8 h-8 bg-white/10 hover:bg-white/20 rounded-lg flex items-center justify-center transition-colors duration-200"
                >
                  <svg
                    className="w-4 h-4 text-white"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z" />
                  </svg>
                </a>
                <a
                  href="#"
                  className="w-8 h-8 bg-white/10 hover:bg-white/20 rounded-lg flex items-center justify-center transition-colors duration-200"
                >
                  <svg
                    className="w-4 h-4 text-white"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z" />
                  </svg>
                </a>
                <a
                  href="#"
                  className="w-8 h-8 bg-white/10 hover:bg-white/20 rounded-lg flex items-center justify-center transition-colors duration-200"
                >
                  <svg
                    className="w-4 h-4 text-white"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                  </svg>
                </a>
              </div>
            </div>
          </div>

          {/* Linkovi - ostale kolone */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
            {/* Kolona 1 - O platformi */}
            <div className="text-center lg:text-left">
              <h3 className="text-white font-semibold mb-3">O platformi</h3>
              <ul className="space-y-2">
                <li>
                  <Link
                    href="/kako-funkcionise"
                    className="text-white/70 hover:text-white transition-colors duration-200"
                  >
                    Sigurnost
                  </Link>
                </li>
                <li>
                  <Link
                    href="/influencer"
                    className="text-white/70 hover:text-white transition-colors duration-200"
                  >
                    Za influencere
                  </Link>
                </li>
                <li>
                  <Link
                    href="/brand"
                    className="text-white/70 hover:text-white transition-colors duration-200"
                  >
                    Za brendove
                  </Link>
                </li>
                <li>
                  <Link
                    href="/registracija"
                    className="text-white/70 hover:text-white transition-colors duration-200"
                  >
                    Registracija
                  </Link>
                </li>
                <li>
                  <Link
                    href="/prijava"
                    className="text-white/70 hover:text-white transition-colors duration-200"
                  >
                    Prijava
                  </Link>
                </li>
              </ul>
            </div>

            {/* Kolona 2 - Pravni dokumenti */}
            <div className="text-center lg:text-left">
              <h3 className="text-white font-semibold mb-3">
                Pravni dokumenti
              </h3>
              <ul className="space-y-2">
                <li>
                  <Link
                    href="/uslovi-koristenja"
                    className="text-white/70 hover:text-white transition-colors duration-200"
                  >
                    Uslovi korištenja
                  </Link>
                </li>
                <li>
                  <Link
                    href="/politika-privatnosti"
                    className="text-white/70 hover:text-white transition-colors duration-200"
                  >
                    Politika privatnosti
                  </Link>
                </li>
              </ul>
            </div>

            {/* Kolona 3 - Podrška */}
            <div className="text-center lg:text-left">
              <h3 className="text-white font-semibold mb-3">Podrška</h3>
              <ul className="space-y-2">
                <li>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-white/70 hover:text-white transition-colors duration-200"
                  >
                    Pomoć
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-white/70 hover:text-white transition-colors duration-200"
                  >
                    FAQ
                  </a>
                </li>
              </ul>
            </div>

            {/* Kolona 4 - Kontakt */}
            <div className="text-center lg:text-left">
              <h3 className="text-white font-semibold mb-3">Kontakt</h3>
              <div className="space-y-2">
                <div className="flex items-center justify-center lg:justify-start space-x-2">
                  <svg
                    className="w-4 h-4 text-white/70"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-white/70 hover:text-white transition-colors duration-200 text-sm"
                  >
                    <EMAIL>
                  </a>
                </div>
                <div className="flex items-center justify-center lg:justify-start space-x-2">
                  <svg
                    className="w-4 h-4 text-white/70"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                    />
                  </svg>
                  <a
                    href="tel:+38761234567"
                    className="text-white/70 hover:text-white transition-colors duration-200 text-sm"
                  >
                    +387 61 234 567
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Copyright - dno */}
        <div className="border-t border-white/10 mt-8 pt-6 text-center">
          <p className="text-sm text-white/60">
            © 2025 INFLUEXUS. Sva prava zadržana.
          </p>
        </div>
      </div>
    </footer>
  );
}
