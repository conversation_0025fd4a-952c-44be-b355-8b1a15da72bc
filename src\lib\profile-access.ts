import { supabase } from '@/lib/supabase';

export interface ProfileAccessResult {
  hasAccess: boolean;
  reason?: string;
  redirectTo?: string;
}

/**
 * Provjeri da li trenutni korisnik može pristupiti influencer profilu
 *
 * Pravila pristupa:
 * - Vlasnik profila može pristupiti svom profilu
 * - Business korisnici mogu pristupiti bilo kom influencer profilu
 * - Influenceri NE MOGU pristupiti profilima drugih influencera
 * - Neautentifikovani korisnici ne mogu pristupiti (middleware ih blokira)
 *
 * @param targetUsername - username influencera čiji profil se pokušava pristupiti
 * @param currentUserId - ID trenutno ulogovanog korisnika
 * @returns ProfileAccessResult sa informacijom o pristupu
 */
export async function checkInfluencerProfileAccess(
  targetUsername: string,
  currentUserId: string
): Promise<ProfileAccessResult> {
  try {
    // 1. <PERSON>hvati podatke o trenutnom korisniku
    const { data: currentUserProfile, error: currentUserError } = await supabase
      .from('profiles')
      .select('id, username, user_type')
      .eq('id', currentUserId)
      .single();

    if (currentUserError || !currentUserProfile) {
      return {
        hasAccess: false,
        reason: 'Greška pri učitavanju korisničkih podataka',
        redirectTo: '/dashboard',
      };
    }

    // 2. Dohvati podatke o target influenceru
    const { data: targetProfile, error: targetError } = await supabase
      .from('profiles')
      .select('id, username, user_type')
      .eq('username', targetUsername)
      .eq('user_type', 'influencer')
      .single();

    if (targetError || !targetProfile) {
      return {
        hasAccess: false,
        reason: 'Influencer profil ne postoji',
        redirectTo: '/dashboard',
      };
    }

    // 3. Provjeri da li je korisnik vlasnik profila
    if (currentUserProfile.id === targetProfile.id) {
      return {
        hasAccess: true,
        reason: 'Vlasnik profila',
      };
    }

    // 4. Provjeri tip korisnika
    if (currentUserProfile.user_type === 'business') {
      return {
        hasAccess: true,
        reason: 'Business korisnik može pristupiti influencer profilima',
      };
    }

    // 5. Influenceri ne mogu pristupiti profilima drugih influencera
    if (currentUserProfile.user_type === 'influencer') {
      return {
        hasAccess: false,
        reason: 'Influenceri ne mogu pristupiti profilima drugih influencera',
        redirectTo: '/dashboard/influencer',
      };
    }

    // 6. Nepoznat tip korisnika
    return {
      hasAccess: false,
      reason: 'Nepoznat tip korisnika',
      redirectTo: '/dashboard',
    };
  } catch (error) {
    console.error('Error checking profile access:', error);
    return {
      hasAccess: false,
      reason: 'Neočekivana greška pri provjeri pristupa',
      redirectTo: '/dashboard',
    };
  }
}

/**
 * Provjeri da li trenutni korisnik može pristupiti business profilu
 *
 * Napomena: Trenutno nema javnih business profila u aplikaciji,
 * ali ova funkcija je pripremljena za buduću implementaciju.
 *
 * Pravila pristupa:
 * - Vlasnik profila može pristupiti svom profilu
 * - Influencer korisnici mogu pristupiti bilo kom business profilu
 * - Biznisi NE MOGU pristupiti profilima drugih biznisa
 *
 * @param targetUsername - username biznisa čiji profil se pokušava pristupiti
 * @param currentUserId - ID trenutno ulogovanog korisnika
 * @returns ProfileAccessResult sa informacijom o pristupu
 */
export async function checkBusinessProfileAccess(
  targetUsername: string,
  currentUserId: string
): Promise<ProfileAccessResult> {
  try {
    // 1. Dohvati podatke o trenutnom korisniku
    const { data: currentUserProfile, error: currentUserError } = await supabase
      .from('profiles')
      .select('id, username, user_type')
      .eq('id', currentUserId)
      .single();

    if (currentUserError || !currentUserProfile) {
      return {
        hasAccess: false,
        reason: 'Greška pri učitavanju korisničkih podataka',
        redirectTo: '/dashboard',
      };
    }

    // 2. Dohvati podatke o target biznis korisniku
    const { data: targetProfile, error: targetError } = await supabase
      .from('profiles')
      .select('id, username, user_type')
      .eq('username', targetUsername)
      .eq('user_type', 'business')
      .single();

    if (targetError || !targetProfile) {
      return {
        hasAccess: false,
        reason: 'Business profil ne postoji',
        redirectTo: '/dashboard',
      };
    }

    // 3. Provjeri da li je korisnik vlasnik profila
    if (currentUserProfile.id === targetProfile.id) {
      return {
        hasAccess: true,
        reason: 'Vlasnik profila',
      };
    }

    // 4. Provjeri tip korisnika
    if (currentUserProfile.user_type === 'influencer') {
      return {
        hasAccess: true,
        reason: 'Influencer korisnik može pristupiti business profilima',
      };
    }

    // 5. Biznisi ne mogu pristupiti profilima drugih biznisa
    if (currentUserProfile.user_type === 'business') {
      return {
        hasAccess: false,
        reason: 'Biznisi ne mogu pristupiti profilima drugih biznisa',
        redirectTo: '/dashboard/biznis',
      };
    }

    // 6. Nepoznat tip korisnika
    return {
      hasAccess: false,
      reason: 'Nepoznat tip korisnika',
      redirectTo: '/dashboard',
    };
  } catch (error) {
    console.error('Error checking business profile access:', error);
    return {
      hasAccess: false,
      reason: 'Neočekivana greška pri provjeri pristupa',
      redirectTo: '/dashboard',
    };
  }
}

/**
 * Helper funkcija za dobijanje trenutnog korisnika iz Supabase auth (client-side)
 * @returns User ID ili null ako korisnik nije ulogovan
 */
export async function getCurrentUserId(): Promise<string | null> {
  try {
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return null;
    }

    return user.id;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}
