'use client';

import { useEffect, useState } from 'react';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Send, CheckCircle, XCircle, Inbox } from 'lucide-react';
import {
  getBusinessOffersCards,
  getBusinessOffersStats,
  type DirectOfferWithDetails,
} from '@/lib/offers';
import { GradientTabs } from '@/components/ui/gradient-tabs';
import BusinessOfferCard from '@/components/campaigns/BusinessOfferCard';
import Link from 'next/link';

interface OfferCard {
  id: string;
  influencer_id: string;
  title: string;
  description: string;
  budget: number;
  content_types: string[];
  platforms: string[];
  status: 'pending' | 'accepted' | 'rejected' | 'completed' | 'cancelled';
  offer_type: string;
  created_at: string;
  deadline: string | null;
  influencer_username: string;
  influencer_display_name: string;
  influencer_avatar_url: string | null;
}

export default function BusinessOffersPage() {
  const [offers, setOffers] = useState<OfferCard[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    accepted: 0,
    rejected: 0,
  });

  useEffect(() => {
    loadOffers();
  }, [activeTab]);

  const loadOffers = async () => {
    setIsLoading(true);
    try {
      // Get current user
      const { supabase } = await import('@/lib/supabase');
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) return;

      // Load offers and stats with optimized functions
      const [offersResult, statsResult] = await Promise.all([
        getBusinessOffersCards(
          user.id,
          activeTab === 'all' ? undefined : activeTab
        ),
        getBusinessOffersStats(user.id),
      ]);

      if (offersResult.error) {
        console.error('Error loading offers:', offersResult.error);
      } else {
        setOffers(offersResult.data || []);
      }

      if (statsResult.error) {
        console.error('Error loading stats:', statsResult.error);
      } else if (statsResult.data) {
        setStats({
          total: statsResult.data.total_count,
          pending: statsResult.data.pending_count,
          accepted: statsResult.data.accepted_count,
          rejected: statsResult.data.rejected_count,
        });
      }
    } catch (error) {
      console.error('Error loading offers:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Offers are already filtered by activeTab in useEffect

  if (isLoading) {
    return (
      <DashboardLayout requiredUserType="business">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="business">
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
            Moje ponude
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Upravljajte vašim direktnim ponudama influencerima
          </p>
        </div>

        {/* Gradient Tabs */}
        <GradientTabs
          tabs={[
            { name: 'Sve', value: 'all', count: stats.total },
            { name: 'Na čekanju', value: 'pending', count: stats.pending },
            { name: 'Prihvaćeno', value: 'accepted', count: stats.accepted },
            { name: 'Odbijeno', value: 'rejected', count: stats.rejected },
          ]}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          className="mb-6"
        />

        {/* Offers List */}
        <div className="space-y-4">
          {offers.length === 0 ? (
            <div className="text-center py-12">
              <Inbox className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {activeTab === 'all'
                  ? 'Nema ponuda'
                  : `Nema ponuda sa statusom "${activeTab}"`}
              </h3>
              <p className="text-gray-600 mb-4">
                {activeTab === 'all'
                  ? 'Još niste poslali nijednu direktnu ponudu influencerima.'
                  : 'Nema ponuda sa ovim statusom.'}
              </p>
              <Button
                asChild
                className="bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 hover:from-purple-500 hover:via-pink-500 hover:to-purple-600 text-white"
              >
                <Link href="/marketplace/influencers">Pronađi influencere</Link>
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {offers.map(offer => (
                <BusinessOfferCard
                  key={offer.id}
                  offer={{
                    ...offer,
                    // Map new interface to old BusinessOfferCard props
                    influencer: {
                      id: offer.influencer_id,
                      username: offer.influencer_username,
                      full_name: offer.influencer_display_name,
                      public_display_name: offer.influencer_display_name,
                      avatar_url: offer.influencer_avatar_url,
                    },
                  }}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
