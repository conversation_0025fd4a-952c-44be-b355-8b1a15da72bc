import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Calendar, Euro, Eye, Edit, Play, Loader2, Crown } from 'lucide-react';
import PlatformIconSimple from '@/components/ui/platform-icon-simple';
import Link from 'next/link';
import { Campaign } from '@/lib/types';

interface Platform {
  name: string;
  icon: string;
  content_types: string[];
}

interface CampaignWithPlatforms extends Campaign {
  platforms?: Platform[];
}

interface CampaignCardProps {
  campaign: CampaignWithPlatforms;
  onActivate?: (id: string) => void;
  isActivating?: boolean;
  onPromote?: (campaign: {
    id: string;
    title: string;
    business_id?: string;
  }) => void;
  userSubscriptionType?: 'free' | 'premium';
}

const CampaignCard: React.FC<CampaignCardProps> = ({
  campaign,
  onActivate,
  isActivating = false,
  onPromote,
  userSubscriptionType = 'free',
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-200';
      case 'draft':
        return 'bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 border-purple-200';
      case 'paused':
        return 'bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-800 border-yellow-200';
      case 'completed':
        return 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-200';
      case 'cancelled':
        return 'bg-gradient-to-r from-red-100 to-pink-100 text-red-800 border-red-200';
      default:
        return 'bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 border-purple-200';
    }
  };

  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      draft: 'Neaktivna',
      active: 'Aktivna',
      paused: 'Pauzirana',
      completed: 'Završena',
      cancelled: 'Otkazana',
    };
    return labels[status] || status;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('sr-RS', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const getContentTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      post: 'Photo Feed Post',
      story: 'Story',
      reel: 'Reel',
      video: 'Video',
      blog: 'Blog Post',
    };
    return labels[type] || type;
  };

  return (
    <div
      className={`relative overflow-hidden rounded-2xl backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 group hover:scale-[1.02] h-[550px] flex flex-col ${
        campaign.is_featured
          ? 'bg-amber-50 dark:bg-amber-950/30 border-2 border-amber-400/80 dark:border-amber-600/50'
          : 'bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30'
      }`}
    >
      {/* Premium gold overlay or gradient overlay */}
      <div
        className={`absolute inset-0 opacity-60 group-hover:opacity-80 transition-opacity duration-300 ${
          campaign.is_featured
            ? 'bg-amber-100/40 dark:bg-amber-900/20'
            : 'bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20'
        }`}
      />

      {/* Subtle glow effect */}
      <div
        className={`absolute inset-0 blur-xl opacity-50 group-hover:opacity-70 transition-opacity duration-300 ${
          campaign.is_featured
            ? 'bg-amber-400/30 dark:bg-amber-600/20'
            : 'bg-gradient-to-r from-purple-300/20 via-pink-300/10 to-purple-400/20 dark:from-purple-600/10 dark:via-pink-600/5 dark:to-purple-500/10'
        }`}
      />

      <div className="relative p-6 flex flex-col h-full">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1 min-w-0">
            {campaign.is_featured && (
              <div className="flex items-center gap-2 mb-2">
                <Crown className="w-4 h-4 text-amber-600" />
                <Badge className="bg-gradient-to-r from-amber-400 to-yellow-500 text-amber-900 border-amber-300 font-semibold text-xs">
                  Premium
                </Badge>
              </div>
            )}
            <div className="h-12 mb-2">
              <h3
                className={`text-xl font-semibold line-clamp-2 ${
                  campaign.is_featured
                    ? 'text-amber-800 dark:text-amber-300'
                    : 'bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent'
                }`}
              >
                {campaign.title}
              </h3>
            </div>
          </div>
          <div className="flex-shrink-0 ml-4">
            <Badge
              variant="outline"
              className={`${getStatusColor(campaign.status)} font-medium`}
            >
              {getStatusLabel(campaign.status)}
            </Badge>
          </div>
        </div>

        {/* Description - Fixed height for 2 lines */}
        <div className="h-10 mb-4">
          <p className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed line-clamp-2">
            {campaign.description}
          </p>
        </div>

        {/* Budget and Date - Fixed height */}
        <div className="h-16 mb-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-2 text-sm">
              <Euro className={`w-4 h-4 ${campaign.is_featured ? 'text-amber-600' : 'text-purple-500'}`} />
              <span className="text-gray-600 dark:text-gray-400">Budžet:</span>
              <span className="font-semibold text-gray-900 dark:text-gray-100">
                {campaign.budget?.toLocaleString() || 'N/A'} €
              </span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <Calendar className={`w-4 h-4 ${campaign.is_featured ? 'text-amber-600' : 'text-pink-500'}`} />
              <span className="text-gray-600 dark:text-gray-400">
                Datum kreiranja:
              </span>
              <span className="font-semibold text-gray-900 dark:text-gray-100">
                {formatDate(campaign.created_at)}
              </span>
            </div>
          </div>
        </div>

        {/* Platforms - Flexible height to take remaining space */}
        <div className="flex-1 mb-4">
          {campaign.platforms && campaign.platforms.length > 0 ? (
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200">
                Platforme i Content Type:
              </h4>
              <div className="space-y-2">
                {campaign.platforms.slice(0, 2).map((platform, index) => (
                  <div
                    key={index}
                    className={`rounded-lg p-3 border h-20 flex flex-col ${
                      campaign.is_featured
                        ? 'bg-amber-50/60 dark:bg-amber-900/20 border-amber-200/60 dark:border-amber-700/40'
                        : 'bg-white/60 dark:bg-gray-800/40 border-purple-100/50 dark:border-purple-800/30'
                    }`}
                  >
                    <div className="flex items-center gap-2 mb-2">
                      <PlatformIconSimple platform={platform.name} size="lg" />
                      <span className="font-medium text-gray-800 dark:text-gray-200 text-sm truncate">
                        {platform.name}
                      </span>
                    </div>
                    <div className="flex flex-wrap gap-1 overflow-hidden">
                      {platform.content_types
                        ?.slice(0, 3)
                        .map((type, typeIndex) => (
                          <Badge
                            key={typeIndex}
                            variant="secondary"
                            className={`text-xs ${
                              campaign.is_featured
                                ? 'bg-amber-100 dark:bg-amber-800/30 text-amber-800 dark:text-amber-200 border-amber-300 dark:border-amber-600'
                                : 'bg-gradient-to-r from-violet-100 to-purple-100 text-violet-700 border-violet-200'
                            }`}
                          >
                            {getContentTypeLabel(type)}
                          </Badge>
                        ))}
                      {platform.content_types &&
                        platform.content_types.length > 3 && (
                          <Badge variant="secondary" className="text-xs">
                            +{platform.content_types.length - 3}
                          </Badge>
                        )}
                    </div>
                  </div>
                ))}
                {campaign.platforms.length > 2 && (
                  <div className="text-xs text-gray-500 text-center">
                    +{campaign.platforms.length - 2} više platformi
                  </div>
                )}
              </div>
            </div>
          ) : campaign.content_types && campaign.content_types.length > 0 ? (
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200">
                Tipovi sadržaja:
              </h4>
              <div className="flex flex-wrap gap-1">
                {campaign.content_types.map((type, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className={`text-xs ${
                      campaign.is_featured
                        ? 'bg-amber-100 dark:bg-amber-800/30 text-amber-800 dark:text-amber-200 border-amber-300 dark:border-amber-600'
                        : 'bg-gradient-to-r from-violet-100 to-purple-100 text-violet-700 border-violet-200'
                    }`}
                  >
                    {getContentTypeLabel(type)}
                  </Badge>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-sm text-gray-500 italic">
              Platforme nisu specificirane
            </div>
          )}
        </div>

        {/* Action Buttons - Push to bottom */}
        <div className="flex gap-2 pt-2 mt-auto">
          <Link href={`/campaigns/${campaign.id}`}>
            <button className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white/70 dark:bg-gray-800/50 hover:bg-white dark:hover:bg-gray-800/70 border border-gray-200/50 dark:border-gray-700/50 rounded-lg transition-all duration-200 hover:shadow-md">
              <Eye className="w-4 h-4" />
              Pregled
            </button>
          </Link>

          {campaign.status === 'draft' && (
            <>
              <Link href={`/campaigns/${campaign.id}/edit`}>
                <button className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white/70 dark:bg-gray-800/50 hover:bg-white dark:hover:bg-gray-800/70 border border-gray-200/50 dark:border-gray-700/50 rounded-lg transition-all duration-200 hover:shadow-md">
                  <Edit className="w-4 h-4" />
                  Uredi
                </button>
              </Link>

              {onActivate && (
                <button
                  onClick={() => onActivate(campaign.id)}
                  disabled={isActivating}
                  className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-white bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 hover:from-purple-500 hover:via-pink-500 hover:to-purple-600 rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-purple-200/50 dark:hover:shadow-purple-900/30"
                >
                  {isActivating ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Play className="w-4 h-4" />
                  )}
                  Aktiviraj
                </button>
              )}
            </>
          )}

          {campaign.status === 'active' &&
            !campaign.is_featured &&
            onPromote &&
            userSubscriptionType === 'premium' && (
              <button
                onClick={() =>
                  onPromote({
                    id: campaign.id,
                    title: campaign.title,
                    business_id: campaign.business_id,
                  })
                }
                className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-yellow-800 bg-gradient-to-r from-yellow-100 to-yellow-200 hover:from-yellow-200 hover:to-yellow-300 border border-yellow-300 rounded-lg transition-all duration-200 hover:shadow-md"
              >
                <Crown className="w-4 h-4" />
                Promoviši
              </button>
            )}
        </div>
      </div>
    </div>
  );
};

export default CampaignCard;
