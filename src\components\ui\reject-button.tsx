import React from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';

interface RejectButtonProps extends React.ComponentProps<'button'> {
  variant?: 'primary' | 'secondary';
}

const RejectButton = React.forwardRef<HTMLButtonElement, RejectButtonProps>(
  ({ className, variant = 'primary', ...props }, ref) => {
    const variantClasses = {
      primary:
        'bg-gradient-to-r from-red-600 via-rose-600 to-red-700 hover:from-red-700 hover:via-rose-700 hover:to-red-800 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] hover:shadow-red-200/50 dark:hover:shadow-red-900/30',
      secondary:
        'bg-gradient-to-r from-red-50 to-rose-50 border border-red-200 text-red-700 hover:bg-gradient-to-r hover:from-red-100 hover:to-rose-100 hover:border-red-300 transition-all duration-300 hover:scale-[1.02]',
    };

    return (
      <Button
        ref={ref}
        className={cn(variantClasses[variant], className)}
        {...props}
      />
    );
  }
);

RejectButton.displayName = 'RejectButton';

export { RejectButton };
