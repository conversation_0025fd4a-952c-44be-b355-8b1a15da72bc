'use client';

import React, { type SyntheticEvent } from 'react';
import React<PERSON><PERSON>, {
  centerCrop,
  makeAspectCrop,
  type Crop,
  type PixelCrop,
} from 'react-image-crop';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Crop as CropIcon, X } from 'lucide-react';
import 'react-image-crop/dist/ReactCrop.css';

interface ImageCropperProps {
  isOpen: boolean;
  onClose: () => void;
  imageUrl: string;
  onCropComplete: (croppedImageBlob: File) => void;
  aspectRatio?: number;
}

export function ImageCropper({
  isOpen,
  onClose,
  imageUrl,
  onCropComplete,
  aspectRatio = 1, // Square by default
}: ImageCropperProps) {
  const imgRef = React.useRef<HTMLImageElement | null>(null);
  const [crop, setCrop] = React.useState<Crop>();
  const [completedCrop, setCompletedCrop] = React.useState<PixelCrop>();
  const [isProcessing, setIsProcessing] = React.useState(false);

  function onImageLoad(e: SyntheticEvent<HTMLImageElement>) {
    const { width, height } = e.currentTarget;
    setCrop(centerAspectCrop(width, height, aspectRatio));
  }

  function getCroppedImageBlob(
    image: HTMLImageElement,
    crop: PixelCrop
  ): Promise<File> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');

      // Ensure we have valid crop dimensions
      if (!crop.width || !crop.height || crop.width < 1 || crop.height < 1) {
        reject(new Error('Invalid crop dimensions'));
        return;
      }

      // Use a fixed output size for consistency
      const outputSize = 1080; // Match our IMAGE_CONFIG
      canvas.width = outputSize;
      canvas.height = outputSize;

      const ctx = canvas.getContext('2d');
      if (!ctx) {
        reject(new Error('Failed to get canvas context'));
        return;
      }

      // Calculate scale factors based on natural image size
      const scaleX = image.naturalWidth / image.width;
      const scaleY = image.naturalHeight / image.height;

      // Calculate source coordinates and dimensions
      const sourceX = crop.x * scaleX;
      const sourceY = crop.y * scaleY;
      const sourceWidth = crop.width * scaleX;
      const sourceHeight = crop.height * scaleY;

      console.log('Crop debug:', {
        displaySize: { width: image.width, height: image.height },
        naturalSize: { width: image.naturalWidth, height: image.naturalHeight },
        scaleX,
        scaleY,
        crop,
        source: {
          x: sourceX,
          y: sourceY,
          width: sourceWidth,
          height: sourceHeight,
        },
        canvas: { width: canvas.width, height: canvas.height },
      });

      // Clear canvas and set high quality
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = 'high';

      // Draw cropped image to fill entire canvas
      ctx.drawImage(
        image,
        sourceX,
        sourceY,
        sourceWidth,
        sourceHeight,
        0,
        0,
        outputSize,
        outputSize
      );

      canvas.toBlob(
        blob => {
          if (blob) {
            const file = new File([blob], 'cropped-avatar.jpg', {
              type: 'image/jpeg',
              lastModified: Date.now(),
            });
            console.log('Cropped file size:', blob.size);
            resolve(file);
          } else {
            reject(new Error('Failed to create blob from canvas'));
          }
        },
        'image/jpeg',
        0.95 // Higher quality
      );
    });
  }

  async function handleCrop() {
    if (!imgRef.current || !completedCrop?.width || !completedCrop?.height) {
      console.error('Missing required data for crop:', {
        hasImage: !!imgRef.current,
        completedCrop,
      });
      return;
    }

    console.log('Starting crop with:', {
      imageDimensions: {
        display: { width: imgRef.current.width, height: imgRef.current.height },
        natural: {
          width: imgRef.current.naturalWidth,
          height: imgRef.current.naturalHeight,
        },
      },
      completedCrop,
    });

    setIsProcessing(true);
    try {
      const croppedFile = await getCroppedImageBlob(
        imgRef.current,
        completedCrop
      );
      console.log('Crop completed successfully, file size:', croppedFile.size);
      onCropComplete(croppedFile);
      onClose();
    } catch (error) {
      console.error('Error cropping image:', error);
      alert(`Greška pri crop-ovanju: ${error}`);
    } finally {
      setIsProcessing(false);
    }
  }

  const handleClose = () => {
    setCrop(undefined);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto w-[95vw] sm:w-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Odaberi dio slike</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="p-1 h-auto"
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="relative flex justify-center">
            <ReactCrop
              crop={crop}
              onChange={(_, percentCrop) => setCrop(percentCrop)}
              onComplete={c => setCompletedCrop(c)}
              aspect={aspectRatio}
              className="max-w-full"
            >
              <img
                ref={imgRef}
                src={imageUrl}
                alt="Crop preview"
                onLoad={onImageLoad}
                className="max-w-full max-h-[40vh] sm:max-h-[50vh] object-contain"
                style={{ display: 'block' }}
              />
            </ReactCrop>
          </div>

          <div className="text-center text-sm text-muted-foreground px-4">
            Povuci uglove da odabereš dio slike koji će biti korišten kao avatar
          </div>
        </div>

        <DialogFooter className="gap-2 pt-4 flex-col sm:flex-row">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isProcessing}
            className="w-full sm:w-auto"
          >
            Otkaži
          </Button>
          <Button
            onClick={handleCrop}
            disabled={
              !completedCrop?.width || !completedCrop?.height || isProcessing
            }
            className="w-full sm:w-auto"
          >
            {isProcessing ? (
              'Obrađujem...'
            ) : (
              <>
                <CropIcon className="w-4 h-4 mr-2" />
                Potvrdi crop
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// Helper function to center the crop
function centerAspectCrop(
  mediaWidth: number,
  mediaHeight: number,
  aspect: number
): Crop {
  return centerCrop(
    makeAspectCrop(
      {
        unit: '%',
        width: 80, // Start with 80% of the image
      },
      aspect,
      mediaWidth,
      mediaHeight
    ),
    mediaWidth,
    mediaHeight
  );
}
