'use client';

import { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { toast } from 'sonner';
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';

export function PaymentStatusHandler() {
  const searchParams = useSearchParams();
  const payment = searchParams.get('payment');
  const sessionId = searchParams.get('session_id');

  useEffect(() => {
    if (payment === 'success' && sessionId) {
      // Check if we already showed this notification
      const notificationKey = `payment-notification-${sessionId}`;
      if (sessionStorage.getItem(notificationKey)) {
        return;
      }
      sessionStorage.setItem(notificationKey, 'shown');
      toast.success(
        <div className="flex items-center gap-2">
          <CheckCircle className="h-5 w-5 text-green-600" />
          <div>
            <div className="font-semibold">P<PERSON><PERSON><PERSON><PERSON>!</div>
            <div className="text-sm text-gray-600">
              <PERSON><PERSON><PERSON> kampanja je promovirana.
            </div>
          </div>
        </div>,
        {
          duration: 6000,
        }
      );
    } else if (payment === 'cancelled') {
      toast.error(
        <div className="flex items-center gap-2">
          <XCircle className="h-5 w-5 text-red-600" />
          <div>
            <div className="font-semibold">Plaćanje otkazano</div>
            <div className="text-sm text-gray-600">
              Možete pokušati ponovo kad god želite.
            </div>
          </div>
        </div>,
        {
          duration: 5000,
        }
      );
    }
  }, [payment, sessionId]);

  // Remove payment params from URL without reloading
  useEffect(() => {
    if (payment && typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      url.searchParams.delete('payment');
      url.searchParams.delete('session_id');
      window.history.replaceState({}, '', url.toString());
    }
  }, [payment]);

  return null;
}