import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Get display name for a user profile
 * For influencers: Uses public_display_name if available, otherwise falls back to 'Ime i prezime skriveno'
 * For business users: Uses public_display_name if available, otherwise falls back to full_name (brand name), then username
 * @param profile - User profile object with public_display_name, full_name, username, and user_type
 * @returns Display name string
 */
export function getDisplayName(
  profile:
    | {
        public_display_name?: string | null;
        full_name?: string | null;
        username?: string | null;
        user_type?: string | null;
      }
    | null
    | undefined
): string {
  // Check if profile exists
  if (!profile) {
    return 'Ime i prezime skriveno';
  }

  // Use public_display_name if available and not empty
  if (profile.public_display_name && profile.public_display_name.trim()) {
    return profile.public_display_name.trim();
  }

  // For business users, fall back to full_name (brand name), then username
  if (profile.user_type === 'business') {
    if (profile.full_name && profile.full_name.trim()) {
      return profile.full_name.trim();
    }
    if (profile.username && profile.username.trim()) {
      return profile.username.trim();
    }
  }

  // For influencers, fall back to hidden name message
  return 'Ime i prezime skriveno';
}

/**
 * Get initials from a name for avatar fallback
 * @param name - Name string or null
 * @returns Initials string (max 2 characters)
 */
export function getInitials(name: string | null | undefined): string {
  if (!name || !name.trim()) return '?';

  // Special case for hidden name
  if (name.trim() === 'Ime i prezime skriveno') return 'IP';

  return name
    .trim()
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
}
