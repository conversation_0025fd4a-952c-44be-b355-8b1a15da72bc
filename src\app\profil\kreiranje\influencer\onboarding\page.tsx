'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';
import { createPricingPackage } from '@/lib/pricing-packages';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { LogOut } from 'lucide-react';

// Import step components
import { UsernameStep } from '@/components/onboarding/steps/UsernameStep';
import { AgeStep } from '@/components/onboarding/steps/AgeStep';
import { GenderStep } from '@/components/onboarding/steps/GenderStep';
import { CategoriesStep } from '@/components/onboarding/steps/CategoriesStep';
import { InfluencerCountryStep } from '@/components/onboarding/steps/InfluencerCountryStep';
import { CityStep } from '@/components/onboarding/steps/CityStep';
import { BioStep } from '@/components/onboarding/steps/BioStep';

// Import existing components for social media and packages
import { SocialMediaStep } from '@/components/onboarding/SocialMediaStep';
import { PackageStep } from '@/components/onboarding/PackageStep';

interface OnboardingData {
  username: string;
  age: number | null;
  gender: string;
  categories: number[];
  country: string;
  city: string;
  bio: string;
  socialMedia: Array<{
    platform: 'instagram' | 'tiktok' | 'youtube';
    handle: string;
    followers: number;
  }>;
  packages: Array<{
    platform_id: number;
    content_type_id: number;
    quantity: number;
    video_duration?: string;
    price: number;
    platform_name: string;
    platform_icon: string;
    content_type_name: string;
    generated_name: string;
  }>;
}

const ONBOARDING_STEPS = [
  { id: 1, title: 'Username', description: 'Izaberite jedinstveni username' },
  { id: 2, title: 'Godine', description: 'Unesite vaše godine' },
  { id: 3, title: 'Pol', description: 'Izaberite vaš pol' },
  { id: 4, title: 'Kategorije', description: 'Izaberite kategorije sadržaja' },
  { id: 5, title: 'Država', description: 'Izaberite vašu državu' },
  { id: 6, title: 'Grad', description: 'Unesite vaš grad (opcionalno)' },
  { id: 7, title: 'Biografija', description: 'Opišite sebe (opcionalno)' },
  { id: 8, title: 'Društvene mreže', description: 'Dodajte vaše profile' },
  { id: 9, title: 'Paketi', description: 'Kreirajte pakete usluga' },
];

export default function InfluencerOnboardingPage() {
  const { user, signOut } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [onboardingData, setOnboardingData] = useState<Partial<OnboardingData>>(
    {
      username: '',
      age: null,
      gender: '',
      categories: [],
      country: '',
      city: '',
      bio: '',
      socialMedia: [],
      packages: [],
    }
  );

  // Load user's onboarding progress
  useEffect(() => {
    const loadOnboardingProgress = async () => {
      if (!user) return;

      try {
        // Load profile data
        const { data: profile, error } = await supabase
          .from('profiles')
          .select(
            'onboarding_step, onboarding_completed, username, age, gender, country, city, bio'
          )
          .eq('id', user.id)
          .single();

        // Load categories if they exist
        const { data: categories } = await supabase
          .from('influencer_categories')
          .select('category_id')
          .eq('influencer_id', user.id);

        // Load social media if they exist
        const { data: socialMedia } = await supabase
          .from('influencer_platforms')
          .select('platform_id, handle, followers_count')
          .eq('influencer_id', user.id)
          .eq('is_active', true);

        if (error) {
          console.error('Error loading onboarding progress:', error);
          return;
        }

        if (profile) {
          // If onboarding is completed, redirect to dashboard
          if (profile.onboarding_completed) {
            router.push('/dashboard/influencer');
            return;
          }

          // Set current step from URL parameter or database
          const stepFromUrl = searchParams.get('step');
          if (stepFromUrl) {
            const stepNumber = parseInt(stepFromUrl, 10);
            if (stepNumber >= 1 && stepNumber <= ONBOARDING_STEPS.length) {
              setCurrentStep(stepNumber);
            } else if (profile.onboarding_step) {
              setCurrentStep(profile.onboarding_step);
            }
          } else if (profile.onboarding_step) {
            setCurrentStep(profile.onboarding_step);
          }

          // Pre-populate form data if exists
          const socialMediaData =
            socialMedia?.map(sm => {
              const platformMap: {
                [key: number]: 'instagram' | 'tiktok' | 'youtube';
              } = {
                1: 'instagram',
                2: 'tiktok',
                3: 'youtube',
              };
              return {
                platform: platformMap[sm.platform_id] || 'instagram',
                handle: sm.handle,
                followers: sm.followers_count,
              };
            }) || [];

          setOnboardingData(prev => ({
            ...prev,
            username: profile.username || '',
            age: profile.age || null,
            gender: profile.gender || '',
            categories: categories?.map(c => c.category_id) || [],
            country: profile.country || '',
            city: profile.city || '',
            bio: profile.bio || '',
            socialMedia: socialMediaData,
          }));
        }
      } catch (error) {
        console.error('Error loading onboarding progress:', error);
      } finally {
        setIsInitialized(true);
      }
    };

    loadOnboardingProgress();
  }, [user, router]);

  const nextStep = async () => {
    if (currentStep < ONBOARDING_STEPS.length) {
      const newStep = currentStep + 1;
      setCurrentStep(newStep);
      await saveOnboardingProgress(newStep);
    }
  };

  const prevStep = async () => {
    if (currentStep > 1) {
      const newStep = currentStep - 1;
      setCurrentStep(newStep);
      await saveOnboardingProgress(newStep);
    }
  };

  const saveOnboardingProgress = async (step: number) => {
    if (!user) return;

    try {
      // Prepare data to save based on current step and onboarding data
      const updateData: any = {
        onboarding_step: step,
        updated_at: new Date().toISOString(),
      };

      // Save data progressively based on completed steps
      if (step >= 1 && onboardingData.username) {
        updateData.username = onboardingData.username;
      }
      if (step >= 2 && onboardingData.age) {
        updateData.age = onboardingData.age;
      }
      if (step >= 3 && onboardingData.gender) {
        updateData.gender = onboardingData.gender;
      }
      if (step >= 5 && onboardingData.country) {
        updateData.country = onboardingData.country;
      }
      if (step >= 6 && onboardingData.city) {
        updateData.city = onboardingData.city;
      }
      if (step >= 7 && onboardingData.bio) {
        updateData.bio = onboardingData.bio;
      }

      const { error } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', user.id);

      if (error) {
        console.error('Error saving onboarding progress:', error);
      }

      // Save categories after step 4
      if (
        step >= 4 &&
        onboardingData.categories &&
        onboardingData.categories.length > 0
      ) {
        await saveCategoriesData();
      }

      // Save social media after step 8
      if (
        step >= 8 &&
        onboardingData.socialMedia &&
        onboardingData.socialMedia.length > 0
      ) {
        await saveSocialMediaData();
      }
    } catch (error) {
      console.error('Error saving onboarding progress:', error);
    }
  };

  const saveCategoriesData = async () => {
    if (
      !user ||
      !onboardingData.categories ||
      onboardingData.categories.length === 0
    )
      return;

    try {
      // Ensure influencer record exists first
      await ensureInfluencerRecord();

      // Delete existing categories first
      await supabase
        .from('influencer_categories')
        .delete()
        .eq('influencer_id', user.id);

      // Insert new categories
      const categoryInserts = onboardingData.categories.map(categoryId => ({
        influencer_id: user.id,
        category_id: categoryId,
        is_primary: false,
      }));

      const { error } = await supabase
        .from('influencer_categories')
        .insert(categoryInserts);

      if (error) {
        console.error('Error saving categories:', error);
      }
    } catch (error) {
      console.error('Error saving categories:', error);
    }
  };

  const saveSocialMediaData = async () => {
    if (
      !user ||
      !onboardingData.socialMedia ||
      onboardingData.socialMedia.length === 0
    )
      return;

    try {
      // Ensure influencer record exists first
      await ensureInfluencerRecord();

      // Delete existing social media first
      await supabase
        .from('influencer_platforms')
        .delete()
        .eq('influencer_id', user.id);

      // Insert new social media
      const platformMapping: { [key: string]: number } = {
        instagram: 1,
        tiktok: 2,
        youtube: 3,
      };

      const socialMediaInserts = onboardingData.socialMedia.map(social => ({
        influencer_id: user.id,
        platform_id: platformMapping[social.platform],
        handle: social.handle,
        followers_count: social.followers,
        is_active: true,
      }));

      const { error } = await supabase
        .from('influencer_platforms')
        .insert(socialMediaInserts);

      if (error) {
        console.error('Error saving social media:', error);
      }
    } catch (error) {
      console.error('Error saving social media:', error);
    }
  };

  const ensureInfluencerRecord = async () => {
    if (!user) return;

    try {
      // Check if influencer record exists
      const { data: existingInfluencer, error: selectError } = await supabase
        .from('influencers')
        .select('id')
        .eq('id', user.id)
        .single();

      // If error is PGRST116, it means no record found, which is expected
      if (selectError && selectError.code !== 'PGRST116') {
        console.error('Error checking influencer record:', selectError);
        return;
      }

      if (!existingInfluencer) {
        // Create influencer record with only required fields
        const { error: influencerError } = await supabase
          .from('influencers')
          .insert({
            id: user.id,
            // Only include columns that exist in the table
            is_verified: false,
          });

        if (influencerError) {
          console.error('Error creating influencer record:', influencerError);
        }
      }
    } catch (error) {
      console.error('Error ensuring influencer record:', error);
    }
  };

  const updateData = (field: keyof OnboardingData, value: any) => {
    setOnboardingData(prev => ({ ...prev, [field]: value }));
  };

  const handleFinish = async () => {
    if (
      !user ||
      !onboardingData.username ||
      !onboardingData.age ||
      onboardingData.categories.length === 0 ||
      !onboardingData.country
    ) {
      alert('Molimo popunite sva obavezna polja');
      return;
    }

    setIsLoading(true);

    try {
      // 1. Update profile with basic info
      const profileUpdates: any = {
        username: onboardingData.username,
        age: onboardingData.age,
        gender: onboardingData.gender,
        country: onboardingData.country,
        user_type: 'influencer',
        profile_completed: true,
        onboarding_completed: true,
        onboarding_completed_at: new Date().toISOString(),
        onboarding_step: ONBOARDING_STEPS.length,
      };

      if (onboardingData.bio) {
        profileUpdates.bio = onboardingData.bio;
      }

      if (onboardingData.city) {
        profileUpdates.city = onboardingData.city;
      }

      const { error: profileError } = await supabase
        .from('profiles')
        .update(profileUpdates)
        .eq('id', user.id);

      if (profileError) {
        throw new Error('Failed to update profile: ' + profileError.message);
      }

      // 2. Ensure influencer record exists (categories and social media are already saved during onboarding)
      await ensureInfluencerRecord();

      // 3. Save packages
      if (onboardingData.packages && onboardingData.packages.length > 0) {
        for (const pkg of onboardingData.packages) {
          const result = await createPricingPackage(user.id, {
            platform_id: pkg.platform_id,
            content_type_id: pkg.content_type_id,
            price: pkg.price,
            quantity: pkg.quantity || 1,
            video_duration: pkg.video_duration,
          });

          if (result.error) {
            throw new Error(
              'Failed to create package: ' + result.error.message
            );
          }
        }
      }

      // Redirect to dashboard
      router.push('/dashboard/influencer');
    } catch (error) {
      console.error('Error saving onboarding data:', error);
      alert('Došlo je do greške. Pokušajte ponovo.');
    } finally {
      setIsLoading(false);
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <UsernameStep
            value={onboardingData.username || ''}
            onChange={value => updateData('username', value)}
            onNext={nextStep}
          />
        );
      case 2:
        return (
          <AgeStep
            value={onboardingData.age}
            onChange={value => updateData('age', value)}
            onNext={nextStep}
            onBack={prevStep}
          />
        );
      case 3:
        return (
          <GenderStep
            value={onboardingData.gender || ''}
            onChange={value => updateData('gender', value)}
            onNext={nextStep}
            onBack={prevStep}
          />
        );
      case 4:
        return (
          <CategoriesStep
            value={onboardingData.categories || []}
            onChange={value => updateData('categories', value)}
            onNext={nextStep}
            onBack={prevStep}
          />
        );
      case 5:
        return (
          <InfluencerCountryStep
            value={onboardingData.country || ''}
            onChange={value => updateData('country', value)}
            onNext={nextStep}
            onBack={prevStep}
          />
        );
      case 6:
        return (
          <CityStep
            value={onboardingData.city || ''}
            onChange={value => updateData('city', value)}
            onNext={nextStep}
            onBack={prevStep}
          />
        );
      case 7:
        return (
          <BioStep
            value={onboardingData.bio || ''}
            onChange={value => updateData('bio', value)}
            onNext={nextStep}
            onBack={prevStep}
          />
        );
      case 8:
        return (
          <SocialMediaStep
            socialMedia={onboardingData.socialMedia || []}
            onUpdate={socialMedia => updateData('socialMedia', socialMedia)}
            onNext={nextStep}
            onBack={prevStep}
          />
        );
      case 9:
        return (
          <PackageStep
            packages={onboardingData.packages || []}
            onUpdate={packages => updateData('packages', packages)}
            onFinish={handleFinish}
            onBack={prevStep}
            isLoading={isLoading}
          />
        );
      default:
        return null;
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push('/');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const progressPercentage = (currentStep / ONBOARDING_STEPS.length) * 100;

  // Show loading while initializing
  if (!isInitialized) {
    return (
      <div className="min-h-screen bg-instagram-story relative overflow-hidden flex items-center justify-center">
        {/* Background decorative elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/5"></div>
        <div className="absolute top-10 right-10 w-64 h-64 bg-white/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-10 left-10 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>

        <div className="relative z-10 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white/60 mx-auto mb-4"></div>
          <p className="text-white/80">Učitavanje onboarding procesa...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-instagram-story relative overflow-hidden flex flex-col">
      {/* Background decorative elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/5"></div>
      <div className="absolute top-10 right-10 w-64 h-64 bg-white/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-10 left-10 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>

      {/* Navbar */}
      <nav className="relative z-10 border-b border-white/20 backdrop-blur-sm bg-white/10 px-4 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center border border-white/30">
              <span className="text-white font-bold text-lg">🔗</span>
            </div>
            <span className="text-xl font-bold text-white">INFLUEXUS</span>
          </div>

          {/* Logout Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleSignOut}
            className="flex items-center space-x-2 border-white/30 text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm"
          >
            <LogOut className="h-4 w-4" />
            <span>Odjava</span>
          </Button>
        </div>
      </nav>

      {/* Main Content */}
      <div className="relative z-10 flex-1 flex items-center justify-center px-4 py-8">
        <div className="w-full max-w-2xl">
          {/* Progress bar */}
          <div className="mb-8">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-white">
                Korak {currentStep} od {ONBOARDING_STEPS.length}
              </span>
              <span className="text-sm text-white/70">
                {Math.round(progressPercentage)}% završeno
              </span>
            </div>
            <Progress value={progressPercentage} className="h-2 bg-white/20" />
          </div>

          {/* Current step */}
          <Card className="glass-instagram border-white/20 shadow-2xl">
            <CardContent className="p-8">{renderCurrentStep()}</CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
