'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import Footer from '@/components/footer';

export default function InfluencerPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#E02F75] via-[#6700A3] to-[#050C38] relative overflow-hidden">
      {/* Header */}
      <header className="relative z-50 p-6">
        <div className="container mx-auto flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center border border-white/30">
              <span className="text-white font-bold text-lg">🔗</span>
            </div>
            <span className="text-xl font-bold text-white">INFLUEXUS</span>
          </Link>
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              className="text-white hover:bg-white/10"
              asChild
            >
              <Link href="/prijava">Prijava</Link>
            </Button>
            <Button
              className="bg-white text-purple-700 hover:bg-white/90"
              asChild
            >
              <Link href="/registracija">Registracija</Link>
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 py-20 px-4">
        <div className="container mx-auto max-w-4xl">
          {/* Page Title */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Za
              <span className="bg-gradient-to-r from-pink-400 to-purple-400 bg-clip-text text-transparent">
                {' '}
                influencere
              </span>
            </h1>
            <p className="text-xl text-white/80 max-w-2xl mx-auto">
              Otkrijte kako da monetizujete vašu publiku kroz sigurne i
              profitabilne saradnje
            </p>
          </div>

          {/* Hero Section */}
          <section className="mb-20">
            <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-12 border border-white/20 text-center">
              <div className="w-24 h-24 bg-gradient-to-r from-pink-400 to-purple-500 rounded-3xl flex items-center justify-center mx-auto mb-8">
                <span className="text-4xl">📱</span>
              </div>
              <h2 className="text-3xl font-bold text-white mb-6">
                Pretvorite vašu publiku u stabilni prihod
              </h2>
              <p className="text-xl text-white/80 max-w-3xl mx-auto">
                INFLUEXUS povezuje vas sa brendovima koji traže upravo vaš tip
                sadržaja. Bez stresa oko naplate, bez pregovora - samo sigurni
                poslovi sa garantovanim plaćanjem.
              </p>
            </div>
          </section>

          {/* Kako funkcioniše proces */}
          <section className="mb-20">
            <h2 className="text-3xl font-bold text-white mb-12 text-center">
              Vaš put do uspješne saradnje
            </h2>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="bg-white/10 backdrop-blur-sm border-white/20 text-white">
                <CardHeader>
                  <div className="w-12 h-12 bg-gradient-to-r from-pink-400 to-rose-500 rounded-xl flex items-center justify-center mb-4">
                    <span className="text-2xl">👤</span>
                  </div>
                  <CardTitle>1. Napravite profil</CardTitle>
                  <CardDescription className="text-white/70">
                    Pokažite vašu publiku, statistike i prethodne radove
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="text-sm text-white/80 space-y-2">
                    <li>• Dodajte vaše društvene mreže</li>
                    <li>• Postavite vaše cene po objavi</li>
                    <li>• Opišite vašu publiku i nišu</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-white/10 backdrop-blur-sm border-white/20 text-white">
                <CardHeader>
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-indigo-500 rounded-xl flex items-center justify-center mb-4">
                    <span className="text-2xl">🎯</span>
                  </div>
                  <CardTitle>2. Pronađite kampanje</CardTitle>
                  <CardDescription className="text-white/70">
                    Pregledajte dostupne kampanje koje odgovaraju vašoj niši
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="text-sm text-white/80 space-y-2">
                    <li>• Filteri po kategoriji i budžetu</li>
                    <li>• Jasni uslovi i rokovi</li>
                    <li>• Transparentne cijene</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-white/10 backdrop-blur-sm border-white/20 text-white">
                <CardHeader>
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-xl flex items-center justify-center mb-4">
                    <span className="text-2xl">💡</span>
                  </div>
                  <CardTitle>3. Pošaljite prijedlog</CardTitle>
                  <CardDescription className="text-white/70">
                    Prezentirajte vašu ideju i predložite pristup
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="text-sm text-white/80 space-y-2">
                    <li>• Opišite vašu kreativnu viziju</li>
                    <li>• Postavite vašu cijenu</li>
                    <li>• Predložite timeline</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-white/10 backdrop-blur-sm border-white/20 text-white">
                <CardHeader>
                  <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-teal-500 rounded-xl flex items-center justify-center mb-4">
                    <span className="text-2xl">📱</span>
                  </div>
                  <CardTitle>4. Kreirajte sadržaj</CardTitle>
                  <CardDescription className="text-white/70">
                    Proizvedite kvalitetan sadržaj prema dogovoru
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="text-sm text-white/80 space-y-2">
                    <li>• Pratite brand guideline</li>
                    <li>• Komunicirajte sa brendom</li>
                    <li>• Poštujte rokove</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-white/10 backdrop-blur-sm border-white/20 text-white">
                <CardHeader>
                  <div className="w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center mb-4">
                    <span className="text-2xl">📈</span>
                  </div>
                  <CardTitle>5. Objavite i izvjestite</CardTitle>
                  <CardDescription className="text-white/70">
                    Podijelite sadržaj i pošaljite rezultate
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="text-sm text-white/80 space-y-2">
                    <li>• Objavite prema dogovoru</li>
                    <li>• Pošaljite screenshot</li>
                    <li>• Priložite analitiku</li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-white/10 backdrop-blur-sm border-white/20 text-white">
                <CardHeader>
                  <div className="w-12 h-12 bg-gradient-to-r from-emerald-400 to-green-500 rounded-xl flex items-center justify-center mb-4">
                    <span className="text-2xl">💸</span>
                  </div>
                  <CardTitle>6. Primite plaćanje</CardTitle>
                  <CardDescription className="text-white/70">
                    Automatsko i sigurno plaćanje
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="text-sm text-white/80 space-y-2">
                    <li>• Novac oslobođen odmah</li>
                    <li>• Bez čekanja računa</li>
                    <li>• Direktno na vaš račun</li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </section>

          {/* Prednosti za influencere */}
          <section className="mb-20">
            <h2 className="text-3xl font-bold text-white mb-12 text-center">
              Zašto influenceri biraju
              <span className="bg-gradient-to-r from-pink-400 to-purple-400 bg-clip-text text-transparent">
                {' '}
                INFLUEXUS?
              </span>
            </h2>

            <div className="grid md:grid-cols-2 gap-8">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                <div className="w-16 h-16 bg-gradient-to-r from-green-400 to-emerald-500 rounded-2xl flex items-center justify-center mb-6">
                  <span className="text-3xl">💰</span>
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">
                  Garantovano plaćanje
                </h3>
                <p className="text-white/80 text-lg">
                  Novac je siguran u escrow sistemu. Čim završite posao i brend
                  odobri, sredstva se automatski oslobađaju na vaš račun.
                </p>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-2xl flex items-center justify-center mb-6">
                  <span className="text-3xl">🎯</span>
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">
                  Relevantne kampanje
                </h3>
                <p className="text-white/80 text-lg">
                  Naš algoritam povezuje vas samo sa brendovima koji odgovaraju
                  vašoj publici i niši. Manje vremena traženja, više kvalitetnih
                  poslova.
                </p>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-400 to-pink-500 rounded-2xl flex items-center justify-center mb-6">
                  <span className="text-3xl">⚡</span>
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">
                  Brže do dogovora
                </h3>
                <p className="text-white/80 text-lg">
                  Standardizovani procesi i jasni uslovi čine pregovore bržim i
                  efikasnijim. Fokusirajte se na kreativnost, ne na
                  papirologiju.
                </p>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                <div className="w-16 h-16 bg-gradient-to-r from-orange-400 to-red-500 rounded-2xl flex items-center justify-center mb-6">
                  <span className="text-3xl">📊</span>
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">
                  Analitika i praćenje
                </h3>
                <p className="text-white/80 text-lg">
                  Detaljno praćenje performansi vaših kampanja. Koristite
                  podatke da poboljšate svoj sadržaj i povećate zaradu.
                </p>
              </div>
            </div>
          </section>

          {/* Pricing tiers */}
          <section className="mb-20">
            <h2 className="text-3xl font-bold text-white mb-12 text-center">
              Jednostavno cijenje
            </h2>

            <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-8 border border-white/20 text-center">
              <div className="max-w-2xl mx-auto">
                <div className="w-20 h-20 bg-gradient-to-r from-green-400 to-teal-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <span className="text-3xl">📈</span>
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">
                  Samo 10% provizije
                </h3>
                <p className="text-white/80 text-lg mb-6">
                  Zadržavamo samo 10% od vaše zarade za korištenje platforme.
                  Bez skrivenih troškova, bez mesečnih naknada.
                </p>
                <div className="bg-white/5 rounded-xl p-6 border border-white/10">
                  <p className="text-white/70 text-sm mb-2">Primer:</p>
                  <p className="text-2xl font-bold text-white">
                    Zaradili ste <span className="text-green-400">100€</span> →
                    Dobijate <span className="text-green-400">90€</span>
                  </p>
                </div>
              </div>
            </div>
          </section>

          {/* CTA Section */}
          <section className="text-center">
            <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-12 border border-white/20">
              <h2 className="text-3xl font-bold text-white mb-6">
                Spremni da počnete zarađivati?
              </h2>
              <p className="text-xl text-white/80 mb-8 max-w-2xl mx-auto">
                Pridružite se hiljadama influencera koji već koriste INFLUEXUS
                za sigurne i profitabilne saradnje
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Button
                  className="bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white font-bold text-lg px-8 py-4 rounded-xl shadow-xl"
                  asChild
                >
                  <Link href="/registracija">Registrujte se besplatno</Link>
                </Button>
                <Button
                  variant="outline"
                  className="border-white/30 text-white hover:bg-white/10 font-bold text-lg px-8 py-4 rounded-xl"
                  asChild
                >
                  <Link href="/kako-funkcionise">Saznajte o sigurnosti</Link>
                </Button>
              </div>
            </div>
          </section>
        </div>
      </main>

      <Footer />
    </div>
  );
}
