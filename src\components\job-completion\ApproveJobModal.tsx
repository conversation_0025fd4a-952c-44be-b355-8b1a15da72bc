'use client';

import { useState } from 'react';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Star } from 'lucide-react';
import { toast } from 'sonner';
import { approveJobCompletion } from '@/lib/job-completions';

interface ApproveJobModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  jobCompletionId: string;
  influencerName: string;
}

export function ApproveJobModal({
  isOpen,
  onClose,
  onSuccess,
  jobCompletionId,
  influencerName,
}: ApproveJobModalProps) {
  const [rating, setRating] = useState(0);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [reviewNotes, setReviewNotes] = useState('');
  const [businessNotes, setBusinessNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (rating === 0) {
      toast.error('Molimo odaberite ocjenu');
      return;
    }

    setIsSubmitting(true);
    try {
      const result = await approveJobCompletion(
        jobCompletionId,
        rating,
        reviewNotes.trim() || undefined,
        businessNotes.trim() || undefined
      );

      if (result.error) {
        console.error('Error approving job completion:', result.error);
        toast.error('Greška pri odobravanju rada');
        return;
      }

      toast.success('Rad je uspješno odobren i ocjena je poslana');
      onSuccess();
      onClose();

      // Reset form
      setRating(0);
      setReviewNotes('');
      setBusinessNotes('');
    } catch (error) {
      console.error('Unexpected error:', error);
      toast.error('Dogodila se neočekivana greška');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px] relative overflow-hidden bg-gradient-to-br from-purple-50/95 via-pink-50/90 to-purple-100/95 dark:from-purple-950/95 dark:via-pink-950/90 dark:to-purple-900/95 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
        <div className="relative">
          <DialogHeader>
            <DialogTitle className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
              Odobri rad i ocijeni influencera
            </DialogTitle>
            <DialogDescription className="text-gray-700 dark:text-gray-300">
              Odobravate rad od <strong>{influencerName}</strong>. Molimo
              ostavite ocjenu i komentar.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6 py-4">
            {/* Rating */}
            <div className="space-y-2">
              <Label className="text-gray-900 dark:text-gray-100">
                Ocjena (1-5 zvjezdica) *
              </Label>
              <div className="flex items-center gap-1 bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                {[1, 2, 3, 4, 5].map(star => (
                  <button
                    key={star}
                    type="button"
                    className="p-1 hover:scale-110 transition-transform"
                    onMouseEnter={() => setHoveredRating(star)}
                    onMouseLeave={() => setHoveredRating(0)}
                    onClick={() => setRating(star)}
                  >
                    <Star
                      className={`h-8 w-8 ${
                        star <= (hoveredRating || rating)
                          ? 'fill-yellow-400 text-yellow-400'
                          : 'text-gray-300'
                      }`}
                    />
                  </button>
                ))}
                {rating > 0 && (
                  <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">
                    {rating} od 5 zvjezdica
                  </span>
                )}
              </div>
            </div>

            {/* Review Notes */}
            <div className="space-y-2">
              <Label
                htmlFor="reviewNotes"
                className="text-gray-900 dark:text-gray-100"
              >
                Javni komentar (vidljiv influenceru)
              </Label>
              <Textarea
                id="reviewNotes"
                placeholder="Ostavite komentar o radu influencera..."
                value={reviewNotes}
                onChange={e => setReviewNotes(e.target.value)}
                rows={3}
                className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50 dark:border-purple-800/30"
              />
            </div>

            {/* Business Notes */}
            <div className="space-y-2">
              <Label
                htmlFor="businessNotes"
                className="text-gray-900 dark:text-gray-100"
              >
                Privatne napomene (samo za vas)
              </Label>
              <Textarea
                id="businessNotes"
                placeholder="Privatne napomene o ovom poslu..."
                value={businessNotes}
                onChange={e => setBusinessNotes(e.target.value)}
                rows={2}
                className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50 dark:border-purple-800/30"
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
              className="bg-white/70 dark:bg-gray-800/50 hover:bg-white dark:hover:bg-gray-800/70 border-purple-200/50 dark:border-purple-700/50"
            >
              Otkaži
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting || rating === 0}
              className="bg-gradient-to-r from-green-500 via-emerald-500 to-green-600 hover:from-green-600 hover:via-emerald-600 hover:to-green-700 text-white border-0"
            >
              {isSubmitting ? 'Odobrava se...' : 'Odobri rad'}
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
}
