import React from 'react';
import { cn } from '@/lib/utils';

interface GradientBackgroundProps {
  variant?: 'primary' | 'secondary' | 'story' | 'subtle' | 'warm';
  children: React.ReactNode;
  className?: string;
  decorativeElements?: boolean;
}

const GradientBackground: React.FC<GradientBackgroundProps> = ({
  variant = 'primary',
  children,
  className,
  decorativeElements = true,
}) => {
  const gradientClasses = {
    primary: 'bg-instagram-primary',
    secondary: 'bg-instagram-secondary',
    story: 'bg-instagram-story',
    subtle: 'bg-instagram-subtle',
    warm: 'bg-instagram-warm',
  };

  return (
    <div
      className={cn(
        'min-h-screen relative overflow-hidden flex flex-col',
        gradientClasses[variant],
        className
      )}
    >
      {decorativeElements && (
        <>
          {/* Background decorative elements */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/5"></div>
          <div className="absolute top-10 right-10 w-64 h-64 bg-white/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-10 left-10 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>
        </>
      )}
      {children}
    </div>
  );
};

export { GradientBackground };
