'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { getInfluencer } from '@/lib/profiles';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Loader2, Crown } from 'lucide-react';

export default function InfluencerDashboardPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [influencer, setInfluencer] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [hasActiveSubscription, setHasActiveSubscription] = useState(false);

  useEffect(() => {
    if (user) {
      loadInfluencer();
      checkSubscription();
    }
  }, [user]);

  const loadInfluencer = async () => {
    try {
      setLoading(true);
      const { data, error } = await getInfluencer(user!.id);

      if (error || !data) {
        // Influencer profile doesn't exist, redirect to profile creation
        router.push('/profil/kreiranje/influencer');
        return;
      }

      setInfluencer(data);
    } catch (err) {
      console.error('Error loading influencer data:', err);
    } finally {
      setLoading(false);
    }
  };

  const checkSubscription = async () => {
    if (user) {
      try {
        const { supabase } = await import('@/lib/supabase');
        const { data: subscription } = await supabase
          .from('user_subscriptions')
          .select('status')
          .eq('user_id', user.id)
          .eq('user_type', 'influencer')
          .eq('status', 'active')
          .single();
        
        setHasActiveSubscription(!!subscription);
      } catch (error) {
        console.error('Error checking subscription:', error);
        setHasActiveSubscription(false);
      }
    }
  };

  if (loading) {
    return (
      <DashboardLayout requiredUserType="influencer">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  const isPremium = hasActiveSubscription;

  return (
    <DashboardLayout requiredUserType="influencer">
      <div>
        <div className="flex items-center gap-3 mb-4">
          <h1 className="text-3xl font-bold text-foreground">
            Dobrodošli, {influencer?.profiles?.username || 'Influencer'}!
          </h1>
          <div
            className={`flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium ${
              isPremium
                ? 'bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-700 border border-yellow-200'
                : 'bg-gray-100 text-gray-700 border border-gray-200'
            }`}
          >
            {isPremium && <Crown className="h-4 w-4" />}
            {isPremium ? 'Premium' : 'Free'} korisnik
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
