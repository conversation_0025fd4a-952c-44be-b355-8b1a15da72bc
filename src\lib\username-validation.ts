import { supabase } from './supabase';

export interface UsernameValidationResult {
  isValid: boolean;
  isAvailable: boolean;
  message: string;
  type: 'success' | 'error' | 'warning';
}

/**
 * Validate username format
 */
export function validateUsernameFormat(username: string): {
  isValid: boolean;
  message: string;
} {
  // Check length
  if (username.length < 3) {
    return {
      isValid: false,
      message: 'Username mora imati najmanje 3 karaktera',
    };
  }

  if (username.length > 20) {
    return {
      isValid: false,
      message: 'Username može imati maksimalno 20 karaktera',
    };
  }

  // Check format (only letters, numbers, and underscore)
  const usernameRegex = /^[a-zA-Z0-9_]+$/;
  if (!usernameRegex.test(username)) {
    return {
      isValid: false,
      message: 'Username može sadržavati samo slova, brojeve i _',
    };
  }

  // Check if starts with underscore or number
  if (username.startsWith('_') || /^[0-9]/.test(username)) {
    return {
      isValid: false,
      message: 'Username mora počinjati slovom',
    };
  }

  // Check for consecutive underscores
  if (username.includes('__')) {
    return {
      isValid: false,
      message: 'Username ne može imati uzastopne _',
    };
  }

  // Check for reserved words
  const reservedWords = [
    'admin',
    'administrator',
    'root',
    'api',
    'www',
    'mail',
    'email',
    'support',
    'help',
    'info',
    'contact',
    'about',
    'terms',
    'privacy',
    'login',
    'register',
    'signup',
    'signin',
    'logout',
    'dashboard',
    'profile',
    'settings',
    'account',
    'user',
    'users',
    'influencer',
    'business',
    'campaign',
    'campaigns',
    'offer',
    'offers',
    'chat',
    'message',
    'messages',
    'notification',
    'notifications',
  ];

  if (reservedWords.includes(username.toLowerCase())) {
    return {
      isValid: false,
      message: 'Ovaj username nije dostupan',
    };
  }

  return {
    isValid: true,
    message: 'Username format je valjan',
  };
}

/**
 * Check if username is available in database
 */
export async function checkUsernameAvailability(
  username: string,
  excludeUserId?: string
): Promise<{ isAvailable: boolean; message: string }> {
  try {
    let query = supabase
      .from('profiles')
      .select('id, username')
      .eq('username', username);

    // Exclude current user if updating
    if (excludeUserId) {
      query = query.neq('id', excludeUserId);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error checking username availability:', error);
      return {
        isAvailable: false,
        message: 'Greška pri proveravanju dostupnosti username-a',
      };
    }

    const isAvailable = !data || data.length === 0;

    return {
      isAvailable,
      message: isAvailable ? 'Username je dostupan' : 'Username je već zauzet',
    };
  } catch (error) {
    console.error('Error checking username availability:', error);
    return {
      isAvailable: false,
      message: 'Greška pri proveravanju dostupnosti username-a',
    };
  }
}

/**
 * Complete username validation (format + availability)
 */
export async function validateUsername(
  username: string,
  excludeUserId?: string
): Promise<UsernameValidationResult> {
  // First check format
  const formatValidation = validateUsernameFormat(username);

  if (!formatValidation.isValid) {
    return {
      isValid: false,
      isAvailable: false,
      message: formatValidation.message,
      type: 'error',
    };
  }

  // Then check availability
  const availabilityCheck = await checkUsernameAvailability(
    username,
    excludeUserId
  );

  if (!availabilityCheck.isAvailable) {
    return {
      isValid: false,
      isAvailable: false,
      message: availabilityCheck.message,
      type: 'error',
    };
  }

  return {
    isValid: true,
    isAvailable: true,
    message: 'Username je dostupan',
    type: 'success',
  };
}

/**
 * Generate username suggestions based on full name
 */
export function generateUsernameSuggestions(fullName: string): string[] {
  const cleanName = fullName
    .toLowerCase()
    .replace(/[^a-z\s]/g, '')
    .trim();

  const parts = cleanName.split(/\s+/).filter(part => part.length > 0);

  if (parts.length === 0) {
    return [];
  }

  const suggestions: string[] = [];

  // First name + last name
  if (parts.length >= 2) {
    suggestions.push(`${parts[0]}_${parts[1]}`);
    suggestions.push(`${parts[0]}${parts[1]}`);
    suggestions.push(`${parts[1]}_${parts[0]}`);
  }

  // First name with numbers
  if (parts[0]) {
    suggestions.push(`${parts[0]}123`);
    suggestions.push(`${parts[0]}_2024`);
    suggestions.push(`${parts[0]}_official`);
  }

  // Initials
  if (parts.length >= 2) {
    const initials = parts.map(part => part[0]).join('');
    suggestions.push(`${initials}_${Math.floor(Math.random() * 1000)}`);
  }

  // Remove duplicates and filter valid ones
  return [...new Set(suggestions)]
    .filter(suggestion => validateUsernameFormat(suggestion).isValid)
    .slice(0, 5);
}
