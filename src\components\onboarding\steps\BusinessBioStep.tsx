'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FileText, ArrowLeft, ArrowRight } from 'lucide-react';

const businessBioSchema = z.object({
  bio: z
    .string()
    .max(500, 'Opis može imati maksimalno 500 karaktera')
    .optional(),
});

type BusinessBioForm = z.infer<typeof businessBioSchema>;

interface BusinessBioStepProps {
  value: string;
  onNext: (bio: string) => void;
  onBack: () => void;
}

export function BusinessBioStep({
  value,
  onNext,
  onBack,
}: BusinessBioStepProps) {
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<BusinessBioForm>({
    resolver: zodResolver(businessBioSchema),
    defaultValues: {
      bio: value,
    },
  });

  const bioValue = watch('bio') || '';

  const onSubmit = async (data: BusinessBioForm) => {
    setIsLoading(true);
    try {
      onNext(data.bio || '');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto">
      <div className="space-y-6">
        <div className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-white/20">
            <FileText className="h-6 w-6 text-white" />
          </div>
          <h2 className="text-2xl font-bold text-white mb-2">
            Opis vašeg brenda
          </h2>
          <p className="text-white/70">
            Opišite čime se bavi vaša firma (opcionalno)
          </p>
        </div>
        <div>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="bio" className="text-white">
                Opis firme
              </Label>
              <Textarea
                id="bio"
                {...register('bio')}
                placeholder="Kratko opišite čime se bavi vaša firma, vaše proizvode ili usluge..."
                className={`min-h-[120px] bg-white/10 border-white/30 text-white placeholder:text-white/50 focus:border-white/50 focus:ring-white/20 ${errors.bio ? 'border-red-400' : ''}`}
                maxLength={500}
              />
              <div className="flex justify-between text-sm text-white/60">
                <span>Možete ovo dodati i kasnije</span>
                <span>{bioValue.length}/500</span>
              </div>
              {errors.bio && (
                <p className="text-sm text-red-300">{errors.bio.message}</p>
              )}
            </div>

            <div className="flex justify-between pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onBack}
                className="flex items-center space-x-2 border-white/30 text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm"
              >
                <ArrowLeft className="h-4 w-4" />
                <span>Nazad</span>
              </Button>

              <Button
                type="submit"
                disabled={isLoading}
                className="flex items-center space-x-2 bg-white text-instagram-purple hover:bg-white/90 font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <span>Dalje</span>
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
