'use client';

import React, { useState, useRef, useCallback } from 'react';
import { Upload, X, Camera, Check, AlertCircle, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent } from '@/components/ui/card';
import {
  uploadAvatarWithCompression,
  type UploadProgress,
  type AvatarUrls,
} from '@/lib/avatar-upload';
import {
  validateImageFile,
  createImagePreview,
  cleanupImagePreview,
  formatFileSize,
} from '@/lib/image-compression';
import { ImageCropper } from '@/components/ui/image-cropper';
import Image from 'next/image';

interface AvatarUploadProps {
  userId: string;
  currentAvatarUrl?: string;
  onUploadComplete?: (avatarUrls: AvatarUrls) => void;
  onUploadError?: (error: string) => void;
  className?: string;
}

export function AvatarUpload({
  userId,
  currentAvatarUrl,
  onUploadComplete,
  onUploadError,
  className = '',
}: AvatarUploadProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [croppedFile, setCroppedFile] = useState<File | null>(null);
  const [croppedPreviewUrl, setCroppedPreviewUrl] = useState<string | null>(
    null
  );
  const [cropperOpen, setCropperOpen] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(
    null
  );
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [compressionStats, setCompressionStats] = useState<{
    originalSize: number;
    totalCompressedSize: number;
    compressionRatio: number;
  } | null>(null);

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle drag and drop on the avatar
  const handleAvatarDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  const handleAvatarDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelection(files[0]);
    }
  }, []);

  const handleFileSelection = (file: File) => {
    setError(null);
    setSuccess(false);

    // Validate file
    const validation = validateImageFile(file);
    if (!validation.isValid) {
      setError(validation.error || 'Neispravna slika');
      return;
    }

    // Clean up previous preview
    if (previewUrl) {
      cleanupImagePreview(previewUrl);
    }

    // Set new file and preview
    setSelectedFile(file);
    const newPreviewUrl = createImagePreview(file);
    setPreviewUrl(newPreviewUrl);

    // Open cropper for user to select area
    setCropperOpen(true);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelection(files[0]);
    }
  };

  const handleCropComplete = (croppedImageFile: File) => {
    setCroppedFile(croppedImageFile);

    // Create preview URL for cropped image
    const croppedUrl = createImagePreview(croppedImageFile);
    setCroppedPreviewUrl(croppedUrl);

    setCropperOpen(false);
  };

  const handleUpload = async () => {
    const fileToUpload = croppedFile || selectedFile;
    if (!fileToUpload) return;

    setIsUploading(true);
    setError(null);
    setUploadProgress(null);

    try {
      const result = await uploadAvatarWithCompression(
        userId,
        fileToUpload,
        progress => {
          setUploadProgress(progress);
        }
      );

      setCompressionStats(result.compressionStats);
      setSuccess(true);
      onUploadComplete?.(result.avatarUrls);

      // Clean up
      setTimeout(() => {
        setSelectedFile(null);
        setCroppedFile(null);
        if (previewUrl) {
          cleanupImagePreview(previewUrl);
          setPreviewUrl(null);
        }
        if (croppedPreviewUrl) {
          cleanupImagePreview(croppedPreviewUrl);
          setCroppedPreviewUrl(null);
        }
        setSuccess(false);
        setUploadProgress(null);
      }, 3000);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Greška pri upload-u';
      setError(errorMessage);
      onUploadError?.(errorMessage);
    } finally {
      setIsUploading(false);
    }
  };

  const handleCancel = () => {
    setSelectedFile(null);
    setCroppedFile(null);
    setError(null);
    setSuccess(false);
    setUploadProgress(null);
    setCropperOpen(false);

    if (previewUrl) {
      cleanupImagePreview(previewUrl);
      setPreviewUrl(null);
    }

    if (croppedPreviewUrl) {
      cleanupImagePreview(croppedPreviewUrl);
      setCroppedPreviewUrl(null);
    }

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {!selectedFile ? (
        // Clickable avatar display
        <div className="flex flex-col items-center space-y-3">
          <div
            className="relative w-32 h-32 rounded-full overflow-hidden bg-gray-100 cursor-pointer transition-all duration-200 hover:scale-105 group"
            onClick={openFileDialog}
            onDragOver={handleAvatarDragOver}
            onDrop={handleAvatarDrop}
          >
            {currentAvatarUrl ? (
              <Image
                src={currentAvatarUrl}
                alt="Current avatar"
                fill
                className="object-cover transition-all duration-200 group-hover:brightness-75"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center transition-all duration-200 group-hover:from-gray-300 group-hover:to-gray-400 dark:group-hover:from-gray-600 dark:group-hover:to-gray-700">
                <Camera className="w-12 h-12 text-gray-400 dark:text-gray-500 transition-all duration-200 group-hover:text-gray-500 dark:group-hover:text-gray-400" />
              </div>
            )}

            {/* Hover overlay */}
            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
              <div className="bg-white/90 dark:bg-gray-900/90 rounded-full p-2">
                <Camera className="w-6 h-6 text-gray-700 dark:text-gray-300" />
              </div>
            </div>
          </div>

          <div className="text-center">
            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
              Trenutna profilna slika
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              Kliknite ili povucite novu sliku da je zamijenite
            </p>
          </div>
        </div>
      ) : (
        // Preview selected/cropped file
        <div className="space-y-4">
          <div className="relative w-32 h-32 mx-auto rounded-full overflow-hidden bg-gray-100">
            <Image
              src={croppedPreviewUrl || previewUrl || '/placeholder.svg'}
              alt="Preview"
              fill
              className="object-cover"
            />
          </div>
          <div className="text-center">
            <p className="font-medium">{selectedFile.name}</p>
            <p className="text-sm text-muted-foreground">
              {formatFileSize(selectedFile.size)}
            </p>
            {croppedFile && (
              <p className="text-xs text-green-600 mt-1">
                ✓ Slika je obrezana i spremna za upload
              </p>
            )}
          </div>
          <div className="flex gap-2 justify-center">
            {!croppedFile && (
              <Button
                variant="outline"
                onClick={() => setCropperOpen(true)}
                disabled={isUploading}
              >
                Obrež sliku
              </Button>
            )}
            <Button
              onClick={handleUpload}
              disabled={isUploading || !croppedFile}
            >
              {isUploading ? 'Upload u toku...' : 'Upload sliku'}
            </Button>
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={isUploading}
            >
              Otkaži
            </Button>
          </div>
        </div>
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/jpg,image/png,image/webp"
        onChange={handleFileInputChange}
        className="hidden"
      />

      {/* Progress */}
      {uploadProgress && (
        <Card>
          <CardContent className="p-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">
                  {uploadProgress.message || 'Upload u toku...'}
                </span>
                <span className="text-sm text-muted-foreground">
                  {uploadProgress.percentage}%
                </span>
              </div>
              <Progress value={uploadProgress.percentage} className="w-full" />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Success message */}
      {success && compressionStats && (
        <Alert>
          <Check className="h-4 w-4" />
          <AlertDescription>
            Slika je uspješno upload-ovana! Kompresija:{' '}
            {compressionStats.compressionRatio}% (sa{' '}
            {formatFileSize(compressionStats.originalSize)} na{' '}
            {formatFileSize(compressionStats.totalCompressedSize)})
          </AlertDescription>
        </Alert>
      )}

      {/* Error message */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Image Cropper Dialog */}
      {previewUrl && (
        <ImageCropper
          isOpen={cropperOpen}
          onClose={() => setCropperOpen(false)}
          imageUrl={previewUrl}
          onCropComplete={handleCropComplete}
          aspectRatio={1} // Square crop
        />
      )}
    </div>
  );
}
