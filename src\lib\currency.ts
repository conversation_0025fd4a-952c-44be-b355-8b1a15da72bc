/**
 * Currency utilities for EUR formatting and conversion
 * Centralized functions for handling EUR currency throughout the application
 */

// Constants
export const DEFAULT_CURRENCY = 'EUR';
export const CURRENCY_SYMBOL = '€';

// Conversion rate from KM to EUR (approximate)
// 1 KM ≈ 0.51 EUR (Bosnia and Herzegovina convertible mark to Euro)
export const KM_TO_EUR_RATE = 0.51;

/**
 * Format a number as EUR currency
 * @param amount - The amount to format
 * @param options - Formatting options
 * @returns Formatted currency string
 */
export function formatEUR(
  amount: number,
  options: {
    showSymbol?: boolean;
    showCurrency?: boolean;
    decimals?: number;
    locale?: string;
  } = {}
): string {
  const {
    showSymbol = true,
    showCurrency = false,
    decimals = 2,
    locale = 'de-DE', // German locale for EUR formatting (1.234,56 €)
  } = options;

  if (isNaN(amount) || amount === null || amount === undefined) {
    return showSymbol ? '0 €' : '0';
  }

  const formattedNumber = new Intl.NumberFormat(locale, {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(amount);

  if (showCurrency) {
    return `${formattedNumber} EUR`;
  }

  if (showSymbol) {
    return `${formattedNumber} €`;
  }

  return formattedNumber;
}

/**
 * Format price for display in components
 * Default formatting for most UI components
 */
export function formatPrice(price: number): string {
  return formatEUR(price, { showSymbol: true });
}

/**
 * Format budget for display
 * Used in campaigns, offers, etc.
 */
export function formatBudget(budget: number): string {
  return formatEUR(budget, { showSymbol: true });
}

/**
 * Convert KM amount to EUR
 * Used for migrating existing data
 */
export function convertKMToEUR(kmAmount: number): number {
  if (isNaN(kmAmount) || kmAmount === null || kmAmount === undefined) {
    return 0;
  }
  return Math.round(kmAmount * KM_TO_EUR_RATE * 100) / 100; // Round to 2 decimal places
}

/**
 * Convert USD amount to EUR
 * Used for migrating existing data (approximate rate)
 */
export function convertUSDToEUR(usdAmount: number): number {
  const USD_TO_EUR_RATE = 0.92; // Approximate rate
  if (isNaN(usdAmount) || usdAmount === null || usdAmount === undefined) {
    return 0;
  }
  return Math.round(usdAmount * USD_TO_EUR_RATE * 100) / 100;
}

/**
 * Parse currency input string to number
 * Handles various input formats
 */
export function parseCurrencyInput(input: string): number {
  if (!input || typeof input !== 'string') {
    return 0;
  }

  // Remove currency symbols and spaces
  const cleaned = input.replace(/[€$KM\s]/g, '').replace(/,/g, '.'); // Convert comma to dot for decimal

  const parsed = parseFloat(cleaned);
  return isNaN(parsed) ? 0 : parsed;
}

/**
 * Validate currency amount
 * Common validation for forms
 */
export function validateCurrencyAmount(
  amount: number,
  min: number = 0,
  max: number = 100000
): { isValid: boolean; error?: string } {
  if (isNaN(amount) || amount === null || amount === undefined) {
    return { isValid: false, error: 'Iznos mora biti valjan broj' };
  }

  if (amount < min) {
    return { isValid: false, error: `Minimalni iznos je ${formatEUR(min)}` };
  }

  if (amount > max) {
    return { isValid: false, error: `Maksimalni iznos je ${formatEUR(max)}` };
  }

  return { isValid: true };
}

/**
 * Get currency symbol
 */
export function getCurrencySymbol(): string {
  return CURRENCY_SYMBOL;
}

/**
 * Get currency code
 */
export function getCurrencyCode(): string {
  return DEFAULT_CURRENCY;
}

/**
 * Format price range for filters
 */
export function formatPriceRange(min: number, max: number): string {
  return `${formatEUR(min)} - ${formatEUR(max)}`;
}

/**
 * Get minimum budget for campaigns/offers
 */
export function getMinimumBudget(): number {
  return 25; // 25 EUR minimum (was 50 KM)
}

/**
 * Get maximum budget for campaigns/offers
 */
export function getMaximumBudget(): number {
  return 25000; // 25,000 EUR maximum (was 50,000 KM)
}

/**
 * Format amount for Stripe or other payment providers
 * Returns amount in cents (for EUR)
 */
export function formatForPaymentProvider(amount: number): number {
  return Math.round(amount * 100); // Convert to cents
}

/**
 * Format amount from payment provider response
 * Converts from cents back to EUR
 */
export function formatFromPaymentProvider(amountInCents: number): number {
  return amountInCents / 100;
}

/**
 * Migration helper: Convert existing KM prices to EUR
 * Used for database migration scripts
 */
export function migrateKMPriceToEUR(kmPrice: number): number {
  return convertKMToEUR(kmPrice);
}

/**
 * Get localized currency formatting options
 */
export function getCurrencyFormatOptions() {
  return {
    style: 'currency',
    currency: DEFAULT_CURRENCY,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  };
}
