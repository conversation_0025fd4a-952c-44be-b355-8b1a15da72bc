/**
 * Next.js Instrumentation
 * This file runs before any other code when the Next.js server starts
 * Perfect place to validate environment variables and perform startup tasks
 */

import { initializeApplication, setupGracefulShutdown } from './lib/startup-config';

export async function register() {
  // Only run on server-side
  if (typeof window === 'undefined') {
    try {
      // Initialize application with environment validation
      await initializeApplication();
      
      // Setup graceful shutdown handlers
      setupGracefulShutdown();
      
    } catch (error) {
      console.error('❌ Failed to initialize application:', error);
      process.exit(1);
    }
  }
}