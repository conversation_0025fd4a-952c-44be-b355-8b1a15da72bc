import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Star, Instagram } from "lucide-react"
import Image from "next/image"

export default function Component() {
  return (
    <div className="p-1 bg-gradient-to-br from-pink-500 via-purple-500 to-orange-400 rounded-xl">
      <Card className="w-full max-w-sm overflow-hidden bg-white">
        <CardContent className="p-0">
          {/* Upper section - Smaller image with overlays */}
          <div className="relative aspect-[4/3] max-h-48">
            <Image src="/professional-influencer-portrait.png" alt="Influencer profile" fill className="object-cover" />

            {/* Verified badge - top left */}
            <div className="absolute top-3 left-3">
              <Badge
                variant="secondary"
                className="bg-gradient-to-r from-pink-500 to-purple-500 text-white border-0 px-2 py-1"
              >
                <Image
                  src="/images/influexus_logo_white_small_300x300.webp"
                  alt="Verified"
                  width={12}
                  height={12}
                  className="w-3 h-3 mr-1"
                />
                Verified
              </Badge>
            </div>

            {/* Star rating - top right */}
            <div className="absolute top-3 right-3">
              <Badge variant="secondary" className="bg-black/70 text-white border-0 px-2 py-1">
                <Star className="w-3 h-3 mr-1 fill-yellow-400 text-yellow-400" />
                4.9
              </Badge>
            </div>
          </div>

          {/* Lower section - Influencer info */}
          <div className="p-4 space-y-3">
            <div className="text-center">
              <h3 className="text-lg font-semibold">Sarah Johnson</h3>
              <p className="text-sm font-medium text-gradient bg-gradient-to-r from-pink-500 to-purple-500 bg-clip-text text-transparent">
                from 200€
              </p>
            </div>

            <div className="space-y-2">
              {/* Instagram handle */}
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <div className="p-1 bg-gradient-to-br from-pink-500 via-purple-500 to-orange-400 rounded-full">
                  <Instagram className="w-3 h-3 text-white" />
                </div>
                <span>@sarahjohnson</span>
              </div>

              {/* TikTok handle */}
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <div className="p-1 bg-gradient-to-br from-pink-500 via-purple-500 to-orange-400 rounded-full">
                  <svg className="w-3 h-3 text-white" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z" />
                  </svg>
                </div>
                <span>@sarahjohnson</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
