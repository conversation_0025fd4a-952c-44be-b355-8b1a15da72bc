'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Plus,
  Edit,
  Eye,
  Loader2,
  Play,
  Crown,
  ChevronDown,
  ChevronUp,
  ArrowUpDown,
} from 'lucide-react';
import Link from 'next/link';

import {
  getBusinessCampaignsDashboardOptimized,
  getBusinessCampaignsDashboardStats,
  updateCampaignStatus,
} from '@/lib/campaigns';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { supabase } from '@/lib/supabase';
import { GradientTabs } from '@/components/ui/gradient-tabs';
import { GradientButton } from '@/components/ui/gradient-button';
import {
  canActivateCampaign,
  activateCampaign as activateCampaignFunction,
} from '@/lib/subscriptions';
import { UpgradeRequiredModal } from '@/components/modals/UpgradeRequiredModal';
import { PromoteCampaignModal } from '@/components/modals/PromoteCampaignModal';
import { PaymentStatusHandler } from '@/components/PaymentStatusHandler';

interface Campaign {
  id: string;
  title: string;
  description: string;
  budget: number;
  status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';
  created_at: string;
  application_count: number;
  is_featured?: boolean;
  business_id?: string;
}

type SortColumn = 'created_at' | 'title' | 'budget' | 'application_count';
type SortDirection = 'asc' | 'desc';

interface CleanCampaignRowProps {
  campaign: Campaign;
  onActivate?: (id: string) => void;
  isActivating?: boolean;
  onPromote?: (campaign: { id: string; title: string; business_id?: string }) => void;
  userSubscriptionType?: 'free' | 'premium';
}

const StaticCampaignRow = ({ 
  campaign, 
  onActivate, 
  isActivating = false, 
  onPromote,
  userSubscriptionType = 'free'
}: CleanCampaignRowProps) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('sr-RS', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  return (
    <div className={`border-b border-gray-200 transition-all duration-200 ${
      campaign.is_featured 
        ? "bg-amber-50/30" 
        : "bg-white hover:bg-gray-50/30"
    }`}>
      <div className="grid grid-cols-12 gap-3 px-4 py-2.5 hover:bg-gray-50/50 transition-colors items-center border-b border-gray-100 last:border-b-0">
        
        <div className="col-span-2 text-sm text-gray-600">
          {formatDate(campaign.created_at)}
        </div>
        
        <div className="col-span-4 min-w-0 flex items-center gap-2">
          {campaign.is_featured && (
            <Crown className="w-4 h-4 text-amber-600 flex-shrink-0" />
          )}
          <div className="min-w-0">
            <h3 className="font-medium text-gray-900 truncate text-sm leading-tight">
              {campaign.title}
            </h3>
            <p className="text-xs text-gray-500 truncate leading-tight">
              {campaign.description}
            </p>
          </div>
        </div>
        
        <div className="col-span-2 text-sm text-gray-900 font-medium text-right">
          {campaign.budget?.toLocaleString() || '0'} €
        </div>
        
        <div className="col-span-2 text-sm text-gray-900 font-medium text-center">
          {campaign.application_count || 0}
        </div>

        <div className="col-span-2">
          <div className="flex gap-1 justify-center">
            <Link href={`/campaigns/${campaign.id}`}>
              <Button
                variant="outline"
                size="sm"
                className="border-purple-200 text-purple-600 hover:bg-purple-50 bg-transparent p-1.5 min-w-0"
                title="Pregled"
              >
                <Eye className="w-3 h-3" />
              </Button>
            </Link>

            {campaign.status === 'draft' && (
              <>
                <Link href={`/campaigns/${campaign.id}/edit`}>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-pink-200 text-pink-600 hover:bg-pink-50 bg-transparent p-1.5 min-w-0"
                    title="Uredi"
                  >
                    <Edit className="w-3 h-3" />
                  </Button>
                </Link>

                {onActivate && (
                  <Button 
                    size="sm" 
                    className="bg-purple-600 hover:bg-purple-700 text-white border-0 p-1.5 min-w-0"
                    onClick={() => onActivate(campaign.id)}
                    disabled={isActivating}
                    title="Aktiviraj"
                  >
                    {isActivating ? (
                      <Loader2 className="w-3 h-3 animate-spin" />
                    ) : (
                      <Play className="w-3 h-3" />
                    )}
                  </Button>
                )}
              </>
            )}

            {campaign.status === 'active' && !campaign.is_featured && onPromote && userSubscriptionType === 'premium' && (
              <Button
                variant="outline"
                size="sm"
                className="border-amber-300 text-amber-700 hover:bg-amber-50 bg-transparent p-1.5 min-w-0"
                onClick={() => onPromote({
                  id: campaign.id,
                  title: campaign.title,
                  business_id: campaign.business_id || '',
                })}
                title="Promoviši"
              >
                <Crown className="w-3 h-3" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default function BusinessCampaignsPage() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [sortedCampaigns, setSortedCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [activatingCampaign, setActivatingCampaign] = useState<string | null>(
    null
  );
  const [activeTab, setActiveTab] = useState('draft');
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [upgradeModalData, setUpgradeModalData] = useState<{
    currentCount: number;
    limit: number;
  } | null>(null);
  const [stats, setStats] = useState({
    draft: 0,
    active: 0,
    featured: 0,
  });
  const [showPromoteModal, setShowPromoteModal] = useState(false);
  const [selectedCampaignForPromotion, setSelectedCampaignForPromotion] =
    useState<{
      id: string;
      title: string;
      business_id: string;
    } | null>(null);
  const [userSubscriptionType, setUserSubscriptionType] = useState<
    'free' | 'premium'
  >('free');
  const [sortColumn, setSortColumn] = useState<SortColumn>('created_at');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');


  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/prijava');
      return;
    }

    if (user) {
      loadCampaigns();
    }
  }, [user, authLoading, router, activeTab]);

  const loadCampaigns = async () => {
    try {
      setLoading(true);

      if (!user?.id) {
        return;
      }

      // Load user subscription type first
      const { data: business } = await supabase
        .from('businesses')
        .select('subscription_type')
        .eq('id', user.id)
        .single();

      if (business) {
        setUserSubscriptionType(
          (business.subscription_type as 'free' | 'premium') || 'free'
        );
      }

      // Load campaigns and stats with optimized functions
      let statusFilter: string;
      if (activeTab === 'featured') {
        statusFilter = 'active'; // Featured kampanje su uvijek active, filteriraćemo nakon
      } else {
        statusFilter = activeTab === 'draft' ? 'draft' : 'active';
      }

      const [campaignsResult, statsResult] = await Promise.all([
        getBusinessCampaignsDashboardOptimized(user.id, statusFilter),
        getBusinessCampaignsDashboardStats(user.id),
      ]);

      if (campaignsResult.error) {
        console.error('Error loading campaigns:', campaignsResult.error);
        return;
      }

      if (statsResult.error) {
        console.error('Error loading stats:', statsResult.error);
      } else if (statsResult.data) {
        setStats({
          draft: statsResult.data.draft_count,
          active:
            statsResult.data.active_count - statsResult.data.featured_count, // Subtract featured from active
          featured: statsResult.data.featured_count,
        });
      }

      // Use campaigns data directly (no platform transformation needed for table view)
      let transformedCampaigns = campaignsResult.data || [];

      // Filter campaigns based on active tab
      if (activeTab === 'featured') {
        transformedCampaigns = transformedCampaigns.filter(
          campaign => campaign.is_featured
        );
      } else if (activeTab === 'active') {
        // Remove featured campaigns from active tab - featured campaigns have their own tab
        transformedCampaigns = transformedCampaigns.filter(
          campaign => !campaign.is_featured
        );
      }

      setCampaigns(transformedCampaigns);
      setSortedCampaigns(transformedCampaigns);
    } catch (error) {
      console.error('Error loading campaigns:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (column: SortColumn) => {
    const newDirection = sortColumn === column && sortDirection === 'asc' ? 'desc' : 'asc';
    setSortColumn(column);
    setSortDirection(newDirection);

    const sorted = [...campaigns].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (column) {
        case 'created_at':
          aValue = new Date(a.created_at).getTime();
          bValue = new Date(b.created_at).getTime();
          break;
        case 'title':
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
          break;
        case 'budget':
          aValue = a.budget || 0;
          bValue = b.budget || 0;
          break;
        case 'application_count':
          aValue = a.application_count || 0;
          bValue = b.application_count || 0;
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return newDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return newDirection === 'asc' ? 1 : -1;
      return 0;
    });

    setSortedCampaigns(sorted);
  };

  const getSortIcon = (column: SortColumn) => {
    if (sortColumn !== column) {
      return <ArrowUpDown className="w-3 h-3 text-gray-400" />;
    }
    return sortDirection === 'asc' ? 
      <ChevronUp className="w-3 h-3 text-purple-600" /> : 
      <ChevronDown className="w-3 h-3 text-purple-600" />;
  };

  useEffect(() => {
    if (campaigns.length > 0) {
      handleSort(sortColumn);
    }
  }, [campaigns]);

  const activateCampaign = async (campaignId: string) => {
    if (!user?.id) return;

    try {
      setActivatingCampaign(campaignId);

      // Check subscription limits before activation
      const activationCheck = await canActivateCampaign(user.id);

      if (!activationCheck.canActivate) {
        if (activationCheck.reason === 'free_limit_reached') {
          setUpgradeModalData({
            currentCount: activationCheck.activeCount,
            limit: activationCheck.maxAllowed,
          });
          setShowUpgradeModal(true);
          return;
        }
      }

      // Use our new activation function
      const success = await activateCampaignFunction(campaignId, user.id);

      if (!success) {
        console.error('Failed to activate campaign');
        return;
      }

      // Refresh campaigns
      await loadCampaigns();
    } catch (error) {
      console.error('Error activating campaign:', error);
    } finally {
      setActivatingCampaign(null);
    }
  };

  const handlePromoteCampaign = (campaign: {
    id: string;
    title: string;
    business_id?: string;
  }) => {
    // Koristimo user.id kao business_id pošto je to trenutni business korisnik
    const campaignWithBusinessId = {
      ...campaign,
      business_id: user?.id || campaign.business_id || '',
    };
    setSelectedCampaignForPromotion(campaignWithBusinessId);
    setShowPromoteModal(true);
  };

  const handlePromotionSuccess = () => {
    // Refresh campaigns and switch to featured tab to show newly promoted campaign
    loadCampaigns().then(() => {
      setActiveTab('featured');
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft':
        return <Clock className="h-4 w-4" />;
      case 'active':
        return <CheckCircle className="h-4 w-4" />;
      case 'paused':
        return <AlertCircle className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<
      string,
      'default' | 'secondary' | 'destructive' | 'outline'
    > = {
      draft: 'outline',
      active: 'default',
      paused: 'secondary',
      completed: 'secondary',
    };

    const labels: Record<string, string> = {
      draft: 'Neaktivna',
      active: 'Aktivna',
      paused: 'Pauzirana',
      completed: 'Završena',
    };

    return (
      <Badge
        variant={variants[status] || 'outline'}
        className="flex items-center gap-1"
      >
        {getStatusIcon(status)}
        {labels[status] || status}
      </Badge>
    );
  };

  const displayedCampaigns = sortedCampaigns;

  if (authLoading || loading) {
    return (
      <DashboardLayout requiredUserType="business">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex items-center gap-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Loading campaigns...</span>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="business">
      <PaymentStatusHandler />
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">Moje Kampanje</h1>
            <p className="text-muted-foreground mt-1">
              Upravljanje kampanjama u tabeli sa sortiranjem
            </p>
          </div>

          <Link href="/campaigns/create">
            <GradientButton
              gradientVariant="primary"
              className="w-full sm:w-auto"
            >
              <Plus className="h-4 w-4" />
              Nova Kampanja
            </GradientButton>
          </Link>
        </div>

        {/* Campaigns Tabs */}
        <div className="space-y-6">
          <GradientTabs
            tabs={[
              { name: 'Neaktivne', value: 'draft', count: stats.draft },
              { name: 'Aktivne', value: 'active', count: stats.active },
              { name: 'Promovisane', value: 'featured', count: stats.featured },
            ]}
            activeTab={activeTab}
            onTabChange={setActiveTab}
          />

          {/* Tab Content */}
          <div className="mt-6">
            {displayedCampaigns.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-muted-foreground mb-4">
                  {activeTab === 'draft'
                    ? 'Nemate neaktivnih kampanja'
                    : 'Nemate aktivnih kampanja'}
                </p>
                {activeTab === 'draft' && (
                  <Link href="/campaigns/create">
                    <GradientButton gradientVariant="primary">
                      <Plus className="h-4 w-4" />
                      Kreiraj prvu kampanju
                    </GradientButton>
                  </Link>
                )}
              </div>
            ) : (
              <div className="space-y-0">
                {/* Header Row with Column Names and Sort */}
                <div className="grid grid-cols-12 gap-3 px-4 py-3 bg-gray-50 border border-gray-200 rounded-t-lg font-medium text-xs text-gray-600 uppercase tracking-wide">
                  <button
                    className="col-span-2 flex items-center gap-1 hover:text-purple-600 transition-colors"
                    onClick={() => handleSort('created_at')}
                  >
                    <span>Datum</span>
                    {getSortIcon('created_at')}
                  </button>
                  <button
                    className="col-span-4 flex items-center gap-1 hover:text-purple-600 transition-colors text-left"
                    onClick={() => handleSort('title')}
                  >
                    <span>Naziv</span>
                    {getSortIcon('title')}
                  </button>
                  <button
                    className="col-span-2 flex items-center gap-1 hover:text-purple-600 transition-colors justify-end"
                    onClick={() => handleSort('budget')}
                  >
                    <span>Budžet</span>
                    {getSortIcon('budget')}
                  </button>
                  <button
                    className="col-span-2 flex items-center gap-1 hover:text-purple-600 transition-colors justify-center"
                    onClick={() => handleSort('application_count')}
                  >
                    <span>Prijave</span>
                    {getSortIcon('application_count')}
                  </button>
                  <div className="col-span-2 text-center">Akcije</div>
                </div>
                
                {/* Campaign Items */}
                <div className="border-x border-b border-gray-200 rounded-b-lg">
                  {displayedCampaigns.map(campaign => (
                    <StaticCampaignRow
                      key={campaign.id}
                      campaign={campaign}
                      onActivate={activateCampaign}
                      isActivating={activatingCampaign === campaign.id}
                      onPromote={handlePromoteCampaign}
                      userSubscriptionType={userSubscriptionType}
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <UpgradeRequiredModal
        isOpen={showUpgradeModal}
        onClose={() => setShowUpgradeModal(false)}
        feature="campaign_activation"
        currentCount={upgradeModalData?.currentCount}
        limit={upgradeModalData?.limit}
      />

      <PromoteCampaignModal
        isOpen={showPromoteModal}
        onClose={() => {
          setShowPromoteModal(false);
          setSelectedCampaignForPromotion(null);
        }}
        campaign={selectedCampaignForPromotion}
        onSuccess={handlePromotionSuccess}
      />
    </DashboardLayout>
  );
}
