// Test script za RPC funkcije - pokrenuti u browser konzoli
// ili preko Node.js sa supabase klijentom

// Prvo treba da se importuje supabase klijent
// import { supabase } from './src/lib/supabase.js';

console.log('🚀 Testiranje RPC funkcija za influencers...');

// Test 1: Osnovni poziv bez filtera
async function testBasicRPC() {
  console.log('\n📋 TEST 1: Osnovni poziv bez filtera');
  const startTime = performance.now();
  
  try {
    const { data, error } = await supabase.rpc('get_influencers_paginated', {
      p_limit: 5,
      p_offset: 0
    });
    
    const endTime = performance.now();
    
    if (error) {
      console.error('❌ RPC Error:', error);
      return false;
    }
    
    console.log(`✅ Success! Returned ${data?.length || 0} influencers in ${(endTime - startTime).toFixed(2)}ms`);
    console.log('Sample data:', data?.[0]);
    return true;
  } catch (err) {
    console.error('❌ Exception:', err);
    return false;
  }
}

// Test 2: Pretraga sa filterima
async function testFilteredRPC() {
  console.log('\n📋 TEST 2: Pretraga sa filterima');
  const startTime = performance.now();
  
  try {
    const { data, error } = await supabase.rpc('get_influencers_paginated', {
      p_search: 'test',
      p_min_age: 20,
      p_max_age: 35,
      p_gender: 'male',
      p_limit: 10,
      p_offset: 0,
      p_sort_by: 'followers_desc',
      p_sort_order: 'desc'
    });
    
    const endTime = performance.now();
    
    if (error) {
      console.error('❌ Filtered RPC Error:', error);
      return false;
    }
    
    console.log(`✅ Filtered search completed! Returned ${data?.length || 0} influencers in ${(endTime - startTime).toFixed(2)}ms`);
    if (data && data.length > 0) {
      console.log('First result:', {
        username: data[0].username,
        age: data[0].age,
        gender: data[0].gender,
        total_followers: data[0].total_followers,
        platforms: data[0].platforms?.length || 0
      });
    }
    return true;
  } catch (err) {
    console.error('❌ Filtered Exception:', err);
    return false;
  }
}

// Test 3: Count funkcija
async function testCountRPC() {
  console.log('\n📋 TEST 3: Count funkcija');
  const startTime = performance.now();
  
  try {
    const { data, error } = await supabase.rpc('count_influencers_paginated', {
      p_search: null,
      p_min_age: 18,
      p_max_age: 50
    });
    
    const endTime = performance.now();
    
    if (error) {
      console.error('❌ Count RPC Error:', error);
      return false;
    }
    
    console.log(`✅ Count completed! Total influencers: ${data} in ${(endTime - startTime).toFixed(2)}ms`);
    return true;
  } catch (err) {
    console.error('❌ Count Exception:', err);
    return false;
  }
}

// Test 4: Performance comparison (stara vs nova implementacija)
async function testPerformanceComparison() {
  console.log('\n📋 TEST 4: Performance poređenje');
  
  // Test nove implementacije
  console.log('Testing NEW RPC implementation...');
  const rpcStart = performance.now();
  
  try {
    const { data: rpcData, error: rpcError } = await supabase.rpc('get_influencers_paginated', {
      p_limit: 20,
      p_offset: 0
    });
    
    const rpcEnd = performance.now();
    const rpcTime = rpcEnd - rpcStart;
    
    if (rpcError) {
      console.error('❌ RPC failed:', rpcError);
      return;
    }
    
    console.log(`✅ NEW (RPC): ${rpcTime.toFixed(2)}ms - ${rpcData?.length || 0} results`);
    
    // Test stare implementacije (legacy)
    console.log('Testing LEGACY implementation...');
    const legacyStart = performance.now();
    
    // Simulacija stare implementacije - 1 glavni query + N platform queries + N pricing queries
    let legacyQueries = 1;
    
    const { data: profiles } = await supabase
      .from('profiles')
      .select(`
        id,
        username,
        full_name,
        avatar_url,
        bio,
        city,
        country,
        age,
        gender,
        influencers!inner(subscription_type)
      `)
      .eq('user_type', 'influencer')
      .eq('profile_completed', true)
      .limit(20);
    
    // Za svaki profil, potrebna su 2 dodatna query-ja
    const legacyData = [];
    for (const profile of profiles || []) {
      const { data: platforms } = await supabase
        .from('influencer_platforms')
        .select('platform_id, handle, followers_count, is_verified, platforms!inner(name, icon)')
        .eq('influencer_id', profile.id)
        .eq('is_active', true);
      
      legacyQueries++;
      
      const { data: pricing } = await supabase
        .from('influencer_platform_pricing')
        .select('platform_id, content_type_id, price, currency, platforms!inner(name), content_types!inner(name)')
        .eq('influencer_id', profile.id)
        .eq('is_available', true);
      
      legacyQueries++;
      
      legacyData.push({ ...profile, platforms, pricing });
    }\n    \n    const legacyEnd = performance.now();\n    const legacyTime = legacyEnd - legacyStart;\n    \n    console.log(`❌ LEGACY: ${legacyTime.toFixed(2)}ms - ${legacyData.length} results - ${legacyQueries} queries`);\n    \n    // Performance poređenje\n    const improvement = ((legacyTime - rpcTime) / legacyTime * 100).toFixed(1);\n    const queryReduction = ((legacyQueries - 1) / legacyQueries * 100).toFixed(1);\n    \n    console.log(`\\n🎉 PERFORMANCE POBOLJŠANJE:`);\n    console.log(`   ⚡ Brzina: ${improvement}% brže (${legacyTime.toFixed(2)}ms → ${rpcTime.toFixed(2)}ms)`);\n    console.log(`   📊 Query-ji: ${queryReduction}% manje (${legacyQueries} → 1)`);\n    \n  } catch (err) {\n    console.error('❌ Performance test failed:', err);\n  }\n}\n\n// Test 5: Različiti sort parametri\nasync function testSortOptions() {\n  console.log('\\n📋 TEST 5: Testiranje sort opcija');\n  \n  const sortTests = [\n    { sortBy: 'created_at', order: 'desc', label: 'Najnoviji' },\n    { sortBy: 'followers_desc', order: 'desc', label: 'Najviše followers' },\n    { sortBy: 'price_asc', order: 'asc', label: 'Najjeftiniji' },\n    { sortBy: 'price_desc', order: 'desc', label: 'Najskuplji' }\n  ];\n  \n  for (const test of sortTests) {\n    const startTime = performance.now();\n    \n    try {\n      const { data, error } = await supabase.rpc('get_influencers_paginated', {\n        p_sort_by: test.sortBy,\n        p_sort_order: test.order,\n        p_limit: 5,\n        p_offset: 0\n      });\n      \n      const endTime = performance.now();\n      \n      if (error) {\n        console.error(`❌ Sort test '${test.label}' failed:`, error);\n        continue;\n      }\n      \n      console.log(`✅ ${test.label}: ${(endTime - startTime).toFixed(2)}ms - ${data?.length || 0} results`);\n      \n      if (data && data.length > 0) {\n        console.log(`   First result: ${data[0].username} (followers: ${data[0].total_followers}, price: $${data[0].min_price})`);\n      }\n      \n    } catch (err) {\n      console.error(`❌ Sort test '${test.label}' exception:`, err);\n    }\n  }\n}\n\n// Glavna test funkcija\nasync function runAllTests() {\n  console.log('🧪 Pokretanje svih testova...\\n');\n  \n  const tests = [\n    { name: 'Basic RPC', fn: testBasicRPC },\n    { name: 'Filtered RPC', fn: testFilteredRPC },\n    { name: 'Count RPC', fn: testCountRPC },\n    { name: 'Sort Options', fn: testSortOptions },\n    { name: 'Performance Comparison', fn: testPerformanceComparison }\n  ];\n  \n  const results = [];\n  \n  for (const test of tests) {\n    console.log(`\\n🔄 Running ${test.name}...`);\n    try {\n      const result = await test.fn();\n      results.push({ name: test.name, success: result !== false });\n    } catch (err) {\n      console.error(`❌ Test ${test.name} crashed:`, err);\n      results.push({ name: test.name, success: false });\n    }\n  }\n  \n  // Summary\n  console.log('\\n📊 TEST SUMMARY:');\n  results.forEach(result => {\n    console.log(`   ${result.success ? '✅' : '❌'} ${result.name}`);\n  });\n  \n  const passedTests = results.filter(r => r.success).length;\n  console.log(`\\n🎯 ${passedTests}/${results.length} testova prošlo`);\n  \n  if (passedTests === results.length) {\n    console.log('🎉 SVI TESTOVI SU USPEŠNI! RPC funkcije rade kako treba.');\n  } else {\n    console.log('⚠️  Neki testovi nisu prošli. Proverite RPC funkcije u Supabase.');\n  }\n}\n\n// Pokretanje testova\n// runAllTests();\n\n// Export za korišćenje\nif (typeof module !== 'undefined' && module.exports) {\n  module.exports = {\n    testBasicRPC,\n    testFilteredRPC,\n    testCountRPC,\n    testPerformanceComparison,\n    testSortOptions,\n    runAllTests\n  };\n}\n\n// Za browser konzolu\nif (typeof window !== 'undefined') {\n  window.testRPCFunctions = {\n    testBasicRPC,\n    testFilteredRPC,\n    testCountRPC,\n    testPerformanceComparison,\n    testSortOptions,\n    runAllTests\n  };\n  \n  console.log('💡 Za pokretanje testova u browser konzoli:');\n  console.log('   window.testRPCFunctions.runAllTests()');\n}