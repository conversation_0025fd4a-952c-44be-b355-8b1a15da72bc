import { supabase } from './supabase';
import { Database } from './database.types';

type Platform = Database['public']['Tables']['platforms']['Row'];
type ContentType = Database['public']['Tables']['content_types']['Row'];

export interface PlatformWithContentTypes extends Platform {
  content_types?: ContentType[];
}

/**
 * Get all platforms
 */
export async function getPlatforms() {
  try {
    const { data, error } = await supabase
      .from('platforms')
      .select('*')
      .order('name');

    if (error) {
      console.error('Error fetching platforms:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error fetching platforms:', error);
    return { data: null, error };
  }
}

/**
 * Get platforms with their content types
 */
export async function getPlatformsWithContentTypes() {
  try {
    const { data, error } = await supabase
      .from('platforms')
      .select(
        `
        *,
        content_types(*)
      `
      )
      .order('name');

    if (error) {
      console.error('Error fetching platforms with content types:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error(
      'Unexpected error fetching platforms with content types:',
      error
    );
    return { data: null, error };
  }
}

/**
 * Get platform by ID
 */
export async function getPlatformById(id: number) {
  try {
    const { data, error } = await supabase
      .from('platforms')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching platform:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error fetching platform:', error);
    return { data: null, error };
  }
}

/**
 * Get platform by slug
 */
export async function getPlatformBySlug(slug: string) {
  try {
    const { data, error } = await supabase
      .from('platforms')
      .select('*')
      .eq('slug', slug)
      .single();

    if (error) {
      console.error('Error fetching platform:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error fetching platform:', error);
    return { data: null, error };
  }
}

/**
 * Get platforms by IDs
 */
export async function getPlatformsByIds(ids: number[]) {
  try {
    const { data, error } = await supabase
      .from('platforms')
      .select('*')
      .in('id', ids)
      .order('name');

    if (error) {
      console.error('Error fetching platforms:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error fetching platforms:', error);
    return { data: null, error };
  }
}

/**
 * Get all content types
 */
export async function getContentTypes() {
  try {
    const { data, error } = await supabase
      .from('content_types')
      .select('*')
      .order('name');

    if (error) {
      console.error('Error fetching content types:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error fetching content types:', error);
    return { data: null, error };
  }
}

/**
 * Get content types for a specific platform
 */
export async function getContentTypesByPlatform(platformId: number) {
  try {
    const { data, error } = await supabase
      .from('content_types')
      .select('*')
      .eq('platform_id', platformId)
      .order('name');

    if (error) {
      console.error('Error fetching content types:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error fetching content types:', error);
    return { data: null, error };
  }
}

/**
 * Get content type by ID
 */
export async function getContentTypeById(id: number) {
  try {
    const { data, error } = await supabase
      .from('content_types')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching content type:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error fetching content type:', error);
    return { data: null, error };
  }
}

/**
 * Get influencer platforms (platforms an influencer works with)
 */
export async function getInfluencerPlatforms(influencerId: string) {
  try {
    const { data, error } = await supabase
      .from('influencer_platforms')
      .select(
        `
        *,
        platforms(*),
        content_types(*)
      `
      )
      .eq('influencer_id', influencerId);

    if (error) {
      console.error('Error fetching influencer platforms:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error fetching influencer platforms:', error);
    return { data: null, error };
  }
}

/**
 * Add platforms to influencer
 */
export async function addInfluencerPlatforms(
  influencerId: string,
  platformData: Array<{
    platform_id: number;
    content_type_ids: number[];
    price_per_post?: number;
    followers_count?: number;
    engagement_rate?: number;
    username?: string;
    profile_url?: string;
  }>
) {
  try {
    const { data, error } = await supabase.from('influencer_platforms').insert(
      platformData.map(platform => ({
        influencer_id: influencerId,
        platform_id: platform.platform_id,
        content_type_ids: platform.content_type_ids,
        price_per_post: platform.price_per_post || null,
        followers_count: platform.followers_count || null,
        engagement_rate: platform.engagement_rate || null,
        username: platform.username || null,
        profile_url: platform.profile_url || null,
      }))
    );

    if (error) {
      console.error('Error adding influencer platforms:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error adding influencer platforms:', error);
    return { data: null, error };
  }
}

/**
 * Remove all platforms from influencer
 */
export async function removeInfluencerPlatforms(influencerId: string) {
  try {
    const { error } = await supabase
      .from('influencer_platforms')
      .delete()
      .eq('influencer_id', influencerId);

    if (error) {
      console.error('Error removing influencer platforms:', error);
      return { error };
    }

    return { error: null };
  } catch (error) {
    console.error('Unexpected error removing influencer platforms:', error);
    return { error };
  }
}

/**
 * Update influencer platforms (remove old, add new)
 */
export async function updateInfluencerPlatforms(
  influencerId: string,
  platformData: Array<{
    platform_id: number;
    content_type_ids: number[];
    price_per_post?: number;
    followers_count?: number;
    engagement_rate?: number;
    username?: string;
    profile_url?: string;
  }>
) {
  try {
    // Remove existing platforms
    await removeInfluencerPlatforms(influencerId);

    // Add new platforms
    if (platformData.length > 0) {
      return await addInfluencerPlatforms(influencerId, platformData);
    }

    return { data: null, error: null };
  } catch (error) {
    console.error('Unexpected error updating influencer platforms:', error);
    return { data: null, error };
  }
}

/**
 * Get popular platforms (most used by influencers)
 */
export async function getPopularPlatforms(limit: number = 10) {
  try {
    const { data, error } = await supabase
      .from('platforms')
      .select(
        `
        *,
        influencer_platforms(count)
      `
      )
      .order('influencer_platforms.count', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching popular platforms:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error fetching popular platforms:', error);
    return { data: null, error };
  }
}
