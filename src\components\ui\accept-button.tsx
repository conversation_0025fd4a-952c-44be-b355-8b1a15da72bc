import React from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';

interface AcceptButtonProps extends React.ComponentProps<'button'> {
  variant?: 'primary' | 'secondary';
}

const AcceptButton = React.forwardRef<HTMLButtonElement, AcceptButtonProps>(
  ({ className, variant = 'primary', ...props }, ref) => {
    const variantClasses = {
      primary:
        'bg-gradient-to-r from-green-600 via-emerald-600 to-green-700 hover:from-green-700 hover:via-emerald-700 hover:to-green-800 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] hover:shadow-green-200/50 dark:hover:shadow-green-900/30',
      secondary:
        'bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 text-green-700 hover:bg-gradient-to-r hover:from-green-100 hover:to-emerald-100 hover:border-green-300 transition-all duration-300 hover:scale-[1.02]',
    };

    return (
      <Button
        ref={ref}
        className={cn(variantClasses[variant], className)}
        {...props}
      />
    );
  }
);

AcceptButton.displayName = 'AcceptButton';

export { AcceptButton };
