import React from 'react';
import { AiFillTikTok, AiFillInstagram, AiFillYoutube } from 'react-icons/ai';
import { cn } from '@/lib/utils';

interface PlatformIconProps {
  platform: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  variant?: 'default' | 'brand' | 'monochrome';
  className?: string;
}

const PlatformIcon: React.FC<PlatformIconProps> = ({
  platform,
  size = 'md',
  variant = 'brand',
  className,
}) => {
  const normalizedName = platform.toLowerCase();

  // Size mapping
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
    xl: 'w-8 h-8',
    '2xl': 'w-10 h-10',
  };

  const sizeClass = sizeClasses[size];

  // Monochrome variant
  if (variant === 'monochrome') {
    const monoClass = cn(
      sizeClass,
      'text-gray-500 dark:text-gray-400',
      className
    );

    switch (normalizedName) {
      case 'instagram':
        return <AiFillInstagram className={monoClass} />;
      case 'tiktok':
        return <AiFillTikTok className={monoClass} />;
      case 'youtube':
        return <AiFillYoutube className={monoClass} />;
      default:
        return (
          <span
            className={cn(
              'flex items-center justify-center font-semibold text-gray-500',
              sizeClass,
              className
            )}
          >
            {platform.charAt(0).toUpperCase()}
          </span>
        );
    }
  }

  // Brand colors variant
  switch (normalizedName) {
    case 'instagram':
      return (
        <div className={cn('relative', sizeClass, className)}>
          <AiFillInstagram
            className={cn(
              sizeClass,
              'text-transparent bg-gradient-to-br from-purple-600 via-pink-500 to-orange-400'
            )}
            style={{
              background:
                'radial-gradient(circle at 30% 107%, #fdf497 0%, #fdf497 5%, #fd5949 45%, #d6249f 60%, #285AEB 90%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
            }}
          />
        </div>
      );

    case 'tiktok':
      return (
        <div className={cn('relative', sizeClass, className)}>
          <AiFillTikTok
            className={cn(sizeClass, 'text-black dark:text-white')}
            style={{
              filter:
                'drop-shadow(1px 1px 0px #ff0050) drop-shadow(-1px -1px 0px #00f2ea)',
            }}
          />
        </div>
      );

    case 'youtube':
      return (
        <div
          className={cn(
            'relative inline-flex items-center justify-center',
            sizeClass,
            className
          )}
        >
          {/* YouTube background */}
          <div
            className={cn(
              sizeClass,
              'rounded-sm flex items-center justify-center'
            )}
            style={{ backgroundColor: '#FF0000' }}
          >
            {/* YouTube play button */}
            <div
              className="triangle-right"
              style={{
                width: 0,
                height: 0,
                borderLeft: `${size === 'sm' ? '4px' : size === 'md' ? '5px' : size === 'lg' ? '6px' : size === 'xl' ? '8px' : '10px'} solid white`,
                borderTop: `${size === 'sm' ? '3px' : size === 'md' ? '4px' : size === 'lg' ? '5px' : size === 'xl' ? '6px' : '8px'} solid transparent`,
                borderBottom: `${size === 'sm' ? '3px' : size === 'md' ? '4px' : size === 'lg' ? '5px' : size === 'xl' ? '6px' : '8px'} solid transparent`,
                marginLeft: '1px',
              }}
            />
          </div>
        </div>
      );

    case 'facebook':
      return (
        <div className={cn('relative', sizeClass, className)}>
          <div
            className={cn(
              sizeClass,
              'rounded-sm flex items-center justify-center text-white font-bold'
            )}
            style={{ backgroundColor: '#1877F2' }}
          >
            f
          </div>
        </div>
      );

    case 'twitter':
    case 'x':
      return (
        <div className={cn('relative', sizeClass, className)}>
          <div
            className={cn(
              sizeClass,
              'rounded-sm flex items-center justify-center text-white font-bold'
            )}
            style={{ backgroundColor: '#000000' }}
          >
            𝕏
          </div>
        </div>
      );

    case 'linkedin':
      return (
        <div className={cn('relative', sizeClass, className)}>
          <div
            className={cn(
              sizeClass,
              'rounded-sm flex items-center justify-center text-white font-bold'
            )}
            style={{ backgroundColor: '#0A66C2' }}
          >
            in
          </div>
        </div>
      );

    default:
      return (
        <span
          className={cn(
            'flex items-center justify-center font-semibold text-gray-500 bg-gray-100 rounded-sm',
            sizeClass,
            className
          )}
        >
          {platform.charAt(0).toUpperCase()}
        </span>
      );
  }
};

export default PlatformIcon;
