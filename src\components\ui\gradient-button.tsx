import React from 'react';
import { cn } from '@/lib/utils';
import { Button, ButtonProps } from '@/components/ui/button';

interface GradientButtonProps extends ButtonProps {
  gradientVariant?:
    | 'primary'
    | 'secondary'
    | 'outline'
    | 'instagram'
    | 'white'
    | 'glass';
}

const GradientButton = React.forwardRef<HTMLButtonElement, GradientButtonProps>(
  ({ className, gradientVariant = 'primary', ...props }, ref) => {
    const gradientClasses = {
      primary:
        'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]',
      secondary:
        'bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 text-purple-700 hover:bg-gradient-to-r hover:from-purple-100 hover:to-pink-100 hover:border-purple-300 transition-all duration-300 hover:scale-[1.02]',
      outline:
        'bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 text-purple-700 hover:bg-gradient-to-r hover:from-purple-100 hover:to-pink-100 hover:border-purple-300 transition-all duration-300 hover:scale-[1.02]',
      instagram:
        'bg-instagram-primary text-white hover:bg-instagram-secondary shadow-lg hover:shadow-xl transition-all duration-300',
      white:
        'bg-white text-instagram-purple hover:bg-white/90 font-semibold shadow-lg hover:shadow-xl transition-all duration-300',
      glass:
        'bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300',
    };

    return (
      <Button
        ref={ref}
        className={cn(gradientClasses[gradientVariant], className)}
        {...props}
      />
    );
  }
);

GradientButton.displayName = 'GradientButton';

export { GradientButton };
