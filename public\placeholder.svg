<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#e2e8f0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#cbd5e1;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="icon-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ec4899;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f97316;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#bg-gradient)"/>
  
  <!-- Grid pattern -->
  <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
    <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e2e8f0" stroke-width="0.5" opacity="0.3"/>
  </pattern>
  <rect width="400" height="300" fill="url(#grid)"/>
  
  <!-- Camera icon -->
  <g transform="translate(200, 150)">
    <!-- Camera body -->
    <rect x="-30" y="-15" width="60" height="30" rx="6" fill="url(#icon-gradient)" opacity="0.8"/>
    <!-- Lens -->
    <circle cx="0" cy="0" r="12" fill="none" stroke="white" stroke-width="2"/>
    <circle cx="0" cy="0" r="8" fill="none" stroke="white" stroke-width="1.5"/>
    <!-- Flash -->
    <rect x="-25" y="-12" width="8" height="4" rx="2" fill="white" opacity="0.9"/>
  </g>
  
  <!-- Text -->
  <text x="200" y="200" text-anchor="middle" fill="#64748b" font-family="system-ui, -apple-system, sans-serif" font-size="14" font-weight="500">
    Placeholder Image
  </text>
  
  <!-- Decorative elements -->
  <circle cx="80" cy="80" r="3" fill="#ec4899" opacity="0.4"/>
  <circle cx="320" cy="220" r="4" fill="#8b5cf6" opacity="0.4"/>
  <circle cx="350" cy="50" r="2" fill="#f97316" opacity="0.4"/>
  <circle cx="50" cy="250" r="3" fill="#06b6d4" opacity="0.4"/>
</svg>
