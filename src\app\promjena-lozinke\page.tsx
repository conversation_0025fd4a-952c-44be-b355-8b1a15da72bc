'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Eye, EyeOff, Lock, Shield, ArrowLeft } from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';

const changePasswordSchema = z
  .object({
    currentPassword: z.string().min(1, 'Unesite trenutnu lozinku'),
    newPassword: z
      .string()
      .min(6, 'Nova lozinka mora imati najmanje 6 karaktera')
      .regex(
        /[A-Z]/,
        'Nova lozinka mora sadržavati najmanje jedno veliko slovo'
      )
      .regex(/[a-z]/, 'Nova lozinka mora sadržavati najmanje jedno malo slovo')
      .regex(/[0-9]/, 'Nova lozinka mora sadržavati najmanje jedan broj')
      .regex(
        /[^A-Za-z0-9]/,
        'Nova lozinka mora sadržavati najmanje jedan specijalni karakter'
      ),
    confirmPassword: z.string().min(6, 'Potvrdite novu lozinku'),
  })
  .refine(data => data.newPassword === data.confirmPassword, {
    message: 'Nove lozinke se ne poklapaju',
    path: ['confirmPassword'],
  });

type ChangePasswordForm = z.infer<typeof changePasswordSchema>;

export default function PromjenaLozinkePage() {
  const router = useRouter();
  const { updatePassword, verifyCurrentPassword, user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
    reset,
  } = useForm<ChangePasswordForm>({
    resolver: zodResolver(changePasswordSchema),
  });

  const onSubmit = async (data: ChangePasswordForm) => {
    setIsLoading(true);

    try {
      // First, verify the current password
      const verification = await verifyCurrentPassword(data.currentPassword);

      if (!verification.isValid) {
        setError('currentPassword', {
          message: verification.error || 'Trenutna lozinka nije ispravna',
        });
        return;
      }

      // Update password - Supabase requires user to be authenticated to change password
      const { error } = await updatePassword(data.newPassword);

      if (error) {
        setError('root', { message: error.message });
        return;
      }

      toast.success('Lozinka je uspješno promijenjena!');
      reset();

      // Redirect to dashboard after successful change
      setTimeout(() => {
        router.push('/dashboard');
      }, 2000);
    } catch (error) {
      setError('root', { message: 'Došlo je do greške. Pokušajte ponovo.' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#E02F75] via-[#6700A3] to-[#050C38] relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-white/5 rounded-full blur-lg"></div>
        <div className="absolute bottom-20 left-1/4 w-40 h-40 bg-white/5 rounded-full blur-2xl"></div>
        <div className="absolute bottom-40 right-10 w-28 h-28 bg-white/10 rounded-full blur-xl"></div>
      </div>

      {/* Main Content */}
      <main className="relative z-10 flex-1 flex items-center justify-center px-4 py-12">
        <Card className="w-full max-w-md glass-instagram border-white/20 shadow-2xl">
          <CardHeader className="text-center">
            <div className="flex items-center justify-center mb-4">
              <Link
                href="/dashboard"
                className="absolute left-6 top-6 text-white/70 hover:text-white transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
              </Link>
            </div>

            <div className="w-16 h-16 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-white/20">
              <Shield className="h-8 w-8 text-white" />
            </div>
            <CardTitle className="text-2xl text-white">
              Promjena lozinke
            </CardTitle>
            <CardDescription className="text-white/70">
              Ažurirajte svoju lozinku za sigurniji pristup
            </CardDescription>
          </CardHeader>

          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Current Password */}
              <div className="space-y-2">
                <Label htmlFor="currentPassword" className="text-white">
                  Trenutna lozinka
                </Label>
                <div className="relative">
                  <Input
                    id="currentPassword"
                    type={showCurrentPassword ? 'text' : 'password'}
                    placeholder="Unesite trenutnu lozinku"
                    {...register('currentPassword')}
                    className="bg-white/10 border-white/20 text-white placeholder:text-white/50 focus:border-white/40 focus:ring-white/20 pr-10"
                  />
                  <button
                    type="button"
                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors"
                  >
                    {showCurrentPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
                {errors.currentPassword && (
                  <p className="text-red-300 text-sm">
                    {errors.currentPassword.message}
                  </p>
                )}
              </div>

              {/* New Password */}
              <div className="space-y-2">
                <Label htmlFor="newPassword" className="text-white">
                  Nova lozinka
                </Label>
                <div className="relative">
                  <Input
                    id="newPassword"
                    type={showNewPassword ? 'text' : 'password'}
                    placeholder="Unesite novu lozinku"
                    {...register('newPassword')}
                    className="bg-white/10 border-white/20 text-white placeholder:text-white/50 focus:border-white/40 focus:ring-white/20 pr-10"
                  />
                  <button
                    type="button"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors"
                  >
                    {showNewPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
                {errors.newPassword && (
                  <p className="text-red-300 text-sm">
                    {errors.newPassword.message}
                  </p>
                )}
              </div>

              {/* Confirm New Password */}
              <div className="space-y-2">
                <Label htmlFor="confirmPassword" className="text-white">
                  Potvrdite novu lozinku
                </Label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    placeholder="Potvrdite novu lozinku"
                    {...register('confirmPassword')}
                    className="bg-white/10 border-white/20 text-white placeholder:text-white/50 focus:border-white/40 focus:ring-white/20 pr-10"
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors"
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
                {errors.confirmPassword && (
                  <p className="text-red-300 text-sm">
                    {errors.confirmPassword.message}
                  </p>
                )}
              </div>

              {/* Error Message */}
              {errors.root && (
                <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-3">
                  <p className="text-red-300 text-sm text-center">
                    {errors.root.message}
                  </p>
                </div>
              )}

              {/* Security Tips */}
              <div className="bg-white/10 border border-white/20 rounded-lg p-4">
                <h4 className="text-sm font-medium text-white mb-2">
                  Zahtjevi za sigurnu lozinku:
                </h4>
                <ul className="text-xs text-white/70 space-y-1">
                  <li>• Najmanje 6 karaktera</li>
                  <li>• Najmanje jedno veliko slovo (A-Z)</li>
                  <li>• Najmanje jedno malo slovo (a-z)</li>
                  <li>• Najmanje jedan broj (0-9)</li>
                  <li>• Najmanje jedan specijalni karakter (!@#$%^&*)</li>
                </ul>
              </div>

              {/* Submit Button */}
              <div className="flex gap-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push('/dashboard')}
                  className="flex-1 bg-white/10 border-white/20 text-white hover:bg-white/20"
                >
                  Otkaži
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="flex-1 bg-white text-[#6700A3] hover:bg-white/90 font-semibold transition-all duration-200 disabled:opacity-50"
                >
                  {isLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-[#6700A3]/30 border-t-[#6700A3] rounded-full animate-spin mr-2" />
                      Ažurira se...
                    </>
                  ) : (
                    'Promijeni lozinku'
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
