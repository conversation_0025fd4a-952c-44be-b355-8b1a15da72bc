import { Card, CardContent } from "@/components/ui/card"
import { Calendar, Users, Instagram, Youtube, Music, Crown } from "lucide-react"

type Platform = "Instagram" | "YouTube" | "TikTok"

interface PlatformRequirement {
  platform: Platform
  contentTypes: string[]
}

interface Campaign {
  name: string
  description: string
  requirements: PlatformRequirement[]
  creationDate: string
  price: number
  applications: number
  isPremium?: boolean // Added premium flag
}

interface CampaignCardProps {
  campaign: Campaign
}

const getPlatformIcon = (platform: Platform) => {
  switch (platform) {
    case "Instagram":
      return <Instagram className="h-4 w-4" />
    case "YouTube":
      return <Youtube className="h-4 w-4" />
    case "TikTok":
      return <Music className="h-4 w-4" />
    default:
      return null
  }
}

const getPlatformStyles = (platform: Platform) => {
  switch (platform) {
    case "Instagram":
      return "bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-purple-900 border border-purple-200/50 backdrop-blur-sm"
    case "YouTube":
      return "bg-red-500/20 text-red-900 border border-red-200/50 backdrop-blur-sm"
    case "TikTok":
      return "bg-gray-500/20 text-gray-900 border border-gray-200/50 backdrop-blur-sm"
    default:
      return "bg-gray-500/20 text-gray-900 border border-gray-200/50 backdrop-blur-sm"
  }
}

export function CampaignCard({ campaign }: CampaignCardProps) {
  const formattedDate = new Date(campaign.creationDate).toLocaleDateString("de-DE", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  })

  const formattedPrice = `€${campaign.price}`

  const cardStyles = campaign.isPremium
    ? "w-full max-w-sm bg-gradient-to-br from-amber-50/80 to-yellow-100/60 backdrop-blur-sm border border-amber-200/50 shadow-lg shadow-amber-100/50 relative overflow-hidden"
    : "w-full max-w-sm bg-gradient-to-br from-pink-50/80 to-rose-100/60 backdrop-blur-sm border border-pink-200/50 shadow-lg shadow-pink-100/50 relative overflow-hidden"

  const ribbonStyles = campaign.isPremium
    ? { backgroundColor: "#d97706", color: "#ffffff" }
    : { backgroundColor: "#059669", color: "#ffffff" }

  const borderStyles = campaign.isPremium ? "border-t border-amber-200/30" : "border-t border-pink-200/30"

  return (
    <Card className={cardStyles}>
      <div className="flex justify-end p-3 pb-0">
        <div className="px-4 py-1.5 rounded-full shadow-lg flex items-center gap-1.5" style={ribbonStyles}>
          {campaign.isPremium && <Crown className="h-3 w-3" />}
          <div className="font-bold text-sm whitespace-nowrap" style={{ color: "#ffffff" }}>
            {campaign.isPremium ? "PREMIUM" : formattedPrice}
          </div>
        </div>
      </div>

      {campaign.isPremium && (
        <div className="flex justify-end px-3 pb-2">
          <div className="text-lg font-bold text-amber-700">{formattedPrice}</div>
        </div>
      )}

      <CardContent className="px-4 py-4 space-y-3">
        <h3 className="font-semibold text-base leading-tight">{campaign.name}</h3>

        <p className="text-sm text-muted-foreground leading-relaxed line-clamp-2">{campaign.description}</p>

        <div className="space-y-2">
          <h4 className="text-xs font-medium text-foreground/80">Required Content:</h4>
          <div className="space-y-2">
            {campaign.requirements.map((req, index) => (
              <div key={index} className="flex items-center gap-2">
                <div
                  className={`flex items-center gap-1.5 text-xs font-medium px-2.5 py-1.5 rounded-full ${getPlatformStyles(req.platform)}`}
                >
                  {getPlatformIcon(req.platform)}
                  <span className="sr-only">{req.platform}</span>
                </div>
                <div className="flex flex-wrap gap-1">
                  {req.contentTypes.map((contentType, contentIndex) => (
                    <span
                      key={contentIndex}
                      className="text-xs bg-white/60 backdrop-blur-sm text-gray-600 px-2 py-0.5 rounded border border-gray-200/50"
                    >
                      {contentType}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className={`flex items-center justify-between text-xs text-muted-foreground pt-2 ${borderStyles}`}>
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            {formattedDate}
          </div>
          <div className="flex items-center gap-1">
            <Users className="h-3 w-3" />
            {campaign.applications}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
