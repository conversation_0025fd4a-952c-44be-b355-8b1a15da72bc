'use client';

import { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { checkBusinessProfileAccess } from '@/lib/profile-access';
import { BusinessProfileClient } from './BusinessProfileClient';
import { Loader2 } from 'lucide-react';

export interface PublicBusinessProfile {
  id: string;
  username: string;
  brand_name: string;
  full_name: string | null;
  avatar_url: string | null;
  bio: string | null;
  company_description: string | null;
  industry: string | null;
  company_size: string | null;
  budget_range: string | null;
  location: string;
  website_url: string | null;
  created_at: string;
  platforms: {
    platform_id: number;
    platform_name: string;
    platform_icon: string;
    handle: string;
    followers_count: number;
  }[];
  categories: {
    id: number;
    name: string;
    icon: string;
  }[];
}

interface BusinessProfileWithAccessControlProps {
  profile: PublicBusinessProfile;
  targetUsername: string;
}

export function BusinessProfileWithAccessControl({
  profile,
  targetUsername,
}: BusinessProfileWithAccessControlProps) {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [accessChecking, setAccessChecking] = useState(true);
  const [hasAccess, setHasAccess] = useState(false);

  const checkAccess = useCallback(async () => {
    if (authLoading) return;

    if (!user) {
      // Korisnik nije ulogovan - middleware bi trebao ovo spriječiti, ali dodajemo za sigurnost
      router.push('/prijava');
      return;
    }

    try {
      setAccessChecking(true);

      const accessResult = await checkBusinessProfileAccess(
        targetUsername,
        user.id
      );

      if (!accessResult.hasAccess) {
        console.log(
          `Access denied for user ${user.id} to business profile ${targetUsername}: ${accessResult.reason}`
        );

        if (accessResult.redirectTo) {
          router.push(accessResult.redirectTo);
        } else {
          router.push('/dashboard');
        }
        return;
      }

      // Access granted - user can view business profile
      setHasAccess(true);
    } catch (error) {
      console.error('Error checking access:', error);
      router.push('/dashboard');
    } finally {
      setAccessChecking(false);
    }
  }, [authLoading, user, targetUsername, router]);

  useEffect(() => {
    checkAccess();
  }, [checkAccess]);

  // Prikaži loading dok se proverava autentifikacija ili access control
  if (authLoading || accessChecking) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Provjera pristupa...</p>
        </div>
      </div>
    );
  }

  // Ako nema pristup, ne prikazuj ništa (redirect će se desiti)
  if (!hasAccess) {
    return null;
  }

  // Prikaži profil ako korisnik ima pristup
  return <BusinessProfileClient profile={profile} />;
}
