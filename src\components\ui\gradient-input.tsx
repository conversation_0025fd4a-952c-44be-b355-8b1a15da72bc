import React from 'react';
import { cn } from '@/lib/utils';
import { Input, InputProps } from '@/components/ui/input';

interface GradientInputProps extends InputProps {
  error?: boolean;
}

const GradientInput = React.forwardRef<HTMLInputElement, GradientInputProps>(
  ({ className, error = false, ...props }, ref) => {
    return (
      <Input
        ref={ref}
        className={cn(
          'bg-white/10 border-white/30 text-white placeholder:text-white/50 focus:border-white/50 focus:ring-white/20',
          error && 'border-red-400',
          className
        )}
        {...props}
      />
    );
  }
);

GradientInput.displayName = 'GradientInput';

export { GradientInput };
