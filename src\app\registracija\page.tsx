'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { ArrowLeft, User, Building2 } from 'lucide-react';
import Image from 'next/image';

export default function RegistracijaPage() {
  const router = useRouter();
  const [selectedType, setSelectedType] = useState<
    'influencer' | 'business' | null
  >(null);

  const handleContinue = () => {
    if (selectedType) {
      router.push(`/registracija/${selectedType}`);
    }
  };

  return (
    <div className="min-h-screen bg-instagram-secondary relative overflow-hidden flex flex-col">
      {/* Background decorative elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/10"></div>
      <div className="absolute top-10 right-10 w-64 h-64 bg-white/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-10 left-10 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>

      {/* Header */}
      <header className="relative z-10 border-b border-white/20 backdrop-blur-sm bg-white/10">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link
            href="/"
            className="flex items-center space-x-2 text-white/80 hover:text-white transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Nazad</span>
          </Link>
          <div className="flex items-center space-x-3">
            <Image
              src="/images/influexus_logo_white.webp"
              alt="Influexus Logo"
              width={32}
              height={32}
              className="rounded-lg"
            />
            <span className="text-xl font-bold gradient-text-auth">Influexus</span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 flex-1 flex items-center justify-center px-4 py-12">
        <div className="w-full max-w-5xl space-y-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-white mb-2">Registracija</h1>
            <p className="text-white/70">
              Izaberite tip korisnika da biste nastavili
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
            {/* Influencer Card */}
            <Card
              className={`cursor-pointer transition-all duration-300 glass-instagram ${
                selectedType === 'influencer'
                  ? 'ring-4 ring-white border-2 border-white shadow-2xl bg-white/20 scale-105'
                  : 'border-white/20 hover:border-white/40 hover:shadow-lg hover:bg-white/10'
              }`}
              onClick={() => setSelectedType('influencer')}
            >
              <CardHeader className="text-center">
                <div
                  className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 backdrop-blur-sm transition-all duration-300 ${
                    selectedType === 'influencer'
                      ? 'bg-white/40 shadow-lg'
                      : 'bg-white/20'
                  }`}
                >
                  <User
                    className={`h-8 w-8 transition-all duration-300 ${
                      selectedType === 'influencer'
                        ? 'text-white scale-110'
                        : 'text-white'
                    }`}
                  />
                </div>
                <CardTitle className="text-xl text-white">Influencer</CardTitle>
                <CardDescription className="text-white/70">
                  Zarađujem kroz kreiranje sadržaja i promociju brendova
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="text-sm text-white/80 space-y-2">
                  <li className="flex items-center space-x-2">
                    <span className="text-green-300">✓</span>
                    <span>Pronađi kampanje koje odgovaraju tvojoj niši</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="text-green-300">✓</span>
                    <span>Postavi svoje cijene i uslove</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="text-green-300">✓</span>
                    <span>Sigurno plaćanje kroz escrow sistem</span>
                  </li>
                </ul>
              </CardContent>
            </Card>

            {/* Business Card */}
            <Card
              className={`cursor-pointer transition-all duration-300 glass-instagram ${
                selectedType === 'business'
                  ? 'ring-4 ring-white border-2 border-white shadow-2xl bg-white/20 scale-105'
                  : 'border-white/20 hover:border-white/40 hover:shadow-lg hover:bg-white/10'
              }`}
              onClick={() => setSelectedType('business')}
            >
              <CardHeader className="text-center">
                <div
                  className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 backdrop-blur-sm transition-all duration-300 ${
                    selectedType === 'business'
                      ? 'bg-white/40 shadow-lg'
                      : 'bg-white/20'
                  }`}
                >
                  <Building2
                    className={`h-8 w-8 transition-all duration-300 ${
                      selectedType === 'business'
                        ? 'text-white scale-110'
                        : 'text-white'
                    }`}
                  />
                </div>
                <CardTitle className="text-xl text-white">Biznis</CardTitle>
                <CardDescription className="text-white/70">
                  Kreiram kampanje za promociju svojih proizvoda ili usluga
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="text-sm text-white/80 space-y-2">
                  <li className="flex items-center space-x-2">
                    <span className="text-green-300">✓</span>
                    <span>Kreiraj kampanje za svaki budžet</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="text-green-300">✓</span>
                    <span>Biraj između stotina lokalnih influencera</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <span className="text-green-300">✓</span>
                    <span>Transparentno praćenje rezultata</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Continue Button */}
          <div className="max-w-md mx-auto">
            <Button
              onClick={handleContinue}
              disabled={!selectedType}
              className="w-full bg-white text-instagram-purple hover:bg-white/90 font-semibold shadow-lg hover:shadow-xl transition-all duration-300 disabled:bg-white/50 disabled:text-instagram-purple/50"
              size="lg"
            >
              Nastavi →
            </Button>

            {/* Login Link */}
            <div className="text-center mt-6">
              <p className="text-sm text-white/70">
                Već imate nalog?{' '}
                <Link
                  href="/prijava"
                  className="text-white hover:underline font-medium transition-colors"
                >
                  Prijavite se
                </Link>
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
