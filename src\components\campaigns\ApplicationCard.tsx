import React from 'react';
import { Badge } from '@/components/ui/badge';
import {
  Calendar,
  Euro,
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  User,
} from 'lucide-react';
import Link from 'next/link';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { getDisplayName, getInitials } from '@/lib/utils';

interface Application {
  id: string;
  campaign_id: string;
  influencer_id: string;
  status: 'pending' | 'accepted' | 'rejected';
  proposed_rate: number;
  proposal_text: string;
  delivery_timeframe: string;
  portfolio_links: string[] | null;
  experience_relevant: string | null;
  audience_insights: string | null;
  applied_at: string;
  campaigns: {
    id: string;
    title: string;
    budget: number;
    business_id: string;
  };
  profiles: {
    id: string;
    username: string;
    full_name: string | null;
    public_display_name: string | null;
    avatar_url: string | null;
  };
}

interface ApplicationCardProps {
  application: Application;
}

const ApplicationCard: React.FC<ApplicationCardProps> = ({ application }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-800 border-yellow-200';
      case 'accepted':
        return 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-200';
      case 'rejected':
        return 'bg-gradient-to-r from-red-100 to-pink-100 text-red-800 border-red-200';
      default:
        return 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'accepted':
        return <CheckCircle className="h-4 w-4" />;
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Na čekanju';
      case 'accepted':
        return 'Prihvaćeno';
      case 'rejected':
        return 'Odbačeno';
      default:
        return status;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('sr-RS', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  return (
    <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 group hover:scale-[1.02] h-[420px] w-full flex flex-col">
      {/* Dreamy gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60 group-hover:opacity-80 transition-opacity duration-300" />

      {/* Subtle glow effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-purple-300/20 via-pink-300/10 to-purple-400/20 dark:from-purple-600/10 dark:via-pink-600/5 dark:to-purple-500/10 blur-xl opacity-50 group-hover:opacity-70 transition-opacity duration-300" />

      <div className="relative p-6 flex flex-col h-full">
        {/* Header with avatar and status - Fixed height */}
        <div className="flex items-start justify-between mb-4 h-16">
          <Link 
            href={`/influencer/${application.profiles.username}`}
            className="flex items-center space-x-3 hover:opacity-80 transition-opacity cursor-pointer min-w-0 flex-1"
          >
            <Avatar className="h-12 w-12 border-2 border-white/50 flex-shrink-0">
              <AvatarImage
                src={application.profiles.avatar_url || ''}
                alt={getDisplayName(application.profiles)}
              />
              <AvatarFallback className="bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700">
                {getInitials(getDisplayName(application.profiles))}
              </AvatarFallback>
            </Avatar>
            <div className="min-w-0 flex-1">
              <h3 className="text-lg font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent truncate">
                {getDisplayName(application.profiles)}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                @{application.profiles.username}
              </p>
            </div>
          </Link>

          <Badge
            variant="outline"
            className={`${getStatusColor(application.status)} font-medium flex-shrink-0 ml-2`}
          >
            <div className="flex items-center space-x-1">
              {getStatusIcon(application.status)}
              <span className="hidden sm:inline">{getStatusText(application.status)}</span>
            </div>
          </Badge>
        </div>

        {/* Campaign info - Fixed height */}
        <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30 mb-4 h-16">
          <p className="text-sm text-gray-600 dark:text-gray-400">Kampanja:</p>
          <p className="font-medium text-gray-900 dark:text-gray-100 truncate">
            {application.campaigns.title}
          </p>
        </div>

        {/* Details - Fixed height */}
        <div className="grid grid-cols-1 gap-2 mb-4 h-16">
          <div className="flex items-center gap-2 text-sm">
            <Euro className="w-4 h-4 text-purple-500 flex-shrink-0" />
            <span className="text-gray-600 dark:text-gray-400">Cijena:</span>
            <span className="font-semibold text-gray-900 dark:text-gray-100">
              {application.proposed_rate} €
            </span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Calendar className="w-4 h-4 text-pink-500 flex-shrink-0" />
            <span className="text-gray-600 dark:text-gray-400">Isporuka:</span>
            <span className="font-semibold text-gray-900 dark:text-gray-100 truncate">
              {application.delivery_timeframe}
            </span>
          </div>
        </div>

        {/* Applied date - Fixed height */}
        <div className="flex items-center gap-2 text-sm mb-4 h-6">
          <User className="w-4 h-4 text-purple-500 flex-shrink-0" />
          <span className="text-gray-600 dark:text-gray-400">Aplicirao:</span>
          <span className="font-semibold text-gray-900 dark:text-gray-100">
            {formatDate(application.applied_at)}
          </span>
        </div>

        {/* Proposal preview - Limited height */}
        <div className="mb-4 h-20">
          {application.proposal_text && (
            <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30 h-full">
              <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200 mb-1">
                Poruka:
              </h4>
              <div className="h-10 overflow-hidden">
                <p className="text-sm text-gray-700 dark:text-gray-300 line-clamp-2 leading-5">
                  {application.proposal_text}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Action Button - Fixed at bottom */}
        <div className="mt-auto">
          <Link
            href={`/dashboard/biznis/applications/${application.id}`}
            className="w-full"
          >
            <button className="flex items-center justify-center gap-2 w-full px-3 py-2 text-sm font-medium text-white bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 hover:from-purple-500 hover:via-pink-500 hover:to-purple-600 rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-purple-200/50 dark:hover:shadow-purple-900/30">
              <Eye className="w-4 h-4" />
              Detaljan pregled
            </button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ApplicationCard;
