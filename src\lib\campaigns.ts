import { supabase } from './supabase';
import { upsertApplicationChatPermission } from './chat-permissions';
import {
  notifyCampaignApplication,
  notifyCampaignAccepted,
  notifyCampaignRejected,
  notifyApplicationAcceptedPaymentPending,
  notifyPaymentCompletedWorkReady,
} from './notifications';
import { Database } from './database.types';
import {
  Campaign,
  CampaignWithBusiness,
  CampaignDetails,
  CampaignApplication,
  ApplicationWithDetails,
  InfluencerApplicationResponse,
  CampaignFormData,
  ApplicationFormData,
  Business,
  Profile,
  Platform,
  Category,
} from './types';

type CampaignInsert = Database['public']['Tables']['campaigns']['Insert'];
type CampaignUpdate = Database['public']['Tables']['campaigns']['Update'];
type CampaignApplicationInsert =
  Database['public']['Tables']['campaign_applications']['Insert'];

// Campaign CRUD operations
export async function createCampaign(campaign: CampaignInsert) {
  const { data, error } = await supabase
    .from('campaigns')
    .insert(campaign)
    .select();

  // Return first item if data is array, otherwise return as is
  const result = data && Array.isArray(data) ? data[0] : data;

  return { data: result, error };
}

export async function getCampaign(id: string) {
  const { data, error } = await supabase
    .from('campaigns')
    .select(
      `
      *,
      businesses!inner(
        company_name,
        industry,
        profiles!inner(
          username,
          avatar_url
        )
      )
    `
    )
    .eq('id', id)
    .single();

  return { data, error };
}

export async function getCampaignWithDetails(
  id: string
): Promise<{ data: CampaignDetails | null; error: any }> {
  try {
    console.log('[PERF] getCampaignWithDetails starting - using optimized RPC');
    const startTime = performance.now();

    // Koristimo našu optimizovanu RPC funkciju
    const { data: rpcData, error: rpcError } = await supabase.rpc(
      'get_campaign_details' as any,
      { p_campaign_id: id }
    );

    const endTime = performance.now();
    console.log(
      `[PERF] getCampaignWithDetails completed in ${(endTime - startTime).toFixed(2)}ms`
    );

    if (rpcError) {
      console.error('Campaign details RPC error:', rpcError);
      return { data: null, error: rpcError };
    }

    if (!rpcData || rpcData.length === 0) {
      return { data: null, error: { message: 'Campaign not found' } };
    }

    const campaign = rpcData[0];

    // Transform RPC rezultat u CampaignDetails format
    const transformedData = {
      id: campaign.id,
      title: campaign.title,
      description: campaign.description,
      budget: campaign.budget,
      status: campaign.status as 'draft' | 'active' | 'paused' | 'completed' | 'cancelled',
      location: campaign.location,
      application_deadline: campaign.application_deadline,
      min_followers: campaign.min_followers,
      max_followers: campaign.max_followers,
      age_range_min: campaign.age_range_min,
      age_range_max: campaign.age_range_max,
      gender: campaign.gender,
      is_featured: campaign.is_featured,
      created_at: campaign.created_at,
      business_id: campaign.business_id,
      content_types: campaign.content_types ? (typeof campaign.content_types === 'string' ? campaign.content_types.split(',') : campaign.content_types) as ('post' | 'story' | 'reel' | 'video' | 'blog')[] : [],
      hashtags: Array.isArray(campaign.hashtags) ? campaign.hashtags : [],
      do_not_mention: Array.isArray(campaign.do_not_mention)
        ? campaign.do_not_mention
        : [],
      additional_notes: campaign.additional_notes || null,
      collaboration_type: campaign.collaboration_type || 'paid',
      platforms: Array.isArray(campaign.platforms) ? campaign.platforms : [],
      categories: Array.isArray(campaign.categories) ? campaign.categories : [],
      applications_count: Number(campaign.applications_count) || 0,
      // Flatten business profile fields for easier access
      company_name: campaign.business_name,
      business_username: campaign.business_username,
      business_avatar: campaign.business_avatar,
      industry: campaign.business_industry,
      // Legacy business object for compatibility
      business: {
        id: campaign.business_id,
        company_name: campaign.business_name,
        industry: campaign.business_industry,
        profile: {
          id: campaign.business_id,
          username: campaign.business_username,
          avatar_url: campaign.business_avatar,
        } as any,
      } as any,
    };

    return { data: transformedData as CampaignDetails, error: null };
  } catch (error) {
    console.error('Error in getCampaignWithDetails:', error);
    return { data: null, error };
  }
}

export async function deleteCampaign(id: string) {
  const { data, error } = await supabase
    .from('campaigns')
    .delete()
    .eq('id', id);

  return { data, error };
}

// Business campaigns
export async function getBusinessCampaigns(
  businessId: string,
  status?: string
) {
  let query = supabase
    .from('campaigns')
    .select(
      `
      *,
      campaign_applications(count)
    `
    )
    .eq('business_id', businessId)
    .order('created_at', { ascending: false });

  if (status) {
    query = query.eq('status', status);
  }

  const { data, error } = await query;
  return { data, error };
}

// Campaign search and filtering for influencers
export interface CampaignFilters {
  search?: string;
  categories?: number[];
  platforms?: number[];
  minBudget?: number;
  maxBudget?: number;
  location?: string;
  minFollowers?: number;
  maxFollowers?: number;
  gender?: string;
  deadlineBefore?: string;
  sortBy?:
    | 'created_at'
    | 'budget'
    | 'application_deadline'
    | 'applications_count';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

// Interface za RPC response
interface CampaignRPCResponse {
  id: string;
  title: string;
  description: string;
  budget: number;
  status: string;
  location: string | null;
  application_deadline: string | null;
  min_followers: number | null;
  max_followers: number | null;
  age_range_min: number | null;
  age_range_max: number | null;
  gender: string | null;
  is_featured: boolean;
  created_at: string;
  business_id: string;
  content_types: string; // Changed to string (comma-separated)
  hashtags: string[] | null;
  do_not_mention: string[] | null;
  additional_notes: string | null;
  collaboration_type: string | null;
  business_name: string;
  business_industry: string;
  business_username: string;
  business_avatar: string | null;
  platforms: any[];
  categories: any[];
  applications_count: number;
}

// Optimizovana funkcija za kartice kampanja (SAMO osnovni podaci)
export async function searchCampaignsCards(filters: CampaignFilters = {}) {
  const {
    search,
    categories,
    platforms,
    minBudget,
    maxBudget,
    location,
    sortBy = 'created_at',
    sortOrder = 'desc',
    limit = 20,
    offset = 0,
  } = filters;

  try {
    console.log(
      '[PERF] searchCampaignsCards starting - optimized for cards only'
    );
    const startTime = performance.now();

    // Koristimo NOVU optimizovanu RPC funkciju za kartice
    const { data: rpcData, error: rpcError } = await supabase.rpc(
      'get_campaigns_cards' as any,
      {
        p_search_query: search || null,
        p_categories: categories || null,
        p_platforms: platforms || null,
        p_min_budget: minBudget || null,
        p_max_budget: maxBudget || null,
        p_location: location || null,
        p_sort_by: sortBy,
        p_sort_order: sortOrder,
        p_limit: limit,
        p_offset: offset,
        p_featured_only: false, // false znači da uzimamo SAMO ne-featured kampanje
      }
    );

    const endTime = performance.now();
    console.log(
      `[PERF] searchCampaignsCards RPC completed in ${(endTime - startTime).toFixed(2)}ms`
    );

    if (rpcError) {
      console.error('RPC error:', rpcError);
      return { data: [], error: rpcError };
    }

    // Jednostavna transformacija - SAMO za kartice
    const transformedCampaigns = (rpcData || []).map(campaign => ({
      id: campaign.id,
      title: campaign.title,
      description: campaign.description,
      budget: campaign.budget,
      is_featured: campaign.is_featured,
      created_at: campaign.created_at,
      platforms: Array.isArray(campaign.platforms) ? campaign.platforms : [],
      applications_count: Number(campaign.applications_count) || 0,
      businesses: {
        company_name: campaign.business_name,
      },
    }));

    const finalTime = performance.now();
    console.log(
      `[PERF] searchCampaignsCards completed in ${(finalTime - startTime).toFixed(2)}ms - ${transformedCampaigns.length} cards`
    );

    return { data: transformedCampaigns, error: null };
  } catch (error) {
    console.error('Search campaigns cards error:', error);
    return { data: [], error };
  }
}

// Funkcija za detaljne podatke kampanje (kada korisnik klikne na karticu)
export async function getCampaignCardDetails(campaignId: string) {
  try {
    console.log('[PERF] getCampaignCardDetails starting');
    const startTime = performance.now();

    const { data: rpcData, error: rpcError } = await supabase.rpc(
      'get_campaign_details' as any,
      { p_campaign_id: campaignId }
    );

    const endTime = performance.now();
    console.log(
      `[PERF] getCampaignCardDetails completed in ${(endTime - startTime).toFixed(2)}ms`
    );

    if (rpcError) {
      console.error('Campaign details RPC error:', rpcError);
      return { data: null, error: rpcError };
    }

    if (!rpcData || rpcData.length === 0) {
      return { data: null, error: { message: 'Campaign not found' } };
    }

    const campaign = rpcData[0];

    // Potpuna transformacija za detalje
    const transformedCampaign = {
      id: campaign.id,
      title: campaign.title,
      description: campaign.description,
      budget: campaign.budget,
      status: campaign.status as 'draft' | 'active' | 'paused' | 'completed' | 'cancelled',
      location: campaign.location,
      application_deadline: campaign.application_deadline,
      min_followers: campaign.min_followers,
      max_followers: campaign.max_followers,
      age_range_min: campaign.age_range_min,
      age_range_max: campaign.age_range_max,
      gender: campaign.gender,
      is_featured: campaign.is_featured,
      created_at: campaign.created_at,
      business_id: campaign.business_id,
      content_types: campaign.content_types
        ? campaign.content_types.split(',')
        : [],
      hashtags: Array.isArray(campaign.hashtags) ? campaign.hashtags : [],
      do_not_mention: Array.isArray(campaign.do_not_mention)
        ? campaign.do_not_mention
        : [],
      additional_notes: campaign.additional_notes || null,
      collaboration_type: campaign.collaboration_type || 'paid',
      platforms: Array.isArray(campaign.platforms) ? campaign.platforms : [],
      categories: Array.isArray(campaign.categories) ? campaign.categories : [],
      applications_count: Number(campaign.applications_count) || 0,
      businesses: {
        company_name: campaign.business_name,
        industry: campaign.business_industry,
        profiles: {
          username: campaign.business_username,
          avatar_url: campaign.business_avatar,
        },
      },
    };

    return { data: transformedCampaign, error: null };
  } catch (error) {
    console.error('Get campaign card details error:', error);
    return { data: null, error };
  }
}

// Legacy funkcija - zadržavamo za kompatibilnost
export async function searchCampaigns(filters: CampaignFilters = {}) {
  const {
    search,
    categories,
    platforms,
    minBudget,
    maxBudget,
    location,
    minFollowers,
    maxFollowers,
    gender,
    deadlineBefore,
    sortBy = 'created_at',
    sortOrder = 'desc',
    limit = 20,
    offset = 0,
  } = filters;

  try {
    // Koristimo optimizovanu RPC funkciju umesto N+1 queries
    const { data: rpcData, error: rpcError } = await supabase.rpc(
      'get_campaigns_paginated' as any,
      {
        p_search_query: search || null,
        p_categories: categories || null,
        p_platforms: platforms || null,
        p_min_budget: minBudget || null,
        p_max_budget: maxBudget || null,
        p_location: location || null,
        p_min_followers: minFollowers || null,
        p_max_followers: maxFollowers || null,
        p_gender: gender || null,
        p_deadline_before: deadlineBefore || null,
        p_sort_by: sortBy,
        p_sort_order: sortOrder,
        p_limit: limit,
        p_offset: offset,
        p_featured_only: false,
      }
    );

    if (rpcError) {
      console.error('RPC error:', rpcError);
      return { data: [], error: rpcError };
    }

    // Transformišemo podatke u format koji frontend očekuje
    const transformedCampaigns = (rpcData || []).map(campaign => ({
      id: campaign.id,
      title: campaign.title,
      description: campaign.description,
      budget: campaign.budget,
      status: campaign.status as 'draft' | 'active' | 'paused' | 'completed' | 'cancelled',
      location: campaign.location,
      application_deadline: campaign.application_deadline,
      min_followers: campaign.min_followers,
      max_followers: campaign.max_followers,
      age_range_min: campaign.age_range_min,
      age_range_max: campaign.age_range_max,
      gender: campaign.gender,
      is_featured: campaign.is_featured,
      created_at: campaign.created_at,
      business_id: campaign.business_id,
      content_types: campaign.content_types
        ? campaign.content_types.split(',')
        : [], // Parse comma-separated string
      platforms: Array.isArray(campaign.platforms) ? campaign.platforms : [],
      categories: Array.isArray(campaign.categories) ? campaign.categories : [],
      businesses: {
        company_name: campaign.business_name,
        industry: campaign.business_industry,
        profiles: {
          username: campaign.business_username,
          avatar_url: campaign.business_avatar,
        },
      },
    }));

    return { data: transformedCampaigns, error: null };
  } catch (error) {
    console.error('Search campaigns error:', error);
    return { data: [], error };
  }
}

// Get campaign for editing (only for business owners and draft campaigns)
export async function getCampaignForEdit(campaignId: string) {
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return { data: null, error: { message: 'User not authenticated' } };
  }

  // Get campaign with all related data
  const { data: campaign, error } = await supabase
    .from('campaigns')
    .select(
      `
      *,
      campaign_platforms (
        platform_id,
        platforms (name)
      ),
      campaign_categories (
        category_id,
        categories (name)
      )
    `
    )
    .eq('id', campaignId)
    .eq('business_id', user.id)
    .eq('status', 'draft')
    .single();

  if (error) {
    return { data: null, error };
  }

  if (!campaign) {
    return {
      data: null,
      error: { message: 'Campaign not found or not editable' },
    };
  }

  return { data: campaign, error: null };
}

// Update campaign (only for business owners and draft campaigns)
export async function updateCampaign(campaignId: string, campaignData: any) {
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return { data: null, error: { message: 'User not authenticated' } };
  }

  // First check if campaign exists and is editable
  const { data: existingCampaign, error: checkError } = await supabase
    .from('campaigns')
    .select('id, status, business_id')
    .eq('id', campaignId)
    .eq('business_id', user.id)
    .eq('status', 'draft')
    .single();

  if (checkError || !existingCampaign) {
    return {
      data: null,
      error: { message: 'Campaign not found or not editable' },
    };
  }

  // Update campaign
  const { data: updatedCampaign, error: updateError } = await supabase
    .from('campaigns')
    .update({
      title: campaignData.title,
      description: campaignData.description,
      budget: campaignData.budget,
      content_types: campaignData.content_types,
      min_followers: campaignData.min_followers,
      max_followers: campaignData.max_followers,
      age_range_min: campaignData.age_range_min,
      age_range_max: campaignData.age_range_max,
      gender: campaignData.gender,
      hashtags: campaignData.hashtags
        ? campaignData.hashtags.split(',').map((h: string) => h.trim())
        : null,
      do_not_mention: campaignData.doNotMention
        ? campaignData.doNotMention.split(',').map((d: string) => d.trim())
        : null,
      additional_notes: campaignData.additionalNotes || null,
      collaboration_type: campaignData.collaborationType || 'paid',
      updated_at: new Date().toISOString(),
    })
    .eq('id', campaignId)
    .select()
    .single();

  if (updateError) {
    return { data: null, error: updateError };
  }

  // Update platforms
  if (campaignData.platforms && campaignData.platforms.length > 0) {
    // Delete existing platforms
    await supabase
      .from('campaign_platforms')
      .delete()
      .eq('campaign_id', campaignId);

    // Insert new platforms
    const platformInserts = campaignData.platforms.map(
      (platformId: string) => ({
        campaign_id: campaignId,
        platform_id: platformId,
      })
    );

    await supabase.from('campaign_platforms').insert(platformInserts);
  }

  // Update categories
  if (campaignData.categories && campaignData.categories.length > 0) {
    // Delete existing categories
    await supabase
      .from('campaign_categories')
      .delete()
      .eq('campaign_id', campaignId);

    // Insert new categories
    const categoryInserts = campaignData.categories.map(
      (categoryId: string) => ({
        campaign_id: campaignId,
        category_id: categoryId,
      })
    );

    await supabase.from('campaign_categories').insert(categoryInserts);
  }

  return { data: updatedCampaign, error: null };
}

// Update campaign status (with validation)
export async function updateCampaignStatus(
  campaignId: string,
  newStatus: 'draft' | 'active' | 'paused' | 'completed'
) {
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return { data: null, error: { message: 'User not authenticated' } };
  }

  // First check if campaign exists and user owns it
  const { data: existingCampaign, error: checkError } = await supabase
    .from('campaigns')
    .select('id, status, business_id')
    .eq('id', campaignId)
    .eq('business_id', user.id)
    .single();

  if (checkError || !existingCampaign) {
    return {
      data: null,
      error: { message: 'Campaign not found or access denied' },
    };
  }

  // Validation rules
  if (existingCampaign.status === 'active' && newStatus === 'draft') {
    // Check if campaign has any applications
    const { count: applicationCount, error: countError } = await supabase
      .from('campaign_applications')
      .select('*', { count: 'exact', head: true })
      .eq('campaign_id', campaignId);

    if (countError) {
      return {
        data: null,
        error: { message: 'Error checking applications' },
      };
    }

    if (applicationCount && applicationCount > 0) {
      return {
        data: null,
        error: { message: 'Cannot deactivate campaign that has applications' },
      };
    }
  }

  if (existingCampaign.status === 'completed') {
    return {
      data: null,
      error: { message: 'Cannot change status of completed campaign' },
    };
  }

  // Update status
  const { data: updatedCampaign, error: updateError } = await supabase
    .from('campaigns')
    .update({
      status: newStatus,
      updated_at: new Date().toISOString(),
    })
    .eq('id', campaignId)
    .select()
    .single();

  if (updateError) {
    return { data: null, error: updateError };
  }

  return { data: updatedCampaign, error: null };
}

// Get business campaigns for dashboard with counts
export async function getBusinessCampaignsForDashboard() {
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    console.log('User not authenticated');
    return { data: [], error: { message: 'User not authenticated' } };
  }

  console.log('Loading campaigns for business:', user.id);

  const { data: campaigns, error } = await supabase
    .from('campaigns')
    .select(
      `
      id,
      title,
      description,
      budget,
      status,
      created_at,
      content_types,
      campaign_applications(count)
    `
    )
    .eq('business_id', user.id)
    .order('created_at', { ascending: false });

  console.log('Campaigns query result:', { campaigns, error });

  if (error) {
    console.error('Error loading campaigns:', error);
    return { data: [], error };
  }

  return { data: campaigns || [], error: null };
}

// Featured campaigns - optimizovano za kartice
export async function getFeaturedCampaigns(limit: number = 6) {
  try {
    console.log('[PERF] getFeaturedCampaigns starting - optimized for cards');
    const startTime = performance.now();

    // Koristimo NOVU optimizovanu RPC funkciju za kartice
    const { data: rpcData, error: rpcError } = await supabase.rpc(
      'get_campaigns_cards' as any,
      {
        p_search_query: null,
        p_categories: null,
        p_platforms: null,
        p_min_budget: null,
        p_max_budget: null,
        p_location: null,
        p_sort_by: 'created_at',
        p_sort_order: 'desc',
        p_limit: limit,
        p_offset: 0,
        p_featured_only: true, // Samo featured kampanje
      }
    );

    const endTime = performance.now();
    console.log(
      `[PERF] getFeaturedCampaigns completed in ${(endTime - startTime).toFixed(2)}ms`
    );

    if (rpcError) {
      console.error('Featured campaigns RPC error:', rpcError);
      return { data: [], error: rpcError };
    }

    // Jednostavna transformacija - SAMO za kartice
    const transformedCampaigns = (rpcData || []).map(campaign => ({
      id: campaign.id,
      title: campaign.title,
      description: campaign.description,
      budget: campaign.budget,
      is_featured: campaign.is_featured,
      created_at: campaign.created_at,
      platforms: Array.isArray(campaign.platforms) ? campaign.platforms : [],
      applications_count: Number(campaign.applications_count) || 0,
      businesses: {
        company_name: campaign.business_name,
      },
    }));

    return { data: transformedCampaigns, error: null };
  } catch (error) {
    console.error('Featured campaigns error:', error);
    return { data: [], error };
  }
}

// Campaign applications
export async function createCampaignApplication(
  application: CampaignApplicationInsert
) {
  const { data, error } = await supabase
    .from('campaign_applications')
    .insert(application)
    .select()
    .single();

  if (error) {
    return { data, error };
  }

  // Pošalji notifikaciju business-u o novoj aplicaciji
  try {
    // Dobij informacije o campagin-u i business-u
    const { data: campaignData } = await supabase
      .from('campaigns')
      .select('title, business_id')
      .eq('id', application.campaign_id)
      .single();

    // Dobij informacije o influencer-u
    const { data: influencerProfile } = await supabase
      .from('profiles')
      .select('username')
      .eq('id', application.influencer_id)
      .single();

    if (campaignData && influencerProfile) {
      await notifyCampaignApplication(
        campaignData.business_id,
        influencerProfile.username,
        campaignData.title,
        data.id
      );
    }
  } catch (notificationError) {
    console.error('Error creating campaign application notification:', notificationError);
    // Ne prekidamo proces ako notifikacija ne uspije
  }

  return { data, error };
}

// Check if influencer already applied to campaign
export async function hasInfluencerApplied(
  campaignId: string,
  influencerId: string
): Promise<{ data: InfluencerApplicationResponse | null; error: any }> {
  const { data, error } = await supabase
    .from('campaign_applications')
    .select('*')
    .eq('campaign_id', campaignId)
    .eq('influencer_id', influencerId)
    .maybeSingle();

  if (error) {
    return { data: null, error };
  }

  const response: InfluencerApplicationResponse = {
    hasApplied: !!data,
    application: data || undefined,
  };

  return { data: response, error: null };
}

export async function getCampaignApplications(
  campaignId: string,
  status?: string
) {
  let query = supabase
    .from('campaign_applications')
    .select(
      `
      *,
      influencers!inner(
        *,
        profiles!inner(
          username,
          full_name,
          public_display_name,
          avatar_url,
          bio,
          city,
          country,
          age,
          gender
        )
      )
    `
    )
    .eq('campaign_id', campaignId)
    .order('applied_at', { ascending: false });

  if (status) {
    query = query.eq('status', status);
  }

  const { data, error } = await query;
  return { data, error };
}

export async function getInfluencerApplications(
  influencerId: string,
  status?: string
) {
  try {
    console.log('Fetching applications for influencer:', influencerId);

    let query = supabase
      .from('campaign_applications')
      .select(
        `
        id,
        campaign_id,
        influencer_id,
        status,
        proposed_rate,
        proposal_text,
        delivery_timeframe,
        portfolio_links,
        additional_services,
        available_start_date,
        experience_relevant,
        audience_insights,
        applied_at,
        campaigns!inner(
          id,
          title,
          description,
          budget,
          status,
          business_id
        )
      `
      )
      .eq('influencer_id', influencerId)
      .order('applied_at', { ascending: false });

    if (status) {
      query = query.eq('status', status as any);
    }

    const { data: applicationsData, error: applicationsError } = await query;

    if (applicationsError) {
      console.error('Error fetching applications:', applicationsError);
      throw applicationsError;
    }

    console.log('Applications data:', applicationsData);

    if (!applicationsData || applicationsData.length === 0) {
      return { data: [], error: null };
    }

    // Get business data separately
    const businessIds = applicationsData.map(app => app.campaigns.business_id);
    const { data: businessesData, error: businessesError } = await supabase
      .from('businesses')
      .select(
        `
        id,
        company_name
      `
      )
      .in('id', businessIds);

    if (businessesError) {
      console.error('Error fetching businesses:', businessesError);
      throw businessesError;
    }

    // Get business profiles
    const { data: profilesData, error: profilesError } = await supabase
      .from('profiles')
      .select('id, username, avatar_url')
      .in('id', businessIds);

    if (profilesError) {
      console.error('Error fetching business profiles:', profilesError);
    }

    console.log('Businesses data:', businessesData);
    console.log('Profiles data:', profilesData);

    // Transform data to match expected interface
    const transformedData = applicationsData.map(app => {
      const business = businessesData?.find(
        b => b.id === app.campaigns.business_id
      );
      const profile = profilesData?.find(
        p => p.id === app.campaigns.business_id
      );

      return {
        id: app.id,
        campaign_id: app.campaign_id,
        status: app.status,
        proposed_rate: app.proposed_rate,
        proposal_text: app.proposal_text,
        portfolio_links: app.portfolio_links,
        delivery_timeframe: app.delivery_timeframe,
        additional_services: app.additional_services,
        applied_at: app.applied_at,
        campaigns: {
          title: app.campaigns.title,
          description: app.campaigns.description,
          budget: app.campaigns.budget,
          status: app.campaigns.status,
          businesses: {
            company_name: business?.company_name || 'Unknown Company',
            profiles: {
              username: profile?.username || 'Unknown',
              avatar_url: profile?.avatar_url || null,
            },
          },
        },
      };
    });

    return { data: transformedData, error: null };
  } catch (error: any) {
    console.error('Error in getInfluencerApplications:', error);
    return { data: null, error: error.message };
  }
}

// Campaign platforms and categories
export async function addCampaignPlatforms(
  campaignId: string,
  platforms: Array<{
    platform_id: number;
    content_type_ids: number[];
    posts_required?: number;
    budget_per_post?: number;
  }>
) {
  const platformData = platforms.map(platform => ({
    campaign_id: campaignId,
    ...platform,
  }));

  const { data, error } = await supabase
    .from('campaign_platforms')
    .insert(platformData as any)
    .select();

  return { data, error };
}

export async function addCampaignCategories(
  campaignId: string,
  categoryIds: number[]
) {
  const categoryData = categoryIds.map(categoryId => ({
    campaign_id: campaignId,
    category_id: categoryId,
  }));

  const { data, error } = await supabase
    .from('campaign_categories')
    .insert(categoryData)
    .select();

  return { data, error };
}

export async function removeCampaignPlatforms(campaignId: string) {
  const { data, error } = await supabase
    .from('campaign_platforms')
    .delete()
    .eq('campaign_id', campaignId);

  return { data, error };
}

export async function removeCampaignCategories(campaignId: string) {
  const { data, error } = await supabase
    .from('campaign_categories')
    .delete()
    .eq('campaign_id', campaignId);

  return { data, error };
}

// Utility functions

export async function refreshCampaignsSearchView() {
  const { data, error } = await supabase.rpc('refresh_campaigns_search_view' as any);

  return { data, error };
}

// Get campaign statistics for business dashboard
export async function getCampaignStats(businessId: string) {
  const { data: campaigns, error: campaignsError } = await supabase
    .from('campaigns')
    .select('status')
    .eq('business_id', businessId);

  if (campaignsError) return { data: null, error: campaignsError };

  const { data: applications, error: applicationsError } = await supabase
    .from('campaign_applications')
    .select('status, campaign_id')
    .in('campaign_id', campaigns?.map((c: any) => c.id) || []);

  if (applicationsError) return { data: null, error: applicationsError };

  const stats = {
    totalCampaigns: campaigns?.length || 0,
    activeCampaigns: campaigns?.filter(c => c.status === 'active').length || 0,
    completedCampaigns:
      campaigns?.filter(c => c.status === 'completed').length || 0,
    totalApplications: applications?.length || 0,
    pendingApplications:
      applications?.filter(a => a.status === 'pending').length || 0,
    acceptedApplications:
      applications?.filter(a => a.status === 'accepted').length || 0,
  };

  return { data: stats, error: null };
}

// Get all applications for business campaigns
export async function getBusinessCampaignApplications(
  businessId: string,
  status?: string
) {
  try {
    console.log('Fetching applications for business:', businessId);

    // Check user's subscription type first
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      return { data: null, error: { message: 'User not authenticated' } };
    }

    // Get user's subscription type
    const { data: business } = await supabase
      .from('businesses')
      .select('subscription_type')
      .eq('id', businessId)
      .single();

    const subscriptionType =
      (business?.subscription_type as 'free' | 'premium') || 'free';

    // Import subscription check function here to avoid circular imports
    const { canViewApplications } = await import('./subscriptions');

    // Use the correct field names from the actual database schema
    let query = supabase
      .from('campaign_applications')
      .select(
        `
        id,
        campaign_id,
        influencer_id,
        status,
        proposed_rate,
        proposal_text,
        delivery_timeframe,
        portfolio_links,
        additional_services,
        available_start_date,
        experience_relevant,
        audience_insights,
        applied_at,
        campaigns!inner(
          id,
          title,
          budget,
          business_id
        )
      `
      )
      .eq('campaigns.business_id', businessId)
      .order('applied_at', { ascending: false });

    if (status) {
      query = query.eq('status', status as any);
    }

    const { data: applicationsData, error: applicationsError } = await query;

    if (applicationsError) {
      console.error('Error fetching applications:', applicationsError);
      throw applicationsError;
    }

    console.log('Applications data:', applicationsData);

    if (!applicationsData || applicationsData.length === 0) {
      return { data: [], error: null };
    }

    // If free user, generate placeholder applications instead of real data
    if (!canViewApplications(subscriptionType)) {
      console.log('Free user - generating placeholder applications');

      // Create placeholder applications that match the real count and status distribution
      const placeholderApps = applicationsData.map((app, index) => ({
        id: `placeholder-${index + 1}`,
        campaign_id: app.campaign_id,
        influencer_id: `placeholder-influencer-${index + 1}`,
        status: app.status, // Keep real status for accurate tab counts
        proposed_rate: Math.floor(Math.random() * 200) + 50, // Random rate 50-250
        proposal_text: 'Nadogradite na Premium da vidite poruku aplikacije.',
        delivery_timeframe: 'Ograničeno',
        portfolio_links: [],
        experience_relevant: 'Premium funkcionalnost potrebna.',
        audience_insights: 'Premium funkcionalnost potrebna.',
        additional_services: '',
        available_start_date: null,
        applied_at: app.applied_at, // Keep real date for sorting
        campaigns: {
          id: app.campaigns.id,
          title: app.campaigns.title, // Keep real campaign title
          budget: app.campaigns.budget, // Keep real budget
          business_id: app.campaigns.business_id,
        },
        profiles: {
          id: `placeholder-profile-${index + 1}`,
          username: `premium_user_${index + 1}`,
          full_name: `Premium Influencer ${index + 1}`,
          public_display_name: `Premium Influencer ${index + 1}`,
          avatar_url: null,
        },
      }));

      return { data: placeholderApps, error: null };
    }

    // For premium users, get real influencer data
    const influencerIds = applicationsData.map(app => app.influencer_id);
    const { data: influencersData, error: influencersError } = await supabase
      .from('influencers')
      .select(
        `
        id,
        profiles!inner(
          id,
          username,
          full_name,
          public_display_name,
          avatar_url
        )
      `
      )
      .in('id', influencerIds);

    if (influencersError) {
      console.error('Error fetching influencers:', influencersError);
      throw influencersError;
    }

    console.log('Influencers data:', influencersData);

    // Transform data to match expected interface
    const transformedData = applicationsData?.map(app => {
      const influencer = influencersData?.find(
        inf => inf.id === app.influencer_id
      );
      return {
        id: app.id,
        campaign_id: app.campaign_id,
        influencer_id: app.influencer_id,
        status: app.status,
        proposed_rate: app.proposed_rate,
        proposal_text: app.proposal_text,
        delivery_timeframe: app.delivery_timeframe || 'Nije specificirano',
        portfolio_links: app.portfolio_links || [],
        experience_relevant: app.experience_relevant || app.proposal_text,
        audience_insights: app.audience_insights || '',
        additional_services: app.additional_services || '',
        available_start_date: app.available_start_date,
        applied_at: app.applied_at,
        campaigns: {
          id: app.campaigns.id,
          title: app.campaigns.title,
          budget: app.campaigns.budget,
          business_id: app.campaigns.business_id,
        },
        profiles: {
          id: influencer?.profiles?.id || app.influencer_id,
          username: influencer?.profiles?.username || 'Unknown',
          full_name: influencer?.profiles?.full_name || 'Unknown User',
          public_display_name:
            influencer?.profiles?.public_display_name || null,
          avatar_url: influencer?.profiles?.avatar_url || null,
        },
      };
    });

    return { data: transformedData, error: null };
  } catch (error: any) {
    console.error('Error fetching campaign applications:', error);
    return { data: null, error: error.message };
  }
}

// Get single application with detailed info
export async function getCampaignApplication(
  applicationId: string
): Promise<{ data: ApplicationWithDetails | null; error: any }> {
  try {
    // Check if user can view application details based on subscription
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      return { data: null, error: { message: 'User not authenticated' } };
    }

    // First get the application to check ownership
    const { data: appCheck, error: appCheckError } = await supabase
      .from('campaign_applications')
      .select(
        `
        id,
        influencer_id,
        campaigns!inner(business_id)
      `
      )
      .eq('id', applicationId)
      .single();

    if (appCheckError || !appCheck) {
      return { data: null, error: appCheckError };
    }

    // Check if current user is either the influencer (owns the application) or the business (owns the campaign)
    const isInfluencer = appCheck.influencer_id === user.id;
    const isBusiness = appCheck.campaigns.business_id === user.id;

    if (!isInfluencer && !isBusiness) {
      return { data: null, error: { message: 'Access denied' } };
    }

    // If user is the influencer who applied, they can always see their own application
    if (isInfluencer) {
      // Skip subscription check for influencers viewing their own applications
    } else if (isBusiness) {
      // For business users, check subscription
      const { data: business } = await supabase
        .from('businesses')
        .select('subscription_type')
        .eq('id', user.id)
        .single();

      const subscriptionType =
        (business?.subscription_type as 'free' | 'premium') || 'free';

      // Import subscription check function here to avoid circular imports
      const { canViewApplicationDetails } = await import('./subscriptions');

      if (!canViewApplicationDetails(subscriptionType)) {
        // Return limited/placeholder data for free users
        return {
          data: {
            id: applicationId,
            campaign_id: 'restricted',
            influencer_id: 'restricted',
            status: 'pending' as const,
            proposed_rate: 0,
            proposal_text:
              'Premium funkcionalnost potrebna za pregled detalja.',
            delivery_timeframe: 'Ograničeno',
            portfolio_links: [],
            experience_relevant: 'Nadogradite na Premium za pregled.',
            audience_insights: 'Nadogradite na Premium za pregled.',
            applied_at: new Date().toISOString(),
            campaign: {
              id: 'restricted',
              title: 'Premium funkcionalnost potrebna',
              description:
                'Nadogradite na Premium da biste videli detalje kampanje.',
              budget: 0,
              requirements: 'Ograničeno',
              deliverables: 'Ograničeno',
              business_id: user.id,
            } as any,
            influencer: {
              id: 'restricted',
              profile: {
                id: 'restricted',
                full_name: 'Premium korisnik',
                username: 'premium_required',
                avatar_url: null,
                bio: 'Nadogradite na Premium za pregled profila.',
                city: 'Ograničeno',
                country: 'Ograničeno',
                age: null,
                gender: 'other',
              } as any,
              categories: ['Premium funkcionalnost'],
              platforms: [
                {
                  platform_name: 'Ograničeno',
                  handle: 'premium_required',
                  followers_count: 0,
                },
              ],
            },
          },
          error: null,
        };
      }
    }

    // If we reach here, user can view the application (either influencer viewing own or business with premium)
    const { data, error } = await supabase
      .from('campaign_applications')
      .select(
        `
        id,
        campaign_id,
        influencer_id,
        status,
        proposed_rate,
        proposal_text,
        portfolio_links,
        delivery_timeframe,
        additional_services,
        experience_relevant,
        audience_insights,
        applied_at,
        campaigns!inner(
          id,
          title,
          description,
          budget,
          requirements,
          deliverables,
          business_id,
          hashtags,
          do_not_mention,
          additional_notes,
          start_date,
          end_date
        ),
        influencers!campaign_applications_influencer_id_fkey(
          id,
          profiles!influencers_id_fkey(
            id,
            full_name,
            public_display_name,
            username,
            avatar_url,
            bio,
            city,
            country,
            age,
            gender
          )
        )
      `
      )
      .eq('id', applicationId)
      .single();

    if (error) throw error;

    // Get campaign platforms
    const { data: campaignPlatformsData } = await supabase
      .from('campaign_platforms')
      .select(
        `
        content_types,
        platforms!inner(
          id,
          name,
          slug,
          icon
        )
      `
      )
      .eq('campaign_id', data.campaigns.id);

    const campaignPlatforms =
      campaignPlatformsData?.map(cp => ({
        ...cp.platforms,
        content_types: cp.content_types,
      })) || [];

    // Get campaign categories
    const { data: campaignCategoriesData } = await supabase
      .from('campaign_categories')
      .select('categories(id, name, icon)')
      .eq('campaign_id', data.campaigns.id);

    const campaignCategories =
      campaignCategoriesData?.map(cc => cc.categories) || [];

    // Get business data with categories
    const { data: businessData } = await supabase
      .from('businesses')
      .select('id, company_name')
      .eq('id', data.campaigns.business_id)
      .single();

    const { data: businessProfile } = await supabase
      .from('profiles')
      .select('username, avatar_url')
      .eq('id', data.campaigns.business_id)
      .single();

    const { data: businessCategoriesData } = await supabase
      .from('business_target_categories')
      .select(
        `
        categories (
          id,
          name,
          icon
        )
      `
      )
      .eq('business_id', data.campaigns.business_id);

    const businessCategories =
      businessCategoriesData?.map(bc => bc.categories) || [];

    // Get influencer categories and platforms
    const { data: categoriesData } = await supabase
      .from('influencer_categories')
      .select('categories(name)')
      .eq('influencer_id', data.influencer_id);

    const { data: platformsData } = await supabase
      .from('influencer_platforms')
      .select(
        `
        handle,
        followers_count,
        platforms(name)
      `
      )
      .eq('influencer_id', data.influencer_id);

    // Transform categories and platforms data
    const categories = categoriesData?.map(cat => cat.categories.name) || [];
    const platforms =
      platformsData?.map(platform => ({
        platform_name: platform.platforms.name,
        handle: platform.handle,
        followers_count: platform.followers_count,
      })) || [];

    // Transform data to match ApplicationWithDetails interface
    const transformedData = {
      ...data,
      campaign: {
        ...data.campaigns,
        business: {
          id: businessData?.id || data.campaigns.business_id,
          company_name: businessData?.company_name || 'Unknown Company',
          categories: businessCategories,
          profiles: {
            username: businessProfile?.username || 'unknown',
            avatar_url: businessProfile?.avatar_url || null,
          },
        } as any,
        platforms: campaignPlatforms as any,
        categories: campaignCategories as any,
      },
      influencer: {
        ...data.influencers,
        profile: data.influencers.profiles as any,
        categories: categories as any,
        platforms: platforms as any,
      },
    };

    return { data: transformedData as ApplicationWithDetails, error: null };
  } catch (error: any) {
    console.error('Error fetching campaign application:', error);
    return { data: null, error: error.message };
  }
}

// Update application status
export async function updateApplicationStatus(
  applicationId: string,
  status: 'accepted' | 'rejected',
  rejectionReason?: string
) {
  try {
    const updateData: any = { status };

    if (status === 'rejected' && rejectionReason) {
      updateData.rejection_reason = rejectionReason;
    }

    const { data, error } = await supabase
      .from('campaign_applications')
      .update(updateData)
      .eq('id', applicationId)
      .select(
        `
        *,
        campaigns!inner(business_id)
      `
      )
      .single();

    if (error) throw error;

    // Kreiraj chat dozvolu kada se aplikacija prihvati
    if (status === 'accepted' && data) {
      try {
        // Influencer je već approved kad se aplicirao, business NEĆE biti approved dok ne plati
        await upsertApplicationChatPermission(
          data.campaigns.business_id,
          data.influencer_id,
          data.id,
          false, // business_approved - NEĆE biti approved dok business ne plati
          true // influencer_approved - influencer je već odobren kad se aplicirao
        );
      } catch (chatError) {
        console.error('Error creating chat permission:', chatError);
        // Ne prekidamo proces ako chat dozvola ne uspije
      }
    }

    // Pošalji notifikaciju influenceru
    try {
      const { data: campaignData } = await supabase
        .from('campaigns')
        .select('title')
        .eq('id', data.campaign_id)
        .single();

      const { data: businessData } = await supabase
        .from('profiles')
        .select('username')
        .eq('id', data.campaigns.business_id)
        .single();

      if (campaignData && businessData) {
        if (status === 'accepted') {
          // Notifikacija o prihvaćenoj aplikaciji
          await notifyCampaignAccepted(
            data.influencer_id,
            businessData.username,
            campaignData.title,
            applicationId
          );

          // Nova notifikacija - čeka se plaćanje
          await notifyApplicationAcceptedPaymentPending(
            data.influencer_id,
            businessData.username,
            campaignData.title,
            applicationId
          );
        } else {
          // Notifikacija o odbačenoj aplikaciji
          await notifyCampaignRejected(
            data.influencer_id,
            businessData.username,
            campaignData.title,
            applicationId
          );
        }
      }
    } catch (notificationError) {
      console.error('Error creating notification:', notificationError);
      // Ne prekidamo proces ako notifikacija ne uspije
    }

    return { data, error: null };
  } catch (error: any) {
    console.error('Error updating application status:', error);
    return { data: null, error: error.message };
  }
}

// OPTIMIZED FUNCTIONS FOR BUSINESS APPLICATIONS CARDS

interface ApplicationCard {
  id: string;
  campaign_id: string;
  influencer_id: string;
  status: 'pending' | 'accepted' | 'rejected';
  proposed_rate: number;
  applied_at: string;
  campaign_title: string;
  campaign_budget: number;
  influencer_username: string;
  influencer_display_name: string;
  influencer_avatar_url: string | null;
}

interface ApplicationStats {
  total_count: number;
  pending_count: number;
  accepted_count: number;
  rejected_count: number;
}

// Optimized function for loading business applications cards (fast!)
export async function getBusinessApplicationsCards(
  businessId: string,
  status?: string,
  limit: number = 50,
  offset: number = 0
): Promise<{ data: ApplicationCard[] | null; error: any }> {
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      return { data: null, error: { message: 'User not authenticated' } };
    }

    // Check user's subscription type
    const { data: business } = await supabase
      .from('businesses')
      .select('subscription_type')
      .eq('id', businessId)
      .single();

    const subscriptionType =
      (business?.subscription_type as 'free' | 'premium') || 'free';
    const { canViewApplications } = await import('./subscriptions');

    // Call optimized RPC function
    // Cast status to proper enum type for PostgreSQL
    const statusParam = status
      ? (status as 'pending' | 'accepted' | 'rejected' | 'completed')
      : null;

    const { data, error } = await supabase.rpc(
      'get_business_applications_cards' as any,
      {
        p_business_id: businessId,
        p_status: statusParam,
        p_limit: limit,
        p_offset: offset,
      }
    );

    if (error) {
      console.error('Error fetching application cards:', error);
      return { data: null, error };
    }

    if (!data || (Array.isArray(data) && data.length === 0)) {
      return { data: [], error: null };
    }

    // If free user, generate placeholder data
    if (!canViewApplications(subscriptionType)) {
      const placeholderApps: ApplicationCard[] = data.map(
        (app: any, index: number) => ({
          id: `placeholder-${index + 1}`,
          campaign_id: app.campaign_id,
          influencer_id: `placeholder-influencer-${index + 1}`,
          status: app.status, // Keep real status for accurate tab counts
          proposed_rate: Math.floor(Math.random() * 200) + 50,
          applied_at: app.applied_at, // Keep real date for sorting
          campaign_title: app.campaign_title, // Keep real campaign title
          campaign_budget: app.campaign_budget, // Keep real budget
          influencer_username: `premium_user_${index + 1}`,
          influencer_display_name: `Premium Influencer ${index + 1}`,
          influencer_avatar_url: null,
        })
      );

      return { data: placeholderApps, error: null };
    }

    // Return real data for premium users
    return { data, error: null };
  } catch (error: any) {
    console.error('Error fetching application cards:', error);
    return { data: null, error: error.message };
  }
}

// Get application stats for tabs
export async function getBusinessApplicationsStats(
  businessId: string
): Promise<{ data: ApplicationStats | null; error: any }> {
  try {
    const { data, error } = await supabase.rpc(
      'count_business_applications_by_status',
      {
        p_business_id: businessId,
      }
    );

    if (error) {
      console.error('Error fetching application stats:', error);
      return { data: null, error };
    }

    // RPC returns array with single object
    const stats =
      data && data.length > 0
        ? data[0]
        : {
            total_count: 0,
            pending_count: 0,
            accepted_count: 0,
            rejected_count: 0,
          };

    return { data: stats, error: null };
  } catch (error: any) {
    console.error('Error fetching application stats:', error);
    return { data: null, error: error.message };
  }
}

interface ApplicationDetails {
  id: string;
  campaign_id: string;
  influencer_id: string;
  status: 'pending' | 'accepted' | 'rejected';
  proposed_rate: number;
  proposal_text: string;
  delivery_timeframe: string;
  portfolio_links: string[];
  experience_relevant: string;
  audience_insights: string;
  additional_services: string;
  available_start_date: string | null;
  applied_at: string;
  campaign_title: string;
  campaign_description: string;
  campaign_budget: number;
  campaign_location: string;
  influencer_username: string;
  influencer_full_name: string;
  influencer_display_name: string;
  influencer_avatar_url: string | null;
  influencer_bio: string;
}

// Lazy loading function for application details (only when user clicks)
export async function getApplicationDetails(
  applicationId: string
): Promise<{ data: ApplicationDetails | null; error: any }> {
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      return { data: null, error: { message: 'User not authenticated' } };
    }

    // Check user's subscription type
    const { data: business } = await supabase
      .from('businesses')
      .select('subscription_type')
      .eq('id', user.id)
      .single();

    const subscriptionType =
      (business?.subscription_type as 'free' | 'premium') || 'free';
    const { canViewApplications } = await import('./subscriptions');

    if (!canViewApplications(subscriptionType)) {
      return {
        data: null,
        error: { message: 'Premium subscription required' },
      };
    }

    // Call RPC function for detailed data
    const { data, error } = await supabase.rpc('get_application_details', {
      p_application_id: applicationId,
    });

    if (error) {
      console.error('Error fetching application details:', error);
      return { data: null, error };
    }

    // RPC returns array with single object
    const details = data && data.length > 0 ? data[0] : null;

    return { data: details, error: null };
  } catch (error: any) {
    console.error('Error fetching application details:', error);
    return { data: null, error: error.message };
  }
}

// Withdraw/Cancel Campaign Application
export async function withdrawCampaignApplication(
  applicationId: string
): Promise<{ data: any | null; error: any }> {
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      return { data: null, error: { message: 'User not authenticated' } };
    }

    // First check if the application exists and belongs to the current user
    const { data: application, error: checkError } = await supabase
      .from('campaign_applications')
      .select(
        `
        id,
        influencer_id,
        status,
        campaigns!inner(
          id,
          title,
          business_id
        )
      `
      )
      .eq('id', applicationId)
      .eq('influencer_id', user.id) // Only the influencer who applied can withdraw
      .single();

    if (checkError) {
      console.error('Error checking application:', checkError);
      return { data: null, error: checkError };
    }

    if (!application) {
      return {
        data: null,
        error: { message: 'Application not found or access denied' },
      };
    }

    // Only allow withdrawal if application is pending
    if (application.status !== 'pending') {
      return {
        data: null,
        error: {
          message: `Ne možete povući aplikaciju sa statusom "${application.status}"`,
        },
      };
    }

    // Delete the application
    const { data: deletedApplication, error: deleteError } = await supabase
      .from('campaign_applications')
      .delete()
      .eq('id', applicationId)
      .select();

    if (deleteError) {
      console.error('Error deleting application:', deleteError);
      return { data: null, error: deleteError };
    }

    // Optionally, send notification to business about withdrawal
    try {
      // Get business profile data for notification
      const { data: businessProfile } = await supabase
        .from('profiles')
        .select('full_name, username')
        .eq('id', application.campaigns.business_id)
        .single();

      const { data: influencerProfile } = await supabase
        .from('profiles')
        .select('full_name, username')
        .eq('id', user.id)
        .single();

      await supabase.rpc('create_notification', {
        p_user_id: application.campaigns.business_id,
        p_type: 'application_withdrawn',
        p_title: 'Aplikacija povučena',
        p_message: `${influencerProfile?.full_name || influencerProfile?.username || 'Influencer'} je povukao svoju aplikaciju za kampanju "${application.campaigns.title}"`,
        p_data: {
          campaign_id: application.campaigns.id,
          campaign_title: application.campaigns.title,
          influencer_name:
            influencerProfile?.full_name || influencerProfile?.username,
          withdrawn_application_id: applicationId,
        },
      });
    } catch (notificationError) {
      console.error(
        'Error sending withdrawal notification:',
        notificationError
      );
      // Don't fail the withdrawal if notification fails
    }

    return { data: deletedApplication?.[0] || { id: applicationId }, error: null };
  } catch (error: any) {
    console.error('Error withdrawing campaign application:', error);
    return { data: null, error: error.message };
  }
}

// OPTIMIZED FUNCTIONS FOR BUSINESS CAMPAIGNS DASHBOARD

interface CampaignDashboardCard {
  id: string;
  title: string;
  description: string;
  budget: number;
  status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';
  created_at: string;
  content_types: string[];
  application_count: number;
  platforms: any[];
}

interface CampaignDashboardStats {
  total_count: number;
  draft_count: number;
  active_count: number;
  paused_count: number;
  completed_count: number;
  cancelled_count: number;
  featured_count: number;
}

// Optimized function for loading business campaigns dashboard cards (fast!)
export async function getBusinessCampaignsDashboardOptimized(
  businessId: string,
  status?: string,
  limit: number = 50,
  offset: number = 0
): Promise<{ data: CampaignDashboardCard[] | null; error: any }> {
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      return { data: null, error: { message: 'User not authenticated' } };
    }

    // Call optimized RPC function for table view (no platforms/content_types)
    const { data, error } = await supabase.rpc(
      'get_business_campaigns_dashboard_table',
      {
        p_business_id: businessId,
        p_status: status || null,
        p_limit: limit,
        p_offset: offset,
      }
    );

    if (error) {
      console.error('Error fetching campaign dashboard cards:', error);
      return { data: null, error };
    }

    return { data: data || [], error: null };
  } catch (error: any) {
    console.error('Error fetching campaign dashboard cards:', error);
    return { data: null, error: error.message };
  }
}

// Get campaign dashboard stats for tabs
export async function getBusinessCampaignsDashboardStats(
  businessId: string
): Promise<{ data: CampaignDashboardStats | null; error: any }> {
  try {
    const { data, error } = await supabase.rpc(
      'count_business_campaigns_by_status',
      {
        p_business_id: businessId,
      }
    );

    if (error) {
      console.error('Error fetching campaign dashboard stats:', error);
      return { data: null, error };
    }

    // RPC returns array with single object
    const stats =
      data && data.length > 0
        ? data[0]
        : {
            total_count: 0,
            draft_count: 0,
            active_count: 0,
            paused_count: 0,
            completed_count: 0,
            cancelled_count: 0,
            featured_count: 0,
          };

    return { data: stats, error: null };
  } catch (error: any) {
    console.error('Error fetching campaign dashboard stats:', error);
    return { data: null, error: error.message };
  }
}

// Kreiranje Stripe checkout session za plaćanje aplikacije
export async function createApplicationPaymentSession(applicationId: string, proposedRate: number) {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) throw new Error('User not authenticated');

    const response = await fetch('/api/stripe/create-application-payment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`,
      },
      body: JSON.stringify({
        applicationId,
        proposedRate,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to create payment session');
    }

    return await response.json();
  } catch (error) {
    console.error('Error creating application payment session:', error);
    throw error;
  }
}

// Proverava da li je aplikacija plaćena
export async function isApplicationPaid(applicationId: string): Promise<boolean> {
  try {
    const { data: payment } = await supabase
      .from('payments')
      .select('id')
      .eq('campaign_application_id', applicationId)
      .eq('payment_status', 'completed')
      .single();
    
    return !!payment;
  } catch (error) {
    console.error('Error checking application payment status:', error);
    return false;
  }
}

// Dobija payment info iz aplikacije
export async function getApplicationPaymentInfo(applicationId: string): Promise<any | null> {
  try {
    console.log('Fetching payment info for application:', applicationId);
    console.log('ApplicationId type:', typeof applicationId);
    console.log('ApplicationId stringified:', JSON.stringify(applicationId));
    
    const { data: payment, error } = await supabase
      .from('payments')
      .select('*')
      .eq('campaign_application_id', applicationId)
      .eq('payment_status', 'completed')
      .maybeSingle();
    
    console.log('Payment query result:', { payment, error });
    
    if (error) {
      throw error;
    }
    
    return payment;
  } catch (error) {
    console.error('Error fetching application payment info:', error);
    return null;
  }
}
