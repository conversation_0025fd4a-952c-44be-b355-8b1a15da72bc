'use client';

import { ReactNode } from 'react';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, ArrowRight } from 'lucide-react';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
}

interface OnboardingLayoutProps {
  children: ReactNode;
  currentStep: number;
  totalSteps: number;
  steps: OnboardingStep[];
  onNext?: () => void;
  onPrevious?: () => void;
  onSkip?: () => void;
  nextLabel?: string;
  previousLabel?: string;
  skipLabel?: string;
  isNextDisabled?: boolean;
  isPreviousDisabled?: boolean;
  showSkip?: boolean;
  isLoading?: boolean;
}

export function OnboardingLayout({
  children,
  currentStep,
  totalSteps,
  steps,
  onNext,
  onPrevious,
  onSkip,
  nextLabel = 'Nastavi',
  previousLabel = 'Nazad',
  skipLabel = 'Preskoči',
  isNextDisabled = false,
  isPreviousDisabled = false,
  showSkip = false,
  isLoading = false,
}: OnboardingLayoutProps) {
  const progressPercentage = ((currentStep - 1) / (totalSteps - 1)) * 100;
  const currentStepData = steps[currentStep - 1];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-gray-900">
                Kreiranje profila
              </h1>
              <div className="text-sm text-gray-500">
                Korak {currentStep} od {totalSteps}
              </div>
            </div>
            {showSkip && onSkip && (
              <Button
                variant="ghost"
                onClick={onSkip}
                className="text-gray-500"
              >
                {skipLabel}
              </Button>
            )}
          </div>
        </div>
      </header>

      {/* Progress Bar */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-gray-600">
              <span>{currentStepData?.title}</span>
              <span>{Math.round(progressPercentage)}% završeno</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader className="text-center">
              <CardTitle className="text-2xl">
                {currentStepData?.title}
              </CardTitle>
              {currentStepData?.description && (
                <p className="text-muted-foreground mt-2">
                  {currentStepData.description}
                </p>
              )}
            </CardHeader>
            <CardContent className="space-y-6">{children}</CardContent>
          </Card>

          {/* Navigation */}
          <div className="flex justify-between items-center mt-8">
            <Button
              variant="outline"
              onClick={onPrevious}
              disabled={isPreviousDisabled || currentStep === 1}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>{previousLabel}</span>
            </Button>

            <div className="flex space-x-2">
              {/* Step indicators */}
              {steps.map((_, index) => (
                <div
                  key={index}
                  className={`w-3 h-3 rounded-full ${
                    index + 1 < currentStep
                      ? 'bg-green-500'
                      : index + 1 === currentStep
                        ? 'bg-blue-500'
                        : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>

            <Button
              onClick={onNext}
              disabled={isNextDisabled || isLoading}
              className="flex items-center space-x-2"
            >
              <span>{nextLabel}</span>
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </main>
    </div>
  );
}
