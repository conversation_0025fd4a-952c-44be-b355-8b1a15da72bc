# =================================
# PRODUCTION ENVIRONMENT CONFIGURATION
# =================================
# This file contains production-specific environment variables
# These should be set in your production environment (Vercel, Netlify, etc.)
# NEVER commit this file with real values to version control

# =================================
# ENVIRONMENT
# =================================
NODE_ENV=production
DEBUG=false
DISABLE_EMAILS=false

# =================================
# APPLICATION CONFIGURATION
# =================================
# Replace with your production domain
NEXT_PUBLIC_APP_URL=https://yourdomain.com

# =================================
# SECURITY SETTINGS (STRICT FOR PRODUCTION)
# =================================
# Strict CORS policy - only allow your production domain
CORS_ALLOWED_ORIGINS=https://yourdomain.com

# Secure session settings
SESSION_SECURE=true
SESSION_SAME_SITE=strict

# Force HTTPS
FORCE_HTTPS=true

# =================================
# LOGGING CONFIGURATION
# =================================
# Production log level (info, warn, error)
LOG_LEVEL=error

# Disable request logging in production for performance
LOG_REQUESTS=false

# =================================
# PERFORMANCE SETTINGS
# =================================
# Enable caching in production
DISABLE_CACHE=false

# Disable development tools
ENABLE_DEVTOOLS=false

# Enable gzip compression
ENABLE_COMPRESSION=true

# =================================
# MONITORING & ANALYTICS
# =================================
# Error tracking (highly recommended for production)
# SENTRY_DSN=https://your-sentry-dsn-here

# Analytics
# NEXT_PUBLIC_GA_TRACKING_ID=G-XXXXXXXXXX

# Session replay and user monitoring
# NEXT_PUBLIC_LOGROCKET_APP_ID=your-logrocket-app-id

# =================================
# API SETTINGS
# =================================
# Enable strict rate limiting in production
ENABLE_RATE_LIMITING=true
STRICT_RATE_LIMITING=true

# API timeout settings
API_TIMEOUT=30000

# =================================
# DATABASE SETTINGS
# =================================
# Disable database query logging in production
LOG_DATABASE_QUERIES=false

# Connection pool settings for production
# DB_POOL_MIN=10
# DB_POOL_MAX=100

# =================================
# EMAIL SETTINGS
# =================================
# Production email settings
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME="Influexus Platform"

# =================================
# REDIS SETTINGS (FOR PRODUCTION SCALING)
# =================================
# Redis for caching and rate limiting
# REDIS_URL=redis://your-redis-instance
# REDIS_PASSWORD=your-redis-password
# REDIS_MAX_CONNECTIONS=100

# =================================
# CDN & STORAGE
# =================================
# If using external CDN
# CDN_BASE_URL=https://cdn.yourdomain.com

# File upload limits
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,webp

# =================================
# BACKUP & MAINTENANCE
# =================================
# Maintenance mode
# MAINTENANCE_MODE=false
# MAINTENANCE_MESSAGE="We're performing scheduled maintenance. Please check back soon."

# Backup settings
# ENABLE_AUTOMATED_BACKUPS=true
# BACKUP_RETENTION_DAYS=30

# =================================
# COMPLIANCE & GDPR
# =================================
# GDPR compliance settings
ENABLE_GDPR_COMPLIANCE=true
COOKIE_CONSENT_REQUIRED=true

# Data retention settings
USER_DATA_RETENTION_DAYS=2555  # 7 years
SESSION_DATA_RETENTION_DAYS=30

# =================================
# FEATURE FLAGS
# =================================
# Production feature flags
# ENABLE_NEW_DASHBOARD=true
# ENABLE_ADVANCED_ANALYTICS=true
# ENABLE_PREMIUM_FEATURES=true