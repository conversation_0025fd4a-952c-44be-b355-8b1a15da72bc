'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import PlatformIconSimple from '@/components/ui/platform-icon-simple';
import {
  Plus,
  X,
  Instagram,
  Youtube,
  ArrowLeft,
  ArrowRight,
} from 'lucide-react';
import { cn } from '@/lib/utils';

// TikTok icon component since it's not in Lucide
const TikTokIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z" />
  </svg>
);

interface SocialPlatform {
  id: 'instagram' | 'tiktok' | 'youtube';
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  handleLabel: string;
  followersLabel: string;
  placeholder: string;
}

const SOCIAL_PLATFORMS: SocialPlatform[] = [
  {
    id: 'instagram',
    name: 'Instagram',
    icon: Instagram,
    color: 'bg-gradient-to-r from-purple-500 to-pink-500',
    handleLabel: 'Instagram handle',
    followersLabel: 'Broj pratilaca',
    placeholder: 'coca_cola',
  },
  {
    id: 'tiktok',
    name: 'TikTok',
    icon: TikTokIcon,
    color: 'bg-black',
    handleLabel: 'TikTok handle',
    followersLabel: 'Broj pratilaca',
    placeholder: 'cocacola',
  },
  {
    id: 'youtube',
    name: 'YouTube',
    icon: Youtube,
    color: 'bg-red-600',
    handleLabel: 'YouTube handle',
    followersLabel: 'Broj pretplatnika',
    placeholder: 'CocaCola',
  },
];

interface SocialMediaData {
  instagram_handle?: string;
  instagram_followers?: number;
  tiktok_handle?: string;
  tiktok_followers?: number;
  youtube_handle?: string;
  youtube_subscribers?: number;
}

const socialMediaSchema = z.object({
  instagram_handle: z.string().optional(),
  instagram_followers: z.number().min(0).optional(),
  tiktok_handle: z.string().optional(),
  tiktok_followers: z.number().min(0).optional(),
  youtube_handle: z.string().optional(),
  youtube_subscribers: z.number().min(0).optional(),
});

type SocialMediaForm = z.infer<typeof socialMediaSchema>;

interface BusinessSocialMediaStepProps {
  value: SocialMediaData;
  onNext: (socialMedia: SocialMediaData) => void;
  onBack: () => void;
}

export function BusinessSocialMediaStep({
  value,
  onNext,
  onBack,
}: BusinessSocialMediaStepProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [activePlatforms, setActivePlatforms] = useState<string[]>(() => {
    const active = [];
    if (value.instagram_handle) active.push('instagram');
    if (value.tiktok_handle) active.push('tiktok');
    if (value.youtube_handle) active.push('youtube');
    return active;
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<SocialMediaForm>({
    resolver: zodResolver(socialMediaSchema),
    defaultValues: value,
  });

  const addPlatform = (platformId: string) => {
    if (!activePlatforms.includes(platformId)) {
      setActivePlatforms([...activePlatforms, platformId]);
    }
  };

  const removePlatform = (platformId: string) => {
    setActivePlatforms(activePlatforms.filter(id => id !== platformId));
    // Clear form values for removed platform
    setValue(`${platformId}_handle` as keyof SocialMediaForm, '');
    setValue(`${platformId}_followers` as keyof SocialMediaForm, 0);
  };

  const onSubmit = async (data: SocialMediaForm) => {
    setIsLoading(true);
    try {
      // Filter out empty values
      const cleanedData: SocialMediaData = {};
      if (data.instagram_handle) {
        cleanedData.instagram_handle = data.instagram_handle;
        cleanedData.instagram_followers = data.instagram_followers || 0;
      }
      if (data.tiktok_handle) {
        cleanedData.tiktok_handle = data.tiktok_handle;
        cleanedData.tiktok_followers = data.tiktok_followers || 0;
      }
      if (data.youtube_handle) {
        cleanedData.youtube_handle = data.youtube_handle;
        cleanedData.youtube_subscribers = data.youtube_subscribers || 0;
      }

      onNext(cleanedData);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-2">Društvene mreže</h2>
        <p className="text-white/70">
          Dodajte profile vašeg brenda na društvenim mrežama (opcionalno)
        </p>
      </div>
      <div>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Available platforms to add */}
          {SOCIAL_PLATFORMS.filter(
            platform => !activePlatforms.includes(platform.id)
          ).length > 0 && (
            <div className="space-y-3">
              <Label className="text-white">Dodajte platforme:</Label>
              <div className="flex flex-wrap gap-2">
                {SOCIAL_PLATFORMS.filter(
                  platform => !activePlatforms.includes(platform.id)
                ).map(platform => (
                  <Button
                    key={platform.id}
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => addPlatform(platform.id)}
                    className="flex items-center space-x-2 border-white/30 text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm"
                  >
                    <PlatformIconSimple platform={platform.name} size="sm" />
                    <span>{platform.name}</span>
                    <Plus className="h-3 w-3" />
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* Active platforms */}
          {activePlatforms.map(platformId => {
            const platform = SOCIAL_PLATFORMS.find(p => p.id === platformId);
            if (!platform) return null;

            return (
              <div
                key={platform.id}
                className="space-y-3 p-4 border border-white/30 rounded-lg bg-white/10 backdrop-blur-sm"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <PlatformIconSimple platform={platform.name} size="md" />
                    <span className="font-medium text-white">
                      {platform.name}
                    </span>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removePlatform(platform.id)}
                    className="text-white/70 hover:text-white hover:bg-white/20"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div className="space-y-2">
                    <Label
                      htmlFor={`${platform.id}_handle`}
                      className="text-white"
                    >
                      Handle
                    </Label>
                    <Input
                      id={`${platform.id}_handle`}
                      {...register(
                        `${platform.id}_handle` as keyof SocialMediaForm
                      )}
                      placeholder={platform.placeholder}
                      className="bg-white/10 border-white/30 text-white placeholder:text-white/50 focus:border-white/50 focus:ring-white/20"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label
                      htmlFor={`${platform.id}_followers`}
                      className="text-white"
                    >
                      {platform.followersLabel}
                    </Label>
                    <Input
                      id={`${platform.id}_followers`}
                      type="number"
                      {...register(
                        `${platform.id}_followers` as keyof SocialMediaForm,
                        {
                          valueAsNumber: true,
                        }
                      )}
                      placeholder="0"
                      min="0"
                      className="bg-white/10 border-white/30 text-white placeholder:text-white/50 focus:border-white/50 focus:ring-white/20"
                    />
                  </div>
                </div>
              </div>
            );
          })}

          {activePlatforms.length === 0 && (
            <div className="text-center py-8 text-white/60">
              <p>Dodajte društvene mreže vašeg brenda</p>
              <p className="text-sm">Možete preskočiti ovaj korak</p>
            </div>
          )}

          <div className="flex justify-between pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onBack}
              className="flex items-center space-x-2 border-white/30 text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Nazad</span>
            </Button>

            <Button
              type="submit"
              disabled={isLoading}
              className="flex items-center space-x-2 bg-white text-instagram-purple hover:bg-white/90 font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <span>Dalje</span>
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
