'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import PlatformIconSimple from '@/components/ui/platform-icon-simple';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { X, Filter } from 'lucide-react';
import { CampaignFilters } from '@/lib/campaigns';
import { getCategories } from '@/lib/categories';
import { getPlatforms } from '@/lib/platforms';
import { formatDate } from '@/lib/date-utils';

interface Platform {
  id: number;
  name: string;
  slug: string;
  icon: string;
}

interface Category {
  id: number;
  name: string;
  slug: string;
  icon: string;
}

interface CampaignFiltersComponentProps {
  filters: CampaignFilters;
  onFiltersChange: (filters: CampaignFilters) => void;
}

export function CampaignFiltersComponent({
  filters,
  onFiltersChange,
}: CampaignFiltersComponentProps) {
  const [platforms, setPlatforms] = useState<Platform[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [localFilters, setLocalFilters] = useState<CampaignFilters>(filters);
  const [budgetRange, setBudgetRange] = useState<[number, number]>([
    filters.minBudget || 0,
    filters.maxBudget || 10000,
  ]);

  // Load platforms and categories
  useEffect(() => {
    const loadData = async () => {
      try {
        const [platformsResult, categoriesResult] = await Promise.all([
          getPlatforms(),
          getCategories(),
        ]);

        if (platformsResult.data) setPlatforms(platformsResult.data);
        if (categoriesResult.data) setCategories(categoriesResult.data);
      } catch (error) {
        console.error('Error loading filter data:', error);
      }
    };

    loadData();
  }, []);

  // Update local filters when props change
  useEffect(() => {
    setLocalFilters(filters);
    setBudgetRange([filters.minBudget || 0, filters.maxBudget || 10000]);
  }, [filters]);

  const handleFilterChange = (key: keyof CampaignFilters, value: any) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handlePlatformToggle = (platformId: number) => {
    const currentPlatforms = localFilters.platforms || [];
    const newPlatforms = currentPlatforms.includes(platformId)
      ? currentPlatforms.filter(id => id !== platformId)
      : [...currentPlatforms, platformId];

    handleFilterChange(
      'platforms',
      newPlatforms.length > 0 ? newPlatforms : undefined
    );
  };

  const handleCategoryToggle = (categoryId: number) => {
    const currentCategories = localFilters.categories || [];
    const newCategories = currentCategories.includes(categoryId)
      ? currentCategories.filter(id => id !== categoryId)
      : [...currentCategories, categoryId];

    handleFilterChange(
      'categories',
      newCategories.length > 0 ? newCategories : undefined
    );
  };

  const handleBudgetChange = (values: number[]) => {
    setBudgetRange([values[0], values[1]]);
    handleFilterChange('minBudget', values[0] > 0 ? values[0] : undefined);
    handleFilterChange('maxBudget', values[1] < 10000 ? values[1] : undefined);
  };

  const clearAllFilters = () => {
    const emptyFilters: CampaignFilters = {};
    setLocalFilters(emptyFilters);
    setBudgetRange([0, 10000]);
    onFiltersChange(emptyFilters);
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (localFilters.platforms?.length) count++;
    if (localFilters.categories?.length) count++;
    if (localFilters.minBudget || localFilters.maxBudget) count++;
    if (localFilters.location) count++;
    if (localFilters.minFollowers || localFilters.maxFollowers) count++;
    if (localFilters.gender && localFilters.gender !== 'all') count++;
    if (localFilters.deadlineBefore) count++;
    return count;
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filteri
            </CardTitle>
            <CardDescription>
              Filtrirajte kampanje prema vašim preferencijama
            </CardDescription>
          </div>
          {getActiveFiltersCount() > 0 && (
            <Button variant="outline" size="sm" onClick={clearAllFilters}>
              <X className="h-4 w-4 mr-1" />
              Očisti ({getActiveFiltersCount()})
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Platforms */}
        <div>
          <Label className="text-sm font-medium mb-3 block">Platforme</Label>
          <div className="space-y-2">
            {platforms.map(platform => (
              <div key={platform.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`platform-${platform.id}`}
                  checked={
                    localFilters.platforms?.includes(platform.id) || false
                  }
                  onCheckedChange={() => handlePlatformToggle(platform.id)}
                />
                <Label
                  htmlFor={`platform-${platform.id}`}
                  className="text-sm flex items-center gap-2 cursor-pointer"
                >
                  <PlatformIconSimple
                    platform={platform.name}
                    size="sm"
                  />
                  {platform.name}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Categories */}
        <div>
          <Label className="text-sm font-medium mb-3 block">Kategorije</Label>
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {categories.map(category => (
              <div key={category.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`category-${category.id}`}
                  checked={
                    localFilters.categories?.includes(category.id) || false
                  }
                  onCheckedChange={() => handleCategoryToggle(category.id)}
                />
                <Label
                  htmlFor={`category-${category.id}`}
                  className="text-sm flex items-center gap-2 cursor-pointer"
                >
                  <span>{category.icon}</span>
                  {category.name}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Budget Range */}
        <div>
          <Label className="text-sm font-medium mb-3 block">
            Budžet: {budgetRange[0].toLocaleString()} -{' '}
            {budgetRange[1].toLocaleString()} KM
          </Label>
          <Slider
            value={budgetRange}
            onValueChange={handleBudgetChange}
            max={10000}
            min={0}
            step={100}
            className="w-full"
          />
          <div className="flex justify-between text-xs text-muted-foreground mt-1">
            <span>0 KM</span>
            <span>10,000+ KM</span>
          </div>
        </div>

        {/* Location */}
        <div>
          <Label htmlFor="location" className="text-sm font-medium">
            Lokacija
          </Label>
          <Input
            id="location"
            placeholder="npr. Sarajevo, BiH"
            value={localFilters.location || ''}
            onChange={e =>
              handleFilterChange('location', e.target.value || undefined)
            }
            className="mt-1"
          />
        </div>

        {/* Follower Range */}
        <div>
          <Label className="text-sm font-medium mb-3 block">
            Broj pratilaca
          </Label>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label
                htmlFor="minFollowers"
                className="text-xs text-muted-foreground"
              >
                Min.
              </Label>
              <Input
                id="minFollowers"
                type="number"
                placeholder="1000"
                value={localFilters.minFollowers || ''}
                onChange={e =>
                  handleFilterChange(
                    'minFollowers',
                    e.target.value ? parseInt(e.target.value) : undefined
                  )
                }
              />
            </div>
            <div>
              <Label
                htmlFor="maxFollowers"
                className="text-xs text-muted-foreground"
              >
                Max.
              </Label>
              <Input
                id="maxFollowers"
                type="number"
                placeholder="100000"
                value={localFilters.maxFollowers || ''}
                onChange={e =>
                  handleFilterChange(
                    'maxFollowers',
                    e.target.value ? parseInt(e.target.value) : undefined
                  )
                }
              />
            </div>
          </div>
        </div>

        {/* Gender */}
        <div>
          <Label htmlFor="gender" className="text-sm font-medium">
            Pol ciljne grupe
          </Label>
          <Select
            value={localFilters.gender || 'all'}
            onValueChange={value =>
              handleFilterChange('gender', value === 'all' ? undefined : value)
            }
          >
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Svi" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Svi</SelectItem>
              <SelectItem value="male">Muški</SelectItem>
              <SelectItem value="female">Ženski</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Application Deadline */}
        <div>
          <Label htmlFor="deadline" className="text-sm font-medium">
            Rok za prijave do
          </Label>
          <Input
            id="deadline"
            type="date"
            value={localFilters.deadlineBefore || ''}
            onChange={e =>
              handleFilterChange('deadlineBefore', e.target.value || undefined)
            }
            className="mt-1"
          />
        </div>

        {/* Sort Options */}
        <div>
          <Label htmlFor="sortBy" className="text-sm font-medium">
            Sortiraj po
          </Label>
          <Select
            value={localFilters.sortBy || 'created_at'}
            onValueChange={value => handleFilterChange('sortBy', value as any)}
          >
            <SelectTrigger className="mt-1">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="created_at">Datum kreiranja</SelectItem>
              <SelectItem value="budget">Budžet</SelectItem>
              <SelectItem value="application_deadline">
                Rok za prijave
              </SelectItem>
              <SelectItem value="applications_count">
                Broj aplikacija
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Active Filters Summary */}
        {getActiveFiltersCount() > 0 && (
          <div>
            <Label className="text-sm font-medium mb-2 block">
              Aktivni filteri
            </Label>
            <div className="flex flex-wrap gap-1">
              {localFilters.platforms?.map(platformId => {
                const platform = platforms.find(p => p.id === platformId);
                return platform ? (
                  <Badge
                    key={platformId}
                    variant="secondary"
                    className="text-xs"
                  >
                    <PlatformIconSimple
                      platform={platform.name}
                      size="sm"
                    /> {platform.name}
                    <X
                      className="h-3 w-3 ml-1 cursor-pointer"
                      onClick={() => handlePlatformToggle(platformId)}
                    />
                  </Badge>
                ) : null;
              })}

              {localFilters.categories?.map(categoryId => {
                const category = categories.find(c => c.id === categoryId);
                return category ? (
                  <Badge
                    key={categoryId}
                    variant="secondary"
                    className="text-xs"
                  >
                    {category.icon} {category.name}
                    <X
                      className="h-3 w-3 ml-1 cursor-pointer"
                      onClick={() => handleCategoryToggle(categoryId)}
                    />
                  </Badge>
                ) : null;
              })}

              {(localFilters.minBudget || localFilters.maxBudget) && (
                <Badge variant="secondary" className="text-xs">
                  Budžet: {localFilters.minBudget || 0} -{' '}
                  {localFilters.maxBudget || '∞'} KM
                  <X
                    className="h-3 w-3 ml-1 cursor-pointer"
                    onClick={() => {
                      handleFilterChange('minBudget', undefined);
                      handleFilterChange('maxBudget', undefined);
                      setBudgetRange([0, 10000]);
                    }}
                  />
                </Badge>
              )}

              {localFilters.location && (
                <Badge variant="secondary" className="text-xs">
                  📍 {localFilters.location}
                  <X
                    className="h-3 w-3 ml-1 cursor-pointer"
                    onClick={() => handleFilterChange('location', undefined)}
                  />
                </Badge>
              )}

              {(localFilters.minFollowers || localFilters.maxFollowers) && (
                <Badge variant="secondary" className="text-xs">
                  👥 {localFilters.minFollowers || 0} -{' '}
                  {localFilters.maxFollowers || '∞'}
                  <X
                    className="h-3 w-3 ml-1 cursor-pointer"
                    onClick={() => {
                      handleFilterChange('minFollowers', undefined);
                      handleFilterChange('maxFollowers', undefined);
                    }}
                  />
                </Badge>
              )}

              {localFilters.gender && localFilters.gender !== 'all' && (
                <Badge variant="secondary" className="text-xs">
                  {localFilters.gender === 'male' ? '♂️ Muški' : '♀️ Ženski'}
                  <X
                    className="h-3 w-3 ml-1 cursor-pointer"
                    onClick={() => handleFilterChange('gender', undefined)}
                  />
                </Badge>
              )}

              {localFilters.deadlineBefore && (
                <Badge variant="secondary" className="text-xs">
                  📅 Do {formatDate(localFilters.deadlineBefore)}
                  <X
                    className="h-3 w-3 ml-1 cursor-pointer"
                    onClick={() =>
                      handleFilterChange('deadlineBefore', undefined)
                    }
                  />
                </Badge>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
