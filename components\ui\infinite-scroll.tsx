'use client';

import { useEffect, useRef, useCallback, useState } from 'react';
import { Loader2 } from 'lucide-react';

interface InfiniteScrollProps<T> {
  data: T[];
  hasMore: boolean;
  isLoading: boolean;
  isLoadingMore: boolean;
  loadMore: () => void;
  threshold?: number;
  children: React.ReactNode;
  className?: string;
  cacheKey?: string; // Za cache-iranje scroll pozicije
  onScrollPosition?: (position: number) => void;
}

export function InfiniteScroll<T>({
  data,
  hasMore,
  isLoading,
  isLoadingMore,
  loadMore,
  threshold = 200,
  children,
  className = '',
  cacheKey,
  onScrollPosition,
}: InfiniteScrollProps<T>) {
  const observerTarget = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [hasRestored, setHasRestored] = useState(false);

  // Intersection Observer za auto-loading
  const handleObserver = useCallback(
    (entries: IntersectionObserverEntry[]) => {
      const [target] = entries;
      if (target.isIntersecting && hasMore && !isLoading && !isLoadingMore) {
        loadMore();
      }
    },
    [hasMore, isLoading, isLoadingMore, loadMore]
  );

  // Setup Intersection Observer
  useEffect(() => {
    const element = observerTarget.current;
    if (!element) return;

    const observer = new IntersectionObserver(handleObserver, {
      threshold: 0,
      rootMargin: `${threshold}px`,
    });

    observer.observe(element);
    return () => observer.disconnect();
  }, [handleObserver, threshold]);

  // Cache scroll pozicije
  const saveScrollPosition = useCallback(() => {
    if (!cacheKey || !containerRef.current) return;

    const scrollTop = containerRef.current.scrollTop;
    sessionStorage.setItem(`scroll-${cacheKey}`, scrollTop.toString());
    onScrollPosition?.(scrollTop);
  }, [cacheKey, onScrollPosition]);

  // Restore scroll pozicije
  const restoreScrollPosition = useCallback(() => {
    if (!cacheKey || !containerRef.current || hasRestored) return;

    const savedPosition = sessionStorage.getItem(`scroll-${cacheKey}`);
    if (savedPosition) {
      // Čekamo da se podaci učitaju pre restore
      setTimeout(() => {
        if (containerRef.current) {
          containerRef.current.scrollTop = parseInt(savedPosition);
          setHasRestored(true);
        }
      }, 100);
    } else {
      setHasRestored(true);
    }
  }, [cacheKey, hasRestored]);

  // Restore scroll kada se data učitaju
  useEffect(() => {
    if (data.length > 0 && !isLoading) {
      restoreScrollPosition();
    }
  }, [data.length, isLoading, restoreScrollPosition]);

  // Save poziciju na scroll
  useEffect(() => {
    const container = containerRef.current;
    if (!container || !cacheKey) return;

    const handleScroll = () => {
      saveScrollPosition();
    };

    // Throttle scroll events
    let scrollTimeout: NodeJS.Timeout;
    const throttledScroll = () => {
      if (scrollTimeout) clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(handleScroll, 100);
    };

    container.addEventListener('scroll', throttledScroll);
    return () => {
      container.removeEventListener('scroll', throttledScroll);
      if (scrollTimeout) clearTimeout(scrollTimeout);
    };
  }, [cacheKey, saveScrollPosition]);

  return (
    <div
      ref={containerRef}
      className={`overflow-auto ${className}`}
      style={{ height: '100%' }}
    >
      {children}

      {/* Loading trigger element */}
      <div ref={observerTarget} className="h-4" />

      {/* Loading states */}
      {isLoadingMore && hasMore && (
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-6 w-6 animate-spin text-primary" />
          <span className="ml-2 text-sm text-muted-foreground">
            Učitava još sadržaja...
          </span>
        </div>
      )}

      {/* End message */}
      {!hasMore && data.length > 0 && (
        <div className="text-center py-8">
          <p className="text-sm text-muted-foreground">
            To je sve! Prikazano je {data.length} rezultata.
          </p>
        </div>
      )}

      {/* Initial loading skeleton */}
      {isLoading && data.length === 0 && (
        <div className="space-y-4">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="bg-gray-200 rounded-lg h-32"></div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

// Cache utility funkcije
export const InfiniteScrollCache = {
  // Čuva filtere u sessionStorage
  saveFilters: (cacheKey: string, filters: any) => {
    sessionStorage.setItem(`filters-${cacheKey}`, JSON.stringify(filters));
  },

  // Učitava filtere iz sessionStorage
  loadFilters: (cacheKey: string) => {
    const saved = sessionStorage.getItem(`filters-${cacheKey}`);
    return saved ? JSON.parse(saved) : null;
  },

  // Čuva podatke u sessionStorage (za back navigaciju)
  saveData: (cacheKey: string, data: any[]) => {
    // Limitiramo na poslednih 100 items da ne zatrpamo memoriju
    const limitedData = data.slice(-100);
    sessionStorage.setItem(`data-${cacheKey}`, JSON.stringify(limitedData));
  },

  // Učitava podatke iz sessionStorage
  loadData: (cacheKey: string) => {
    const saved = sessionStorage.getItem(`data-${cacheKey}`);
    return saved ? JSON.parse(saved) : [];
  },

  // Čisti cache (npr. kada se promene filteri)
  clearCache: (cacheKey: string) => {
    sessionStorage.removeItem(`scroll-${cacheKey}`);
    sessionStorage.removeItem(`filters-${cacheKey}`);
    sessionStorage.removeItem(`data-${cacheKey}`);
  },
};
