'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Check } from 'lucide-react';
import { supabase } from '@/lib/supabase';

interface Category {
  id: number;
  name: string;
  icon: string | null;
}

interface CategoriesStepProps {
  value: number[];
  onChange: (value: number[]) => void;
  onNext: () => void;
  onBack: () => void;
  title?: string;
  description?: string;
  maxCategories?: number;
}

export function CategoriesStep({
  value,
  onChange,
  onNext,
  onBack,
  title = 'Odaberite kategorije',
  description = 'Koje kategorije najbolje opisuju sadržaj koji kreirate?',
  maxCategories = 3,
}: CategoriesStepProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('id, name, icon')
        .order('name');

      if (error) throw error;
      setCategories(data || []);
    } catch (error) {
      console.error('Error loading categories:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleCategory = (categoryId: number) => {
    if (value.includes(categoryId)) {
      // Remove category
      onChange(value.filter(id => id !== categoryId));
    } else if (value.length < maxCategories) {
      // Add category (max based on prop)
      onChange([...value, categoryId]);
    }
  };

  const handleNext = () => {
    if (value.length > 0) {
      onNext();
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-2">
            Učitavanje kategorija...
          </h2>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-2">{title}</h2>
        <p className="text-white/70">{description}</p>
      </div>

      <div className="space-y-4">
        <div>
          <Label className="text-white">
            Kategorije ({value.length}/{maxCategories})
          </Label>
          <div className="grid grid-cols-2 gap-3 mt-2">
            {categories.map(category => {
              const isSelected = value.includes(category.id);
              const isDisabled = !isSelected && value.length >= 3;

              return (
                <button
                  key={category.id}
                  onClick={() => toggleCategory(category.id)}
                  disabled={isDisabled}
                  className={`
                    p-3 rounded-lg border-2 text-left transition-all backdrop-blur-sm relative
                    ${
                      isSelected
                        ? 'border-white bg-white/30 text-white shadow-xl scale-[1.02] ring-2 ring-white/50'
                        : isDisabled
                          ? 'border-white/20 bg-white/5 text-white/40 cursor-not-allowed'
                          : 'border-white/30 bg-white/10 text-white hover:border-white/50 hover:bg-white/20'
                    }
                  `}
                >
                  {/* Check mark for selected items */}
                  {isSelected && (
                    <div className="absolute top-2 right-2 w-5 h-5 bg-white rounded-full flex items-center justify-center shadow-sm">
                      <Check className="w-3 h-3 text-gray-800" />
                    </div>
                  )}

                  <div className="flex items-center gap-2">
                    {category.icon && (
                      <span className="text-lg">{category.icon}</span>
                    )}
                    <span className="font-medium">{category.name}</span>
                  </div>
                </button>
              );
            })}
          </div>
        </div>

        {value.length > 0 && (
          <div>
            <Label className="text-white">Izabrane kategorije:</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {value.map(categoryId => {
                const category = categories.find(c => c.id === categoryId);
                return category ? (
                  <div
                    key={categoryId}
                    className="inline-flex items-center px-3 py-1 rounded-full bg-white/20 border border-white/30 text-white text-sm backdrop-blur-sm"
                  >
                    {category.icon && (
                      <span className="mr-1">{category.icon}</span>
                    )}
                    {category.name}
                  </div>
                ) : null;
              })}
            </div>
          </div>
        )}

        <div className="text-sm text-white/60">
          <p>Možete izabrati najmanje 1, a najviše 3 kategorije.</p>
        </div>
      </div>

      <div className="flex gap-3">
        <Button
          variant="outline"
          onClick={onBack}
          className="flex-1 border-white/30 text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm"
        >
          Nazad
        </Button>
        <Button
          onClick={handleNext}
          disabled={value.length === 0}
          className="flex-1 bg-white text-instagram-purple hover:bg-white/90 font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
        >
          Dalje
        </Button>
      </div>
    </div>
  );
}
