import { sendEmail, generateEmailVerificationTemplate, generatePasswordResetTemplate, generateWelcomeTemplate, generateNotificationTemplate } from './email-service';

export async function sendVerificationEmail(email: string, verificationUrl: string) {
  try {
    const { htmlContent, textContent } = generateEmailVerificationTemplate(verificationUrl, email);
    
    const result = await sendEmail({
      to: email,
      subject: 'Potvrdite svoj email - INFLUEXUS',
      htmlContent,
      textContent
    });

    return result;
  } catch (error) {
    console.error('Failed to send verification email:', error);
    return { success: false, error };
  }
}

export async function sendPasswordResetEmail(email: string, resetUrl: string) {
  try {
    const { htmlContent, textContent } = generatePasswordResetTemplate(resetUrl, email);
    
    const result = await sendEmail({
      to: email,
      subject: 'Resetovanje lozinke - INFLUEXUS',
      htmlContent,
      textContent
    });

    return result;
  } catch (error) {
    console.error('Failed to send password reset email:', error);
    return { success: false, error };
  }
}

export async function sendWelcomeEmail(
  email: string, 
  userName: string, 
  userType: 'influencer' | 'business'
) {
  try {
    const { htmlContent, textContent } = generateWelcomeTemplate(userName, email, userType);
    
    const result = await sendEmail({
      to: email,
      subject: `Dobrodošli u INFLUEXUS ${userType === 'influencer' ? 'zajednicu influensera' : 'zajednicu brendova'}!`,
      htmlContent,
      textContent
    });

    return result;
  } catch (error) {
    console.error('Failed to send welcome email:', error);
    return { success: false, error };
  }
}

export async function sendNotificationEmail(
  email: string,
  userName: string,
  notificationType: 'new_offer' | 'new_order' | 'application_approved' | 'payment_required_offer' | 'payment_required_order' | 'work_submitted' | 'payment_completed' | 'work_approved',
  details: any
) {
  try {
    const { htmlContent, textContent } = generateNotificationTemplate(
      userName, 
      email, 
      notificationType,
      details
    );
    
    const subjectMap = {
      new_offer: 'Nova direktna ponuda - INFLUEXUS',
      new_order: 'Nova narudžba paketa - INFLUEXUS',
      application_approved: 'Aplikacija prihvaćena - INFLUEXUS', 
      payment_required_offer: 'Izvršite plaćanje - Ponuda prihvaćena - INFLUEXUS',
      payment_required_order: 'Izvršite plaćanje - Narudžba prihvaćena - INFLUEXUS',
      work_submitted: 'Rad završen - Potreban pregled - INFLUEXUS',
      payment_completed: 'Plaćanje izvršeno - Možete početi - INFLUEXUS',
      work_approved: 'Rad odobren - Čestitamo! - INFLUEXUS'
    };
    
    const result = await sendEmail({
      to: email,
      subject: subjectMap[notificationType],
      htmlContent,
      textContent
    });

    return result;
  } catch (error) {
    console.error('Failed to send notification email:', error);
    return { success: false, error };
  }
}