'use client';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface GenderStepProps {
  value: string;
  onChange: (value: string) => void;
  onNext: () => void;
  onBack: () => void;
}

export function GenderStep({
  value,
  onChange,
  onNext,
  onBack,
}: GenderStepProps) {
  const genderOptions = [
    { value: 'male', label: '<PERSON><PERSON><PERSON>' },
    { value: 'female', label: '<PERSON><PERSON><PERSON>' },
    { value: 'other', label: '<PERSON>stalo' },
    { value: 'prefer_not_to_say', label: '<PERSON>e želi<PERSON> da ka<PERSON>' },
  ];

  const handleNext = () => {
    if (value) {
      onNext();
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-2">Pol</h2>
        <p className="text-white/70">Izaberite svoj pol</p>
      </div>

      <div className="space-y-3">
        {genderOptions.map(option => (
          <button
            key={option.value}
            onClick={() => onChange(option.value)}
            className={`w-full p-4 text-left border rounded-lg transition-colors ${
              value === option.value
                ? 'border-white/50 bg-white/20 text-white backdrop-blur-sm'
                : 'border-white/30 bg-white/10 text-white/80 hover:border-white/50 hover:bg-white/20 backdrop-blur-sm'
            }`}
          >
            <div className="flex items-center justify-between">
              <span className="font-medium">{option.label}</span>
              {value === option.value && (
                <div className="w-4 h-4 rounded-full bg-white flex items-center justify-center">
                  <div className="w-2 h-2 rounded-full bg-instagram-purple" />
                </div>
              )}
            </div>
          </button>
        ))}
      </div>

      <div className="flex gap-3 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onBack}
          className="flex-1 border-white/30 text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm"
        >
          Nazad
        </Button>
        <Button
          type="button"
          onClick={handleNext}
          disabled={!value}
          className="flex-1 bg-white text-instagram-purple hover:bg-white/90 font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
        >
          Dalje
        </Button>
      </div>
    </div>
  );
}
