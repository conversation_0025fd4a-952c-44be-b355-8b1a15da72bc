import { CampaignCard } from "@/components/campaign-card"

export default function Home() {
  const sampleCampaign = {
    name: "Summer Fashion Collection",
    description:
      "Showcase the latest summer trends with vibrant colors and lightweight fabrics perfect for the season.",
    requirements: [
      {
        platform: "Instagram" as const,
        contentTypes: ["Story", "Photo Post"],
      },
      {
        platform: "YouTube" as const,
        contentTypes: ["Short"],
      },
      {
        platform: "TikTok" as const,
        contentTypes: ["Video"],
      },
    ],
    creationDate: "2024-03-15",
    price: 2500,
    applications: 12,
  }

  const premiumCampaign = {
    name: "Luxury Watch Brand Partnership",
    description:
      "Exclusive collaboration with premium timepiece brand featuring high-end lifestyle content and sophisticated storytelling.",
    requirements: [
      {
        platform: "Instagram" as const,
        contentTypes: ["Story", "Photo Post", "Reel"],
      },
      {
        platform: "YouTube" as const,
        contentTypes: ["Short", "Video Review"],
      },
    ],
    creationDate: "2024-03-20",
    price: 8500,
    applications: 45,
    isPremium: true,
  }

  return (
    <main className="min-h-screen bg-background p-8">
      <div className="max-w-md mx-auto space-y-6">
        <CampaignCard campaign={sampleCampaign} />
        <CampaignCard campaign={premiumCampaign} />
      </div>
    </main>
  )
}
