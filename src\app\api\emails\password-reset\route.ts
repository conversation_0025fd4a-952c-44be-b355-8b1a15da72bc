import { NextRequest, NextResponse } from 'next/server';
import { sendEmail, generatePasswordResetTemplate } from '@/lib/email-service';

export async function POST(request: NextRequest) {
  try {
    const { email, resetUrl } = await request.json();
    
    if (!email || !resetUrl) {
      return NextResponse.json(
        { error: 'Email and reset URL are required' },
        { status: 400 }
      );
    }

    const { htmlContent, textContent } = generatePasswordResetTemplate(resetUrl, email);
    
    const result = await sendEmail({
      to: email,
      subject: 'Resetovanje lozinke - INFLUEXUS',
      htmlContent,
      textContent
    });

    if (result.success) {
      return NextResponse.json(
        { message: 'Password reset email sent successfully' },
        { status: 200 }
      );
    } else {
      return NextResponse.json(
        { error: 'Failed to send password reset email' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Password reset email error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}