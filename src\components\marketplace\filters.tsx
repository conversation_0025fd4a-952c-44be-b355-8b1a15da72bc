'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import PlatformIconSimple from '@/components/ui/platform-icon-simple';
import { Slider } from '@/components/ui/slider';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { ChevronDown, ChevronUp, X, Filter } from 'lucide-react';
import {
  getCategories,
  getPlatforms,
  getContentTypes,
  type SearchFilters,
} from '@/lib/marketplace';

interface FiltersProps {
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  onApplyFilters: () => void;
}

interface Category {
  id: number;
  name: string;
  slug: string;
  icon: string;
}

interface Platform {
  id: number;
  name: string;
  slug: string;
  icon: string;
}

interface ContentType {
  id: number;
  platform_id: number;
  name: string;
  slug: string;
  description: string;
  platforms: {
    name: string;
    icon: string;
  };
}

export default function MarketplaceFilters({
  filters,
  onFiltersChange,
  onApplyFilters,
}: FiltersProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [platforms, setPlatforms] = useState<Platform[]>([]);
  const [contentTypes, setContentTypes] = useState<ContentType[]>([]);
  const [loading, setLoading] = useState(true);

  // Collapsible states
  const [categoriesOpen, setCategoriesOpen] = useState(true);
  const [platformsOpen, setPlatformsOpen] = useState(true);
  const [contentTypesOpen, setContentTypesOpen] = useState(false);
  const [priceOpen, setPriceOpen] = useState(false);
  const [followersOpen, setFollowersOpen] = useState(false);
  const [demographicsOpen, setDemographicsOpen] = useState(false);

  useEffect(() => {
    loadFilterData();
  }, []);

  const loadFilterData = async () => {
    try {
      setLoading(true);

      const [categoriesResult, platformsResult, contentTypesResult] =
        await Promise.all([getCategories(), getPlatforms(), getContentTypes()]);

      if (categoriesResult.data) setCategories(categoriesResult.data);
      if (platformsResult.data) setPlatforms(platformsResult.data);
      if (contentTypesResult.data) setContentTypes(contentTypesResult.data);
    } catch (error) {
      console.error('Error loading filter data:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateFilters = (newFilters: Partial<SearchFilters>) => {
    const updatedFilters = { ...filters, ...newFilters, offset: 0 };
    onFiltersChange(updatedFilters);
  };

  const toggleCategory = (categoryId: number) => {
    const currentCategories = filters.categories || [];
    const newCategories = currentCategories.includes(categoryId)
      ? currentCategories.filter(id => id !== categoryId)
      : [...currentCategories, categoryId];

    updateFilters({ categories: newCategories });
  };

  const togglePlatform = (platformId: number) => {
    const currentPlatforms = filters.platforms || [];
    const newPlatforms = currentPlatforms.includes(platformId)
      ? currentPlatforms.filter(id => id !== platformId)
      : [...currentPlatforms, platformId];

    updateFilters({ platforms: newPlatforms });
  };

  const toggleContentType = (contentTypeId: number) => {
    const currentContentTypes = filters.contentTypes || [];
    const newContentTypes = currentContentTypes.includes(contentTypeId)
      ? currentContentTypes.filter(id => id !== contentTypeId)
      : [...currentContentTypes, contentTypeId];

    updateFilters({ contentTypes: newContentTypes });
  };

  const clearAllFilters = () => {
    onFiltersChange({
      search: filters.search || '',
      sortBy: filters.sortBy || 'relevance',
      limit: filters.limit || 12,
      offset: 0,
    });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.categories?.length) count++;
    if (filters.platforms?.length) count++;
    if (filters.contentTypes?.length) count++;
    if (filters.minPrice || filters.maxPrice) count++;
    if (filters.minFollowers || filters.maxFollowers) count++;
    if (filters.location) count++;
    if (filters.gender) count++;
    if (filters.minAge || filters.maxAge) count++;
    if (filters.verifiedOnly) count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-4 bg-muted rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {[...Array(3)].map((_, j) => (
                  <div key={j} className="h-3 bg-muted rounded"></div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Filter Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filteri
              {activeFiltersCount > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {activeFiltersCount}
                </Badge>
              )}
            </CardTitle>
            {activeFiltersCount > 0 && (
              <Button variant="ghost" size="sm" onClick={clearAllFilters}>
                <X className="h-4 w-4 mr-1" />
                Očisti
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <Button onClick={onApplyFilters} className="w-full">
            Primeni filtere
          </Button>
        </CardContent>
      </Card>

      {/* Categories */}
      <Card>
        <Collapsible open={categoriesOpen} onOpenChange={setCategoriesOpen}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">
                  Kategorije
                  {filters.categories?.length ? (
                    <Badge variant="secondary" className="ml-2">
                      {filters.categories.length}
                    </Badge>
                  ) : null}
                </CardTitle>
                {categoriesOpen ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </div>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="pt-0">
              <div className="space-y-3">
                {categories.map(category => (
                  <div
                    key={category.id}
                    className="flex items-center space-x-2"
                  >
                    <Checkbox
                      id={`category-${category.id}`}
                      checked={
                        filters.categories?.includes(category.id) || false
                      }
                      onCheckedChange={() => toggleCategory(category.id)}
                    />
                    <label
                      htmlFor={`category-${category.id}`}
                      className="flex items-center gap-2 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                    >
                      <span className="text-lg">{category.icon}</span>
                      {category.name}
                    </label>
                  </div>
                ))}
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Platforms */}
      <Card>
        <Collapsible open={platformsOpen} onOpenChange={setPlatformsOpen}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">
                  Platforme
                  {filters.platforms?.length ? (
                    <Badge variant="secondary" className="ml-2">
                      {filters.platforms.length}
                    </Badge>
                  ) : null}
                </CardTitle>
                {platformsOpen ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </div>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="pt-0">
              <div className="space-y-3">
                {platforms.map(platform => (
                  <div
                    key={platform.id}
                    className="flex items-center space-x-2"
                  >
                    <Checkbox
                      id={`platform-${platform.id}`}
                      checked={
                        filters.platforms?.includes(platform.id) || false
                      }
                      onCheckedChange={() => togglePlatform(platform.id)}
                    />
                    <label
                      htmlFor={`platform-${platform.id}`}
                      className="flex items-center gap-2 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                    >
                      <PlatformIconSimple
                        platform={platform.name}
                        size="sm"
                      />
                      {platform.name}
                    </label>
                  </div>
                ))}
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Content Types */}
      <Card>
        <Collapsible open={contentTypesOpen} onOpenChange={setContentTypesOpen}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">
                  Tipovi sadržaja
                  {filters.contentTypes?.length ? (
                    <Badge variant="secondary" className="ml-2">
                      {filters.contentTypes.length}
                    </Badge>
                  ) : null}
                </CardTitle>
                {contentTypesOpen ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </div>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="pt-0">
              <div className="space-y-3">
                {contentTypes.map(contentType => (
                  <div
                    key={contentType.id}
                    className="flex items-center space-x-2"
                  >
                    <Checkbox
                      id={`content-type-${contentType.id}`}
                      checked={
                        filters.contentTypes?.includes(contentType.id) || false
                      }
                      onCheckedChange={() => toggleContentType(contentType.id)}
                    />
                    <label
                      htmlFor={`content-type-${contentType.id}`}
                      className="flex items-center gap-2 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                    >
                      <span className="text-lg">
                        {contentType.platforms.icon}
                      </span>
                      {contentType.name}
                      <span className="text-xs text-muted-foreground">
                        ({contentType.platforms.name})
                      </span>
                    </label>
                  </div>
                ))}
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Price Range */}
      <Card>
        <Collapsible open={priceOpen} onOpenChange={setPriceOpen}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">
                  Cijena (€)
                  {(filters.minPrice || filters.maxPrice) && (
                    <Badge variant="secondary" className="ml-2">
                      {filters.minPrice || 0} - {filters.maxPrice || 1000} €
                    </Badge>
                  )}
                </CardTitle>
                {priceOpen ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </div>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="pt-0 space-y-4">
              <div>
                <div className="flex justify-between text-sm text-muted-foreground mb-2">
                  <span>{filters.minPrice || 0} KM</span>
                  <span>{filters.maxPrice || 1000} KM</span>
                </div>
                <Slider
                  value={[filters.minPrice || 0, filters.maxPrice || 1000]}
                  onValueChange={([min, max]) =>
                    updateFilters({ minPrice: min, maxPrice: max })
                  }
                  max={1000}
                  min={0}
                  step={10}
                  className="w-full"
                />
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Demographics */}
      <Card>
        <Collapsible open={demographicsOpen} onOpenChange={setDemographicsOpen}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">Demografija</CardTitle>
                {demographicsOpen ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </div>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="pt-0 space-y-4">
              {/* Gender */}
              <div>
                <label className="text-sm font-medium mb-2 block">Pol</label>
                <Select
                  value={filters.gender || ''}
                  onValueChange={value =>
                    updateFilters({ gender: value as any })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Svi polovi" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Svi polovi</SelectItem>
                    <SelectItem value="male">Muški</SelectItem>
                    <SelectItem value="female">Ženski</SelectItem>
                    <SelectItem value="other">Ostalo</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Age Range */}
              <div>
                <label className="text-sm font-medium mb-2 block">Uzrast</label>
                <div className="flex justify-between text-sm text-muted-foreground mb-2">
                  <span>{filters.minAge || 18} godina</span>
                  <span>{filters.maxAge || 65} godina</span>
                </div>
                <Slider
                  value={[filters.minAge || 18, filters.maxAge || 65]}
                  onValueChange={([min, max]) =>
                    updateFilters({ minAge: min, maxAge: max })
                  }
                  max={65}
                  min={18}
                  step={1}
                  className="w-full"
                />
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    </div>
  );
}
