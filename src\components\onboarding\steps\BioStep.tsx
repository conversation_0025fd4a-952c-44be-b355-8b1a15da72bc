'use client';

import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

interface BioStepProps {
  value: string;
  onChange: (value: string) => void;
  onNext: () => void;
  onBack: () => void;
}

export function BioStep({ value, onChange, onNext, onBack }: BioStepProps) {
  const handleNext = () => {
    onNext();
  };

  const characterCount = value.length;
  const maxCharacters = 500;

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-2">Opišite sebe</h2>
        <p className="text-white/70">
          Napišite kratku biografiju koja će biti vidljiva na vašem profilu
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="bio" className="text-white">
            Biografija (opcionalno)
          </Label>
          <Textarea
            id="bio"
            value={value}
            onChange={e => onChange(e.target.value)}
            placeholder="Opišite sebe, vaše interese, stil sadržaja koji kreirate..."
            className="min-h-[120px] resize-none bg-white/10 border-white/30 text-white placeholder:text-white/50 focus:border-white/50 focus:ring-white/20"
            maxLength={maxCharacters}
          />
          <div className="flex justify-between items-center mt-1">
            <p className="text-sm text-white/60">
              Možete preskočiti ovaj korak ako ne želite da dodajete biografiju
              sada.
            </p>
            <span
              className={`text-sm ${
                characterCount > maxCharacters * 0.9
                  ? 'text-orange-300'
                  : 'text-white/60'
              }`}
            >
              {characterCount}/{maxCharacters}
            </span>
          </div>
        </div>

        <div className="text-sm text-white/60">
          <p>Dobra biografija sadrži:</p>
          <ul className="list-disc list-inside mt-1 space-y-1">
            <li>Vaše interese i hobije</li>
            <li>Tip sadržaja koji kreirate</li>
            <li>Šta vas čini jedinstvenim</li>
            <li>Vaš stil komunikacije</li>
          </ul>
        </div>
      </div>

      <div className="flex gap-3">
        <Button
          variant="outline"
          onClick={onBack}
          className="flex-1 border-white/30 text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm"
        >
          Nazad
        </Button>
        <Button
          onClick={handleNext}
          className="flex-1 bg-white text-instagram-purple hover:bg-white/90 font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
        >
          Dalje
        </Button>
      </div>
    </div>
  );
}
