import { Database } from './database.types';

// Base types from database
export type Campaign = Database['public']['Tables']['campaigns']['Row'];
export type CampaignApplication =
  Database['public']['Tables']['campaign_applications']['Row'];
export type Business = Database['public']['Tables']['businesses']['Row'];
export type Profile = Database['public']['Tables']['profiles']['Row'];
export type Influencer = Database['public']['Tables']['influencers']['Row'];
export type Category = Database['public']['Tables']['categories']['Row'];
export type Platform = Database['public']['Tables']['platforms']['Row'];

// Extended platform type with additional fields
export interface PlatformWithDetails extends Platform {
  posts_required?: number;
}

// Enums
export type ApplicationStatus =
  Database['public']['Enums']['application_status'];
export type CampaignStatus = Database['public']['Enums']['campaign_status'];
export type ContentType = Database['public']['Enums']['content_type'];
export type UserType = Database['public']['Enums']['user_type'];

// Complex joined types for campaign details
export interface CampaignWithBusiness extends Campaign {
  business: Business & {
    profile: Profile;
  };
  platforms: Platform[];
  categories: Category[];
}

// Application with related data
export interface ApplicationWithDetails extends CampaignApplication {
  campaign: CampaignWithBusiness;
  influencer: Influencer & {
    profile: Profile;
  };
}

// Campaign with application count and business info
export interface CampaignDetails extends Campaign {
  business_id: string;
  business?: Business & {
    profile?: Profile;
  };
  application_count?: number;
  applications_count?: number;
  views_count?: number;
  platforms?: PlatformWithDetails[];
  categories?: Category[];
  // Business profile fields (flattened for easier access)
  business_avatar?: string | null;
  company_name?: string;
  business_username?: string | null;
  industry?: string | null;
  // Additional campaign fields
  collaboration_type?: string;
  location?: string;
  application_deadline?: string | null;
  min_followers?: number;
  max_followers?: number;
  age_range_min?: number;
  age_range_max?: number;
  gender?: string;
  revisions_included?: number;
}

// Influencer application response type
export interface InfluencerApplicationResponse {
  hasApplied: boolean;
  application?: CampaignApplication;
  appliedAt?: string | null;
  status?: ApplicationStatus | null;
}

// Target audience type (from campaign.target_audience JSON field)
export interface TargetAudience {
  age_range?: {
    min: number;
    max: number;
  };
  gender?: string[];
  location?: string[];
  interests?: string[];
  languages?: string[];
}

// Campaign form data
export interface CampaignFormData {
  title: string;
  description: string;
  budget?: number;
  content_types: ContentType[];
  start_date?: string;
  end_date?: string;
  requirements?: string;
  deliverables?: string;
  campaign_goal?: string;
  product_description?: string;
  target_audience?: TargetAudience;
  show_business_name?: boolean;
}

// Application form data
export interface ApplicationFormData {
  proposed_rate: number;
  proposal_text?: string;
  portfolio_links?: string[];
  experience_relevant?: string;
  audience_insights?: string;
  delivery_timeframe?: string;
  available_start_date?: string;
  additional_services?: string;
}
