import { supabase } from './supabase';
import { Database } from './database.types';
import {
  notifyJobCompletionSubmitted,
  notifyJobCompletionApproved,
  notifyJobCompletionRejected,
} from './notifications';

type JobCompletion = Database['public']['Tables']['job_completions']['Row'];
type JobCompletionInsert =
  Database['public']['Tables']['job_completions']['Insert'];
type JobCompletionUpdate =
  Database['public']['Tables']['job_completions']['Update'];

// Export types
export type { JobCompletion, JobCompletionInsert, JobCompletionUpdate };

export interface JobCompletionWithDetails extends JobCompletion {
  campaign_application?: {
    id: string;
    proposed_rate: number;
    campaign: {
      id: string;
      title: string;
      description: string;
      business?: {
        company_name: string;
        profiles?: {
          username: string;
          avatar_url: string | null;
        };
      };
    };
  };
  direct_offer?: {
    id: string;
    title: string;
    description: string;
    budget: number;
    businesses?: {
      company_name: string;
      profiles?: {
        username: string;
        avatar_url: string | null;
      };
    };
  };
  influencer_profile?: {
    username: string;
    full_name: string;
    public_display_name: string | null;
    avatar_url: string | null;
    user_type: string;
  };
  business_profile?: {
    username: string;
    full_name: string;
    public_display_name: string | null;
    avatar_url: string | null;
    user_type: string;
  };
  // Ocjene povezane sa ovim job completion-om
  business_to_influencer_review?: {
    id: string;
    rating: number | null;
    comment: string | null;
    created_at: string | null;
  };
  influencer_to_business_review?: {
    id: string;
    rating: number | null;
    comment: string | null;
    created_at: string | null;
  };
}

/**
 * Get job completions for current user (influencer or business)
 */
export async function getUserJobCompletions(status?: string) {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    const { data: profile } = await supabase
      .from('profiles')
      .select('user_type')
      .eq('id', user.user.id)
      .single();

    if (!profile) throw new Error('Profile not found');

    let query = supabase
      .from('job_completions')
      .select(
        `
        *,
        campaign_application:campaign_applications(
          id,
          proposed_rate,
          campaign:campaigns(
            id,
            title,
            description,
            business:businesses(
              company_name,
              profiles:profiles(
                username,
                avatar_url
              )
            )
          )
        ),
        direct_offer:direct_offers(
          id,
          title,
          description,
          budget,
          businesses(
            company_name,
            profiles(
              username,
              avatar_url
            )
          )
        ),
        influencer_profile:profiles!job_completions_influencer_id_fkey(
          username,
          full_name,
          public_display_name,
          avatar_url,
          user_type
        ),
        business_profile:profiles!job_completions_business_id_fkey(
          username,
          full_name,
          public_display_name,
          avatar_url,
          user_type
        )
      `
      )
      .order('created_at', { ascending: false });

    // Filter based on user type
    if (profile.user_type === 'influencer') {
      query = query.eq('influencer_id', user.user.id);
    } else {
      query = query.eq('business_id', user.user.id);
    }

    // Filter by status if provided
    if (status) {
      query = query.eq('status', status);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching job completions:', error);
      return { data: null, error };
    }

    if (!data || data.length === 0) {
      return { data: [], error: null };
    }

    // Dohvati reviews za sve job completions
    const jobCompletionIds = data.map(jc => jc.id);
    const { data: reviews } = await supabase
      .from('job_reviews')
      .select('id, job_completion_id, rating, comment, created_at, review_type')
      .in('job_completion_id', jobCompletionIds);

    // Dodaj reviews u job completions
    const dataWithReviews = data.map(jobCompletion => {
      const businessToInfluencerReview = reviews?.find(
        r =>
          r.job_completion_id === jobCompletion.id &&
          r.review_type === 'business_to_influencer'
      );
      const influencerToBusinessReview = reviews?.find(
        r =>
          r.job_completion_id === jobCompletion.id &&
          r.review_type === 'influencer_to_business'
      );

      return {
        ...jobCompletion,
        business_to_influencer_review: businessToInfluencerReview || null,
        influencer_to_business_review: influencerToBusinessReview || null,
      };
    });

    return { data: dataWithReviews, error: null };
  } catch (error) {
    console.error('Unexpected error fetching job completions:', error);
    return { data: null, error };
  }
}

/**
 * Get pending job completions for business review
 */
export async function getPendingJobCompletions() {
  return getUserJobCompletions('submitted');
}

/**
 * Get job completion history
 */
export async function getJobCompletionHistory() {
  return getUserJobCompletions();
}

/**
 * Submit job completion for direct offer (influencer marks job as completed)
 */
export async function submitDirectOfferJobCompletion(
  directOfferId: string,
  submissionNotes: string,
  submissionFiles?: any[]
) {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    // Get direct offer details to get business_id
    const { data: offer } = await supabase
      .from('direct_offers')
      .select(
        `
        influencer_id,
        business_id,
        title,
        budget
      `
      )
      .eq('id', directOfferId)
      .single();

    if (!offer) {
      throw new Error('Direct offer not found');
    }

    if (offer.influencer_id !== user.user.id) {
      throw new Error('Not authorized to submit this job completion');
    }

    const jobCompletionData: JobCompletionInsert = {
      direct_offer_id: directOfferId,
      influencer_id: user.user.id,
      business_id: offer.business_id,
      status: 'submitted',
      submitted_at: new Date().toISOString(),
      submission_notes: submissionNotes,
      submission_files: submissionFiles
        ? JSON.stringify(submissionFiles)
        : null,
    };

    const { data, error } = await supabase
      .from('job_completions')
      .insert(jobCompletionData)
      .select()
      .single();

    if (error) {
      console.error('Error submitting job completion:', error);
      return { data: null, error };
    }

    // Create notification for business
    try {
      // Dobij informacije o influencer-u
      const { data: influencerProfile } = await supabase
        .from('profiles')
        .select('username')
        .eq('id', offer.influencer_id)
        .single();

      if (influencerProfile) {
        const projectType = offer.offer_type === 'package_order' ? 'package_order' : 'direct_offer';
        await notifyJobCompletionSubmitted(
          offer.business_id,
          influencerProfile.username,
          offer.title,
          data.id,
          projectType
        );
      }
    } catch (notificationError) {
      console.error('Error creating notification:', notificationError);
      // Don't throw error for notification failure
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error submitting job completion:', error);
    return { data: null, error };
  }
}

/**
 * Submit job completion (influencer marks job as completed)
 */
export async function submitJobCompletion(
  campaignApplicationId: string,
  submissionNotes: string,
  submissionFiles?: any[]
) {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    // Get campaign application details to get business_id
    const { data: application } = await supabase
      .from('campaign_applications')
      .select(
        `
        influencer_id,
        campaign:campaigns!inner(
          business_id
        )
      `
      )
      .eq('id', campaignApplicationId)
      .single();

    if (!application) {
      throw new Error('Campaign application not found');
    }

    if (application.influencer_id !== user.user.id) {
      throw new Error('Not authorized to submit this job completion');
    }

    const jobCompletionData: JobCompletionInsert = {
      campaign_application_id: campaignApplicationId,
      influencer_id: user.user.id,
      business_id: application.campaign.business_id,
      status: 'submitted',
      submitted_at: new Date().toISOString(),
      submission_notes: submissionNotes,
      submission_files: submissionFiles
        ? JSON.stringify(submissionFiles)
        : null,
    };

    const { data, error } = await supabase
      .from('job_completions')
      .insert(jobCompletionData)
      .select()
      .single();

    if (error) {
      console.error('Error submitting job completion:', error);
      return { data: null, error };
    }

    // Create notification for business
    try {
      // Dobij informacije o influencer-u i business-u
      const { data: influencerProfile } = await supabase
        .from('profiles')
        .select('username')
        .eq('id', application.influencer_id)
        .single();

      if (influencerProfile) {
        await notifyJobCompletionSubmitted(
          application.campaign.business_id,
          influencerProfile.username,
          application.campaign.title,
          data.id,
          'campaign'
        );
      }
    } catch (notificationError) {
      console.error('Error creating notification:', notificationError);
      // Don't throw error for notification failure
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error submitting job completion:', error);
    return { data: null, error };
  }
}

/**
 * Mark job completion as completed (final step after approval)
 */
export async function markJobAsCompleted(jobCompletionId: string) {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    const updateData: JobCompletionUpdate = {
      status: 'completed',
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await supabase
      .from('job_completions')
      .update(updateData)
      .eq('id', jobCompletionId)
      .eq('status', 'approved') // Only approved jobs can be marked as completed
      .select()
      .single();

    if (error) {
      console.error('Error marking job as completed:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error marking job as completed:', error);
    return { data: null, error };
  }
}

/**
 * Get job completion by direct offer ID
 */
export async function getJobCompletionByDirectOffer(directOfferId: string) {
  try {
    const { data, error } = await supabase
      .from('job_completions')
      .select(
        `
        *,
        influencer_profile:profiles!job_completions_influencer_id_fkey(
          username,
          full_name,
          public_display_name,
          avatar_url,
          user_type
        ),
        business_profile:profiles!job_completions_business_id_fkey(
          username,
          full_name,
          public_display_name,
          avatar_url,
          user_type
        ),
        direct_offer:direct_offers!job_completions_direct_offer_id_fkey(
          id,
          title,
          description,
          budget
        )
      `
      )
      .eq('direct_offer_id', directOfferId)
      .maybeSingle();

    if (error) {
      console.error('Error fetching job completion by direct offer:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error(
      'Unexpected error fetching job completion by direct offer:',
      error
    );
    return { data: null, error };
  }
}

/**
 * Get job completion by campaign application ID
 */
export async function getJobCompletionByCampaignApplication(
  campaignApplicationId: string
) {
  try {
    const { data, error } = await supabase
      .from('job_completions')
      .select(
        `
        *,
        influencer_profile:profiles!job_completions_influencer_id_fkey(
          username,
          full_name,
          public_display_name,
          avatar_url,
          user_type
        ),
        business_profile:profiles!job_completions_business_id_fkey(
          username,
          full_name,
          public_display_name,
          avatar_url,
          user_type
        ),
        campaign_application:campaign_applications!job_completions_campaign_application_id_fkey(
          id,
          proposed_rate,
          campaign:campaigns!inner(
            id,
            title,
            description
          )
        )
      `
      )
      .eq('campaign_application_id', campaignApplicationId)
      .maybeSingle();

    if (error) {
      console.error(
        'Error fetching job completion by campaign application:',
        error
      );
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error(
      'Unexpected error fetching job completion by campaign application:',
      error
    );
    return { data: null, error };
  }
}

/**
 * Approve job completion and create review (business approves influencer's work)
 */
export async function approveJobCompletion(
  jobCompletionId: string,
  rating: number,
  reviewNotes?: string,
  businessNotes?: string
) {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    // Get current job completion
    const { data: jobCompletion, error: fetchError } = await supabase
      .from('job_completions')
      .select('*')
      .eq('id', jobCompletionId)
      .single();

    if (fetchError || !jobCompletion) {
      return {
        data: null,
        error: fetchError || new Error('Job completion not found'),
      };
    }

    // Update job completion status
    const { error: updateError } = await supabase
      .from('job_completions')
      .update({
        status: 'approved',
        reviewed_at: new Date().toISOString(),
        business_notes: businessNotes,
        approved_by: user.user.id,
        updated_at: new Date().toISOString(),
      })
      .eq('id', jobCompletionId);

    if (updateError) {
      return { data: null, error: updateError };
    }

    // Create review from business to influencer
    const { error: reviewError } = await supabase.from('job_reviews').insert({
      job_completion_id: jobCompletionId,
      reviewer_id: user.user.id,
      reviewee_id: jobCompletion.influencer_id,
      rating: rating,
      comment: reviewNotes,
      review_type: 'business_to_influencer',
    });

    if (reviewError) {
      return { data: null, error: reviewError };
    }

    // Send notification to influencer
    try {
      // Dobij informacije o business-u i projektu
      const { data: businessProfile } = await supabase
        .from('profiles')
        .select('username')
        .eq('id', jobCompletion.business_id)
        .single();

      let projectTitle = 'Unknown Project';
      if (jobCompletion.campaign_application_id) {
        const { data: campaignApp } = await supabase
          .from('campaign_applications')
          .select('campaigns!inner(title)')
          .eq('id', jobCompletion.campaign_application_id)
          .single();
        projectTitle = campaignApp?.campaigns?.title || projectTitle;
      } else if (jobCompletion.direct_offer_id) {
        const { data: offer } = await supabase
          .from('direct_offers')
          .select('title')
          .eq('id', jobCompletion.direct_offer_id)
          .single();
        projectTitle = offer?.title || projectTitle;
      }

      if (businessProfile) {
        await notifyJobCompletionApproved(
          jobCompletion.influencer_id,
          businessProfile.username,
          projectTitle,
          jobCompletionId
        );
      }
    } catch (notificationError) {
      console.warn('Failed to send notification:', notificationError);
      // Don't fail the whole operation if notification fails
    }

    return { data: { success: true }, error: null };
  } catch (error) {
    console.error('Error approving job completion:', error);
    return { data: null, error };
  }
}

/**
 * Reject job completion (business rejects influencer's work)
 */
export async function rejectJobCompletion(
  jobCompletionId: string,
  businessNotes: string
) {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    // Get current job completion
    const { data: jobCompletion, error: fetchError } = await supabase
      .from('job_completions')
      .select('*')
      .eq('id', jobCompletionId)
      .single();

    if (fetchError || !jobCompletion) {
      return {
        data: null,
        error: fetchError || new Error('Job completion not found'),
      };
    }

    // Update job completion status
    const { error: updateError } = await supabase
      .from('job_completions')
      .update({
        status: 'rejected',
        reviewed_at: new Date().toISOString(),
        business_notes: businessNotes,
        approved_by: user.user.id,
        updated_at: new Date().toISOString(),
      })
      .eq('id', jobCompletionId);

    if (updateError) {
      return { data: null, error: updateError };
    }

    // Send notification to influencer
    try {
      // Dobij informacije o business-u i projektu
      const { data: businessProfile } = await supabase
        .from('profiles')
        .select('username')
        .eq('id', jobCompletion.business_id)
        .single();

      let projectTitle = 'Unknown Project';
      if (jobCompletion.campaign_application_id) {
        const { data: campaignApp } = await supabase
          .from('campaign_applications')
          .select('campaigns!inner(title)')
          .eq('id', jobCompletion.campaign_application_id)
          .single();
        projectTitle = campaignApp?.campaigns?.title || projectTitle;
      } else if (jobCompletion.direct_offer_id) {
        const { data: offer } = await supabase
          .from('direct_offers')
          .select('title')
          .eq('id', jobCompletion.direct_offer_id)
          .single();
        projectTitle = offer?.title || projectTitle;
      }

      if (businessProfile) {
        await notifyJobCompletionRejected(
          jobCompletion.influencer_id,
          businessProfile.username,
          projectTitle,
          jobCompletionId,
          businessNotes
        );
      }
    } catch (notificationError) {
      console.warn('Failed to send notification:', notificationError);
      // Don't fail the whole operation if notification fails
    }

    return { data: { success: true }, error: null };
  } catch (error) {
    console.error('Error rejecting job completion:', error);
    return { data: null, error };
  }
}

/**
 * Get job completion by ID with full details
 */
export async function getJobCompletionById(id: string) {
  try {
    const { data, error } = await supabase
      .from('job_completions')
      .select(
        `
        *,
        campaign_application:campaign_applications!inner(
          id,
          proposed_rate,
          campaign:campaigns!inner(
            id,
            title,
            description,
            business:businesses!inner(
              company_name,
              profiles!inner(
                username,
                full_name,
                avatar_url
              )
            )
          )
        ),
        influencer_profile:profiles!job_completions_influencer_id_fkey(
          username,
          full_name,
          public_display_name,
          avatar_url,
          user_type
        ),
        business_profile:profiles!job_completions_business_id_fkey(
          username,
          full_name,
          public_display_name,
          avatar_url,
          user_type
        )
      `
      )
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching job completion:', error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error fetching job completion:', error);
    return { data: null, error };
  }
}
