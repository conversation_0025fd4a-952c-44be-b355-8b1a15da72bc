'use client';

import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface CountryStepProps {
  value: string;
  onChange: (value: string) => void;
  onNext: () => void;
  onBack: () => void;
  title?: string;
  description?: string;
}

const balkanCountries = [
  { code: 'RS', name: '<PERSON><PERSON><PERSON>' },
  { code: 'BA', name: 'Bosna i Hercegovina' },
  { code: 'HR', name: '<PERSON><PERSON><PERSON><PERSON>' },
  { code: 'ME', name: 'Crna Gora' },
  { code: 'M<PERSON>', name: '<PERSON><PERSON> Makedonija' },
  { code: 'SI', name: 'Slovenija' },
  { code: 'AL', name: 'Albani<PERSON>' },
  { code: 'BG', name: '<PERSON><PERSON><PERSON><PERSON>' },
  { code: 'R<PERSON>', name: '<PERSON><PERSON><PERSON><PERSON>' },
  { code: 'G<PERSON>', name: '<PERSON><PERSON><PERSON><PERSON>' },
];

export function CountryStep({
  value,
  onChange,
  onNext,
  onBack,
  title = 'Iz koje ste države?',
  description = 'Ova informacija pomaže brendovima da pronađu lokalne influencere',
}: CountryStepProps) {
  const handleNext = () => {
    if (value) {
      onNext();
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-2">{title}</h2>
        <p className="text-white/70">{description}</p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="country" className="text-white">
            Država
          </Label>
          <Select value={value} onValueChange={onChange}>
            <SelectTrigger className="bg-white/10 border-white/30 text-white focus:border-white/50 focus:ring-white/20">
              <SelectValue
                placeholder="Izaberite državu"
                className="text-white/50"
              />
            </SelectTrigger>
            <SelectContent className="glass-instagram shadow-2xl">
              {balkanCountries.map(country => (
                <SelectItem
                  key={country.code}
                  value={country.name}
                  className="text-white hover:bg-white/20 focus:bg-white/20 cursor-pointer"
                >
                  {country.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="text-sm text-white/60">
          <p>Trenutno podržavamo influencere iz Balkanskih zemalja.</p>
        </div>
      </div>

      <div className="flex gap-3">
        <Button
          variant="outline"
          onClick={onBack}
          className="flex-1 border-white/30 text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm"
        >
          Nazad
        </Button>
        <Button
          onClick={handleNext}
          disabled={!value}
          className="flex-1 bg-white text-instagram-purple hover:bg-white/90 font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
        >
          Dalje
        </Button>
      </div>
    </div>
  );
}
