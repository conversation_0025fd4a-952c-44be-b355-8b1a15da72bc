# Influencer Marketing Platforma

Platforma za povezivanje influencera i biznisa za marketing kampanje na bosanskom jeziku.

## 🚀 Tehnologije

- **Frontend**: Next.js 14 (App Router), TypeScript, Tailwind CSS, Shadcn/ui
- **Backend**: Supabase (PostgreSQL, Auth, Real-time, Storage)
- **Plaćanja**: Stripe Connect
- **Deployment**: Vercel + Supabase Cloud

## 📋 Funkcionalnosti

- 👥 Registracija i profili (influenceri i biznisi)
- 📢 Kreiranje i pregled kampanja
- 💬 Real-time chat sistem
- 💳 Escrow payment sistem
- ⭐ Ocjenjivanje i reviews
- 📊 Dashboard i analytics
- 💶 EUR valuta sistem (migracija sa KM)

## 🛠️ Development Setup

### Preduslovi

- Node.js 18+
- npm ili yarn
- Supabase account

### Instalacija

1. Kloniraj repository:
```bash
git clone <repository-url>
cd influencer-platform
```

2. Instaliraj dependencies:
```bash
npm install
```

3. Kopiraj environment variables:
```bash
cp .env.local.example .env.local
```

4. Konfiguriši Supabase:
   - Kreiraj novi projekat na [supabase.com](https://supabase.com)
   - Kopiraj URL i anon key u `.env.local`
   - Pokreni SQL schema iz `supabase-schema.sql`

5. Pokreni development server:
```bash
npm run dev
```

Otvori [http://localhost:3000](http://localhost:3000) u browseru.

## 📝 Scripts

- `npm run dev` - Development server
- `npm run build` - Production build
- `npm run start` - Production server
- `npm run lint` - ESLint check
- `npm run lint:fix` - ESLint fix
- `npm run format` - Prettier format
- `npm run type-check` - TypeScript check

## 🗄️ Database Schema

Database schema se nalazi u `supabase-schema.sql` fajlu. Uključuje:

- Profili korisnika (influenceri i biznisi)
- Kampanje i aplikacije
- Chat sistem
- Payment tracking
- Reviews i ratings

## 🚀 Deployment

Projekat je konfigurisan za deployment na Vercel:

1. Push kod na GitHub
2. Konektuj repository sa Vercel
3. Konfiguriši environment variables
4. Deploy!

## 📱 Mobile App

Planirana je mobilna aplikacija u React Native/Flutter nakon MVP-a.

## 🤝 Contributing

1. Fork projekat
2. Kreiraj feature branch
3. Commit promjene
4. Push na branch
5. Otvori Pull Request

## 📊 Trenutno Stanje (21.01.2025)

### ✅ Implementirano:
- **Kompletna autentifikacija** - registracija, login, email verifikacija
- **Influencer profil sistem** - kreiranje i pregled profila
- **Dashboard** - influencer dashboard sa statistikama
- **Responsive dizajn** - mobile-first sa Shadcn/ui
- **Database schema** - profiles, influencers, businesses tabele
- **Auth flow** - potpuno funkcionalan od registracije do dashboard-a

### 🧪 Kako testirati:
1. Pokrenite `npm run dev`
2. Idite na http://localhost:3000
3. Registrujte se kao influencer
4. Verifikujte email
5. Popunite profil
6. Vidite dashboard

### 🚧 Sledeće:
- Biznis profil kreiranje
- Kampanje sistem
- Chat funkcionalnost
- Stripe plaćanja

## 📄 License

MIT License
