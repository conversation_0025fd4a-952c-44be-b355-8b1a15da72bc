'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  CheckCircle,
  XCircle,
  Clock,
  FileText,
  User,
  Calendar,
  Euro,
  Star,
  ExternalLink,
  MessageCircle,
} from 'lucide-react';
import { JobCompletionWithDetails } from '@/lib/job-completions';
import {
  approveJobCompletion,
  rejectJobCompletion,
} from '@/lib/job-completions';
import { toast } from 'sonner';
import { formatDate as formatDateUtil } from '@/lib/date-utils';
import { getDisplayName } from '@/lib/utils';

interface JobCompletionCardProps {
  jobCompletion: JobCompletionWithDetails;
  userType: 'influencer' | 'business';
  onUpdate?: () => void;
}

const statusConfig = {
  pending: {
    label: 'Na čekanju',
    color: 'bg-yellow-100 text-yellow-800',
    icon: Clock,
  },
  submitted: {
    label: 'Poslano na pregled',
    color: 'bg-blue-100 text-blue-800',
    icon: FileText,
  },
  approved: {
    label: 'Odobreno',
    color: 'bg-green-100 text-green-800',
    icon: CheckCircle,
  },
  rejected: {
    label: 'Odbačeno',
    color: 'bg-red-100 text-red-800',
    icon: XCircle,
  },
  completed: {
    label: 'Završeno',
    color: 'bg-green-100 text-green-800',
    icon: CheckCircle,
  },
};

export function JobCompletionCard({
  jobCompletion,
  userType,
  onUpdate,
}: JobCompletionCardProps) {
  const [isReviewing, setIsReviewing] = useState(false);
  const [reviewNotes, setReviewNotes] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const status = jobCompletion.status || 'pending';
  const config = statusConfig[status as keyof typeof statusConfig];
  const StatusIcon = config?.icon || Clock;

  const formatDate = (dateString: string | null) => {
    return formatDateUtil(dateString);
  };

  const renderStars = (rating: number | null) => {
    if (!rating) return null;

    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map(star => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating
                ? 'fill-yellow-400 text-yellow-400'
                : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-2 text-sm font-medium">{rating}/5</span>
      </div>
    );
  };

  const handleApprove = async () => {
    setIsLoading(true);
    try {
      const { error } = await approveJobCompletion(
        jobCompletion.id,
        reviewNotes
      );
      if (error) {
        toast.error('Failed to approve job completion');
        return;
      }
      toast.success('Job completion approved successfully');
      setIsReviewing(false);
      setReviewNotes('');
      onUpdate?.();
    } catch (error) {
      toast.error('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReject = async () => {
    if (!reviewNotes.trim()) {
      toast.error('Please provide a reason for rejection');
      return;
    }

    setIsLoading(true);
    try {
      const { error } = await rejectJobCompletion(
        jobCompletion.id,
        reviewNotes
      );
      if (error) {
        toast.error('Failed to reject job completion');
        return;
      }
      toast.success('Job completion rejected');
      setIsReviewing(false);
      setReviewNotes('');
      onUpdate?.();
    } catch (error) {
      toast.error('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const canReview = userType === 'business' && status === 'submitted';

  return (
    <Card className="w-full relative overflow-hidden bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
      <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
      <div className="relative">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <CardTitle className="text-lg">
                {jobCompletion.direct_offer?.title ||
                  jobCompletion.campaign_application?.campaign?.title ||
                  'Posao'}
              </CardTitle>
              <div className="flex items-center gap-2">
                <StatusIcon className="h-4 w-4" />
                <Badge className={config?.color}>{config?.label}</Badge>
              </div>
            </div>
            <div className="text-right">
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <Euro className="h-4 w-4" />
                {jobCompletion.direct_offer?.budget ||
                  jobCompletion.campaign_application?.proposed_rate ||
                  0}
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Job Details */}
          <div className="space-y-2">
            <h4 className="font-medium text-sm">
              {jobCompletion.direct_offer
                ? 'Detalji ponude'
                : 'Detalji kampanje'}
            </h4>
            <p className="text-sm text-muted-foreground">
              {jobCompletion.direct_offer?.description ||
                jobCompletion.campaign_application?.campaign?.description ||
                'Nema opisa'}
            </p>
          </div>

          {userType === 'business' ? (
            /* Business View - Show both participants */
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium">Influencer</Label>
                <div className="flex items-center gap-2">
                  <Avatar className="h-8 w-8">
                    <AvatarImage
                      src={jobCompletion.influencer_profile?.avatar_url || ''}
                    />
                    <AvatarFallback>
                      <User className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="text-sm font-medium">
                      {getDisplayName(jobCompletion.influencer_profile)}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      @{jobCompletion.influencer_profile?.username}
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Biznis</Label>
                <div className="flex items-center gap-2">
                  <Avatar className="h-8 w-8">
                    <AvatarImage
                      src={
                        jobCompletion.campaign_application?.campaign?.business
                          ?.profiles?.avatar_url ||
                        jobCompletion.direct_offer?.businesses?.profiles
                          ?.avatar_url ||
                        jobCompletion.business_profile?.avatar_url ||
                        ''
                      }
                    />
                    <AvatarFallback>
                      <User className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="text-sm font-medium">
                      {jobCompletion.campaign_application?.campaign?.business
                        ?.company_name ||
                        jobCompletion.direct_offer?.businesses?.company_name ||
                        getDisplayName(jobCompletion.business_profile) ||
                        'Business'}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      @
                      {jobCompletion.campaign_application?.campaign?.business
                        ?.profiles?.username ||
                        jobCompletion.direct_offer?.businesses?.profiles
                          ?.username ||
                        jobCompletion.business_profile?.username}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            /* Influencer View - Show only business */
            <div className="space-y-2">
              <Label className="text-sm font-medium">Biznis</Label>
              <div className="flex items-center gap-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage
                    src={
                      jobCompletion.campaign_application?.campaign?.business
                        ?.profiles?.avatar_url ||
                      jobCompletion.direct_offer?.businesses?.profiles
                        ?.avatar_url ||
                      jobCompletion.business_profile?.avatar_url ||
                      ''
                    }
                  />
                  <AvatarFallback>
                    <User className="h-4 w-4" />
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="text-sm font-medium">
                    {jobCompletion.campaign_application?.campaign?.business
                      ?.company_name ||
                      jobCompletion.direct_offer?.businesses?.company_name ||
                      getDisplayName(jobCompletion.business_profile) ||
                      'Business'}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    @
                    {jobCompletion.campaign_application?.campaign?.business
                      ?.profiles?.username ||
                      jobCompletion.direct_offer?.businesses?.profiles
                        ?.username ||
                      jobCompletion.business_profile?.username}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Submission Details */}
          {jobCompletion.submission_notes && (
            <div className="space-y-4">
              {(() => {
                try {
                  const submissionData = JSON.parse(
                    jobCompletion.submission_notes
                  );
                  return (
                    <>
                      {/* Post Links */}
                      {submissionData.post_links &&
                        submissionData.post_links.length > 0 && (
                          <div className="space-y-3">
                            <div className="flex items-center gap-2">
                              <ExternalLink className="h-4 w-4 text-blue-600" />
                              <Label className="text-sm font-medium">
                                Objavljeni postovi
                              </Label>
                            </div>
                            <div className="space-y-2">
                              {submissionData.post_links.map(
                                (link: string, index: number) => (
                                  <div
                                    key={index}
                                    className="bg-gradient-to-r from-blue-50 to-indigo-50 p-3 rounded-lg border border-blue-100"
                                  >
                                    <a
                                      href={link}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center gap-2 hover:underline"
                                    >
                                      <ExternalLink className="h-3 w-3" />
                                      {link}
                                    </a>
                                  </div>
                                )
                              )}
                            </div>
                          </div>
                        )}

                      {/* Optional Message */}
                      {submissionData.message && (
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <MessageCircle className="h-4 w-4 text-green-600" />
                            <Label className="text-sm font-medium">
                              Poruka influencera
                            </Label>
                          </div>
                          <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-3 rounded-lg border border-green-100">
                            <p className="text-sm text-gray-700">
                              {submissionData.message}
                            </p>
                          </div>
                        </div>
                      )}
                    </>
                  );
                } catch {
                  // Fallback for old format
                  return (
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">
                        Napomene o predaji
                      </Label>
                      <div className="bg-white/60 dark:bg-gray-800/40 p-3 rounded-lg border border-purple-100/50 dark:border-purple-800/30">
                        <p className="text-sm text-gray-700 dark:text-gray-300">
                          {jobCompletion.submission_notes}
                        </p>
                      </div>
                    </div>
                  );
                }
              })()}
            </div>
          )}

          {/* Review Notes */}
          {jobCompletion.review_notes && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">Napomene o pregledu</Label>
              <div className="bg-white/60 dark:bg-gray-800/40 p-3 rounded-lg border border-purple-100/50 dark:border-purple-800/30">
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  {jobCompletion.review_notes}
                </p>
              </div>
            </div>
          )}

          {/* Timestamps */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>Kreiran: {formatDate(jobCompletion.created_at)}</span>
            </div>
            {jobCompletion.submitted_at && (
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span>Poslan: {formatDate(jobCompletion.submitted_at)}</span>
              </div>
            )}
            {jobCompletion.reviewed_at && (
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span>Pregledan: {formatDate(jobCompletion.reviewed_at)}</span>
              </div>
            )}
          </div>

          {/* Ocjene */}
          {(jobCompletion.business_to_influencer_review ||
            jobCompletion.influencer_to_business_review) && (
            <div className="space-y-4 pt-4 border-t">
              <h4 className="font-medium text-sm">Ocjene</h4>

              {/* Ocjena biznisa za influencera */}
              {jobCompletion.business_to_influencer_review && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm font-medium">
                      Ocjena biznisa za influencera
                    </Label>
                    {renderStars(
                      jobCompletion.business_to_influencer_review.rating
                    )}
                  </div>
                  {jobCompletion.business_to_influencer_review.comment && (
                    <div className="bg-white/60 dark:bg-gray-800/40 p-3 rounded-lg border border-purple-100/50 dark:border-purple-800/30">
                      <p className="text-sm text-gray-700 dark:text-gray-300">
                        {jobCompletion.business_to_influencer_review.comment}
                      </p>
                    </div>
                  )}
                  <p className="text-xs text-muted-foreground">
                    {formatDate(
                      jobCompletion.business_to_influencer_review.created_at
                    )}
                  </p>
                </div>
              )}

              {/* Ocjena influencera za biznis */}
              {jobCompletion.influencer_to_business_review && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm font-medium">
                      Ocjena influencera za biznis
                    </Label>
                    {renderStars(
                      jobCompletion.influencer_to_business_review.rating
                    )}
                  </div>
                  {jobCompletion.influencer_to_business_review.comment && (
                    <div className="bg-white/60 dark:bg-gray-800/40 p-3 rounded-lg border border-purple-100/50 dark:border-purple-800/30">
                      <p className="text-sm text-gray-700 dark:text-gray-300">
                        {jobCompletion.influencer_to_business_review.comment}
                      </p>
                    </div>
                  )}
                  <p className="text-xs text-muted-foreground">
                    {formatDate(
                      jobCompletion.influencer_to_business_review.created_at
                    )}
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Review Actions */}
          {canReview && (
            <div className="space-y-4 pt-4 border-t">
              {!isReviewing ? (
                <Button onClick={() => setIsReviewing(true)} className="w-full">
                  Pregljedaj predaju
                </Button>
              ) : (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="review-notes">
                      Napomene o pregledu (opcionalno za odobravanje, obavezno
                      za odbacivanje)
                    </Label>
                    <Textarea
                      id="review-notes"
                      placeholder="Dodajte napomene o pregledu..."
                      value={reviewNotes}
                      onChange={e => setReviewNotes(e.target.value)}
                      rows={3}
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button
                      onClick={handleApprove}
                      disabled={isLoading}
                      className="flex-1"
                      variant="default"
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Odobri
                    </Button>
                    <Button
                      onClick={handleReject}
                      disabled={isLoading}
                      className="flex-1"
                      variant="destructive"
                    >
                      <XCircle className="h-4 w-4 mr-2" />
                      Odbaci
                    </Button>
                    <Button
                      onClick={() => {
                        setIsReviewing(false);
                        setReviewNotes('');
                      }}
                      disabled={isLoading}
                      variant="outline"
                    >
                      Otkaži
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </div>
    </Card>
  );
}
