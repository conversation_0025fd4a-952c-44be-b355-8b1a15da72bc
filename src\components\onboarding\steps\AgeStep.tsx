'use client';

import { Button } from '@/components/ui/button';
import { InfluencerDropdown } from '@/components/ui/influencer-dropdown';

interface AgeStepProps {
  value: number | null;
  onChange: (value: number) => void;
  onNext: () => void;
  onBack: () => void;
}

export function AgeStep({ value, onChange, onNext, onBack }: AgeStepProps) {
  // Generate age options from 13 to 100
  const ageOptions = Array.from({ length: 88 }, (_, i) => {
    const age = i + 13;
    return {
      value: age.toString(),
      label: `${age} ${age === 1 ? 'godina' : age < 5 ? 'godine' : 'godina'}`,
    };
  });

  const handleNext = () => {
    if (value && value >= 13) {
      onNext();
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-2">
          <PERSON><PERSON><PERSON> imate godina?
        </h2>
        <p className="text-white/70">
          Ova informacija pomaže brendovima da pronađu odgovarajuće influencere
        </p>
      </div>

      <div className="space-y-4">
        <InfluencerDropdown
          label="Godine"
          placeholder="Izaberite godine"
          value={value ? value.toString() : ''}
          onValueChange={val => onChange(parseInt(val))}
          options={ageOptions}
          maxHeight="max-h-60"
        />

        <div className="text-sm text-white/60">
          <p>Minimalne godine za korišćenje platforme su 13.</p>
        </div>
      </div>

      <div className="flex gap-3">
        <Button
          variant="outline"
          onClick={onBack}
          className="flex-1 border-white/30 text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm"
        >
          Nazad
        </Button>
        <Button
          onClick={handleNext}
          disabled={!value || value < 13}
          className="flex-1 bg-white text-instagram-purple hover:bg-white/90 font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
        >
          Dalje
        </Button>
      </div>
    </div>
  );
}
