'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { ShoppingCart, X, Check } from 'lucide-react';
import { toast } from 'sonner';
import { createPackageOrder } from '@/lib/offers';
import { GradientCard } from '@/components/ui/gradient-card';

interface GradientPackageOrderModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  packageData: {
    id: number;
    platform_id: number;
    platform_name: string;
    platform_icon: string;
    content_type_id: number;
    content_type_name: string;
    price: number;
    currency: string;
    quantity?: number;
    video_duration?: string | null;
    auto_generated_name: string;
  };
  influencerId: string;
  influencerName: string;
}

export function GradientPackageOrderModal({
  isOpen,
  onClose,
  onSuccess,
  packageData,
  influencerId,
  influencerName,
}: GradientPackageOrderModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [requirements, setRequirements] = useState('');
  const [message, setMessage] = useState('');

  const handleConfirmOrder = async () => {
    try {
      setIsLoading(true);

      const orderData = {
        influencer_id: influencerId,
        package_id: packageData.id,
        package_name: packageData.auto_generated_name,
        price: packageData.price,
        platform_name: packageData.platform_name,
        content_type_name: packageData.content_type_name,
        requirements: requirements.trim() || undefined,
        message: message.trim() || undefined,
      };

      const { error } = await createPackageOrder(orderData);

      if (error) {
        toast.error('Greška pri kreiranju narudžbe');
        return;
      }

      toast.success('Narudžba je uspješno poslana!');
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error creating package order:', error);
      toast.error('Neočekivana greška');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-h-[90vh] overflow-y-auto bg-instagram-secondary border-white/20">
        {/* Background decorative elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/5 pointer-events-none"></div>
        <div className="absolute top-5 right-5 w-32 h-32 bg-white/5 rounded-full blur-2xl pointer-events-none"></div>
        <div className="absolute bottom-5 left-5 w-40 h-40 bg-white/5 rounded-full blur-2xl pointer-events-none"></div>

        <DialogHeader className="relative z-10">
          <DialogTitle className="flex items-center gap-2 text-white">
            <ShoppingCart className="h-5 w-5" />
            Potvrda narudžbe
          </DialogTitle>
          <DialogDescription className="text-white/80">
            Molimo potvrdite detalje vaše narudžbe
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-2 relative z-10">
          {/* Order Summary */}
          <GradientCard variant="subtle" glassEffect={true} className="p-4">
            <h3 className="text-sm font-medium text-white mb-3">
              Sažetak narudžbe
            </h3>

            <div className="space-y-3">
              {/* Influencer */}
              <div className="flex items-center justify-between">
                <span className="text-sm text-white/70">Influencer:</span>
                <span className="font-medium text-sm text-white">
                  {influencerName}
                </span>
              </div>

              {/* Package */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="text-base">{packageData.platform_icon}</span>
                  <Badge
                    variant="secondary"
                    className="text-xs bg-white/20 text-white border-white/30"
                  >
                    {packageData.platform_name}
                  </Badge>
                </div>

                <div>
                  <p className="font-medium text-sm text-white">
                    {packageData.auto_generated_name}
                  </p>
                  <p className="text-xs text-white/70">
                    {packageData.content_type_name}
                    {packageData.quantity &&
                      packageData.quantity > 1 &&
                      ` • ${packageData.quantity}x`}
                    {packageData.video_duration &&
                      ` • ${packageData.video_duration}`}
                  </p>
                </div>
              </div>

              <hr className="my-2 border-white/20" />

              <div className="flex justify-between items-center">
                <span className="font-medium text-white">Ukupno:</span>
                <span className="text-lg font-bold text-white">
                  {packageData.price} {packageData.currency}
                </span>
              </div>
            </div>
          </GradientCard>

          {/* Requirements and Message */}
          <div className="space-y-3">
            <div>
              <Label
                htmlFor="requirements"
                className="text-sm font-medium text-white"
              >
                Specifični zahtjevi (opcionalno)
              </Label>
              <Textarea
                id="requirements"
                placeholder="Opišite specifične zahtjeve za sadržaj..."
                value={requirements}
                onChange={e => setRequirements(e.target.value)}
                className="mt-1 text-sm bg-white/10 border-white/20 text-white placeholder-white/60 focus:border-white/40"
                rows={2}
              />
            </div>

            <div>
              <Label
                htmlFor="message"
                className="text-sm font-medium text-white"
              >
                Poruka (opcionalno)
              </Label>
              <Textarea
                id="message"
                placeholder="Dodatna poruka za influencera..."
                value={message}
                onChange={e => setMessage(e.target.value)}
                className="mt-1 text-sm bg-white/10 border-white/20 text-white placeholder-white/60 focus:border-white/40"
                rows={2}
              />
            </div>
          </div>

          {/* Info message */}
          <div className="bg-white/10 border border-white/20 p-3 rounded-md">
            <p className="text-xs text-white/80">
              Narudžba će biti poslana influenceru na razmatranje.
            </p>
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex gap-3 pt-3 relative z-10">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
            className="flex-1 bg-white/10 border-white/20 text-white hover:bg-white/20"
          >
            <X className="mr-2 h-4 w-4" />
            Otkaži
          </Button>
          <Button
            onClick={handleConfirmOrder}
            disabled={isLoading}
            className="flex-1 bg-white text-purple-600 hover:bg-white/90 font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600 mr-2"></div>
                Šalje se...
              </>
            ) : (
              <>
                <Check className="mr-2 h-4 w-4" />
                Potvrdi
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
