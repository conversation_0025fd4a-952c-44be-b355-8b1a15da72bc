'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { getBusiness } from '@/lib/profiles';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Loader2, Crown } from 'lucide-react';

export default function BiznisDashboardPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [business, setBusiness] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadBusiness();
    }
  }, [user]);

  const loadBusiness = async () => {
    try {
      setLoading(true);
      const { data, error } = await getBusiness(user!.id);

      if (error || !data) {
        // Business profile doesn't exist, redirect to profile creation
        router.push('/profil/kreiranje/biznis');
        return;
      }

      // Check subscription status from user_subscriptions table
      const { supabase } = await import('@/lib/supabase');
      const { data: subscription } = await supabase
        .from('user_subscriptions')
        .select('status')
        .eq('user_id', user!.id)
        .eq('user_type', 'business')
        .eq('status', 'active')
        .single();

      setBusiness({ ...data, hasActiveSubscription: !!subscription });
    } catch (err) {
      console.error('Error loading business data:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout requiredUserType="business">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  const isPremium = business?.hasActiveSubscription || false;

  return (
    <DashboardLayout requiredUserType="business">
      <div>
        <div className="flex items-center gap-3 mb-4">
          <h1 className="text-3xl font-bold text-foreground">
            Dobrodošli,{' '}
            {business?.company_name || business?.profiles?.username || 'Biznis'}
            !
          </h1>
          <div
            className={`flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium ${
              isPremium
                ? 'bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-700 border border-yellow-200'
                : 'bg-gray-100 text-gray-700 border border-gray-200'
            }`}
          >
            {isPremium && <Crown className="h-4 w-4" />}
            {isPremium ? 'Premium' : 'Free'} korisnik
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
