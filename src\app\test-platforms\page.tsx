'use client';

import { useState } from 'react';
import { PlatformSelector } from '@/components/ui/platform-selector';
import { PricingMatrix } from '@/components/ui/pricing-matrix';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

interface SelectedPlatform {
  platform_id: number;
  handle: string;
  followers_count: number;
  content_type_ids: number[];
}

interface PricingEntry {
  platform_id: number;
  content_type_id: number;
  price: number;
  is_available: boolean;
}

export default function TestPlatformsPage() {
  const [selectedPlatforms, setSelectedPlatforms] = useState<
    SelectedPlatform[]
  >([]);
  const [pricing, setPricing] = useState<PricingEntry[]>([]);

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="container mx-auto max-w-4xl space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold">Test Platformi i Cijena</h1>
          <p className="text-muted-foreground">
            Testiranje PlatformSelector i PricingMatrix komponenti
          </p>
        </div>

        <div className="space-y-8">
          {/* Platform Selector */}
          <Card>
            <CardHeader>
              <CardTitle>Platform Selector</CardTitle>
              <CardDescription>
                Izaberite platforme, unesite handle/pratioce i izaberite content
                tipove
              </CardDescription>
            </CardHeader>
            <CardContent>
              <PlatformSelector
                selectedPlatforms={selectedPlatforms}
                onPlatformsChange={setSelectedPlatforms}
              />
            </CardContent>
          </Card>

          {/* Pricing Matrix */}
          <Card>
            <CardHeader>
              <CardTitle>Pricing Matrix</CardTitle>
              <CardDescription>
                Postavite cijene za izabrane content tipove
              </CardDescription>
            </CardHeader>
            <CardContent>
              <PricingMatrix
                selectedPlatforms={selectedPlatforms}
                pricing={pricing}
                onPricingChange={setPricing}
              />
            </CardContent>
          </Card>

          {/* Debug Info */}
          <Card>
            <CardHeader>
              <CardTitle>Debug Informacije</CardTitle>
              <CardDescription>JSON prikaz trenutnih podataka</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Selected Platforms:</h4>
                <pre className="text-xs bg-muted p-3 rounded-lg overflow-auto">
                  {JSON.stringify(selectedPlatforms, null, 2)}
                </pre>
              </div>

              <div>
                <h4 className="font-medium mb-2">Pricing:</h4>
                <pre className="text-xs bg-muted p-3 rounded-lg overflow-auto">
                  {JSON.stringify(pricing, null, 2)}
                </pre>
              </div>
            </CardContent>
          </Card>

          {/* Usage Instructions */}
          <Card>
            <CardHeader>
              <CardTitle>Kako koristiti</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 text-sm">
                <div>
                  <h4 className="font-medium">PlatformSelector:</h4>
                  <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                    <li>Kliknite na platformu da je dodate</li>
                    <li>
                      Kliknite na karticu platforme da je otvorite/zatvorite
                    </li>
                    <li>Unesite handle i broj pratilaca</li>
                    <li>Izaberite content tipove koje nudite</li>
                    <li>Kliknite X da uklonite platformu</li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium">PricingMatrix:</h4>
                  <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                    <li>Automatski se pojavljuje za izabrane content tipove</li>
                    <li>Unesite cijenu u KM za svaki content tip</li>
                    <li>
                      Kliknite "Dostupno/Nedostupno" da promijenite status
                    </li>
                    <li>
                      Pregled cijena prikazuje samo dostupne tipove sa cijenom
                    </li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium">Workflow:</h4>
                  <ol className="list-decimal list-inside space-y-1 text-muted-foreground">
                    <li>Izaberite platforme (Instagram, TikTok, YouTube)</li>
                    <li>Za svaku platformu unesite handle i pratioce</li>
                    <li>
                      Izaberite content tipove (Photo Post, Reel, Video, itd.)
                    </li>
                    <li>Postavite cijene za svaki content tip</li>
                    <li>Označite koje tipove nudite (Dostupno/Nedostupno)</li>
                  </ol>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
