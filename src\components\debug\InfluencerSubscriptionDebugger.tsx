'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Crown, User, RotateCcw } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

export function InfluencerSubscriptionDebugger() {
  const [subscriptionType, setSubscriptionType] = useState<'free' | 'premium'>(
    'free'
  );
  const [customOffersEnabled, setCustomOffersEnabled] = useState(false);
  const [loading, setLoading] = useState(false);
  const { user } = useAuth();

  // Only show in development mode
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  useEffect(() => {
    loadCurrentSubscription();
  }, [user]);

  const loadCurrentSubscription = async () => {
    if (!user) return;

    try {
      const { supabase } = await import('@/lib/supabase');
      const { data } = await supabase
        .from('influencers')
        .select('subscription_type, custom_offers_enabled')
        .eq('id', user.id)
        .single();

      if (data) {
        setSubscriptionType(
          (data.subscription_type as 'free' | 'premium') || 'free'
        );
        setCustomOffersEnabled(data.custom_offers_enabled || false);
      }
    } catch (error) {
      console.error('Error loading subscription:', error);
    }
  };

  const toggleSubscription = async (newType: 'free' | 'premium') => {
    if (!user || loading) return;

    setLoading(true);
    try {
      const { supabase } = await import('@/lib/supabase');

      const { error } = await supabase
        .from('influencers')
        .update({ subscription_type: newType })
        .eq('id', user.id);

      if (!error) {
        setSubscriptionType(newType);
        alert(
          `🎉 Uspješno promenjeno na ${newType.toUpperCase()} plan! Refresh stranicu.`
        );
        window.location.reload();
      } else {
        alert('❌ Greška pri promeni subscription type-a');
      }
    } catch (error) {
      console.error('Error toggling subscription:', error);
      alert('❌ Greška pri promeni subscription type-a');
    } finally {
      setLoading(false);
    }
  };

  const toggleCustomOffers = async () => {
    if (!user || loading) return;

    setLoading(true);
    try {
      const { supabase } = await import('@/lib/supabase');

      const newValue = !customOffersEnabled;
      const { error } = await supabase
        .from('influencers')
        .update({ custom_offers_enabled: newValue })
        .eq('id', user.id);

      if (!error) {
        setCustomOffersEnabled(newValue);
        alert(
          `🎉 Custom ponude ${newValue ? 'aktivirane' : 'deaktivirane'}! Refresh stranicu.`
        );
        window.location.reload();
      } else {
        alert('❌ Greška pri promeni custom offers setting-a');
      }
    } catch (error) {
      console.error('Error toggling custom offers:', error);
      alert('❌ Greška pri promeni custom offers setting-a');
    } finally {
      setLoading(false);
    }
  };

  if (!user) return null;

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-4 z-50">
      <div className="text-xs text-gray-500 mb-2">🔧 Debug Mode (Influencer)</div>
      
      {/* Subscription Type */}
      <div className="flex items-center gap-2 mb-3">
        <div className="flex items-center gap-1">
          {subscriptionType === 'premium' ? (
            <Crown className="h-4 w-4 text-yellow-500" />
          ) : (
            <User className="h-4 w-4 text-gray-500" />
          )}
          <span className="text-sm font-medium">
            {subscriptionType.toUpperCase()}
          </span>
        </div>
      </div>

      <div className="flex gap-2 mb-3">
        <Button
          size="sm"
          variant={subscriptionType === 'free' ? 'default' : 'outline'}
          onClick={() => toggleSubscription('free')}
          disabled={loading || subscriptionType === 'free'}
          className="text-xs"
        >
          <User className="h-3 w-3 mr-1" />
          Free
        </Button>
        <Button
          size="sm"
          variant={subscriptionType === 'premium' ? 'default' : 'outline'}
          onClick={() => toggleSubscription('premium')}
          disabled={loading || subscriptionType === 'premium'}
          className="text-xs bg-yellow-500 hover:bg-yellow-600"
        >
          <Crown className="h-3 w-3 mr-1" />
          Premium
        </Button>
      </div>

      {/* Custom Offers Toggle */}
      <div className="border-t pt-2 mb-2">
        <div className="text-xs text-gray-600 mb-1">Custom Ponude</div>
        <Button
          size="sm"
          variant={customOffersEnabled ? 'default' : 'outline'}
          onClick={toggleCustomOffers}
          disabled={loading}
          className="text-xs w-full"
        >
          {customOffersEnabled ? '✅ Uključeno' : '❌ Isključeno'}
        </Button>
      </div>

      <Button
        size="sm"
        variant="ghost"
        onClick={() => window.location.reload()}
        className="text-xs w-full"
      >
        <RotateCcw className="h-3 w-3 mr-1" />
        Refresh
      </Button>
    </div>
  );
}