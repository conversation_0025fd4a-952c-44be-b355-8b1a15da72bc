'use client';

import { useEffect, useRef, useCallback, useState } from 'react';
import { Loader2 } from 'lucide-react';

interface InfiniteScrollProps<T> {
  data: T[];
  hasMore: boolean;
  isLoading: boolean;
  isLoadingMore: boolean;
  loadMore: () => void;
  threshold?: number;
  children: React.ReactNode;
  className?: string;
  cacheKey?: string; // Za cache-iranje scroll pozicije
  onScrollPosition?: (position: number) => void;
}

export function InfiniteScroll<T>({
  data,
  hasMore,
  isLoading,
  isLoadingMore,
  loadMore,
  threshold = 200,
  children,
  className = '',
  cacheKey,
  onScrollPosition,
}: InfiniteScrollProps<T>) {
  const observerTarget = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [hasRestored, setHasRestored] = useState(false);

  // Intersection Observer za auto-loading
  const handleObserver = useCallback(
    (entries: IntersectionObserverEntry[]) => {
      const [target] = entries;
      if (target.isIntersecting && hasMore && !isLoading && !isLoadingMore) {
        loadMore();
      }
    },
    [hasMore, isLoading, isLoadingMore, loadMore]
  );

  // Setup Intersection Observer
  useEffect(() => {
    const element = observerTarget.current;
    if (!element) return;

    const observer = new IntersectionObserver(handleObserver, {
      threshold: 0,
      rootMargin: `${threshold}px`,
    });

    observer.observe(element);
    return () => observer.disconnect();
  }, [handleObserver, threshold]);

  // Cache scroll pozicije - koristimo window scroll
  const saveScrollPosition = useCallback(() => {
    if (!cacheKey) return;

    const scrollTop = window.scrollY || window.pageYOffset;
    sessionStorage.setItem(`scroll-${cacheKey}`, scrollTop.toString());
    onScrollPosition?.(scrollTop);
  }, [cacheKey, onScrollPosition]);

  // Restore scroll pozicije - koristimo window scroll umesto container
  const restoreScrollPosition = useCallback(() => {
    if (!cacheKey || hasRestored) return;

    const savedPosition = sessionStorage.getItem(`scroll-${cacheKey}`);
    if (savedPosition && parseInt(savedPosition) > 0) {
      console.log(`[Cache] Restoring scroll position: ${savedPosition}px`);
      // Čekamo da se podaci renderuju
      setTimeout(() => {
        window.scrollTo({
          top: parseInt(savedPosition),
          behavior: 'auto', // Instant restore
        });
        setHasRestored(true);
      }, 200); // Duže čekanje za rendering
    } else {
      setHasRestored(true);
    }
  }, [cacheKey, hasRestored]);

  // Restore scroll kada se data učitaju
  useEffect(() => {
    if (data.length > 0 && !isLoading) {
      restoreScrollPosition();
    }
  }, [data.length, isLoading, restoreScrollPosition]);

  // Save poziciju na scroll - koristimo window scroll
  useEffect(() => {
    if (!cacheKey) return;

    const handleScroll = () => {
      saveScrollPosition();
    };

    // Throttle scroll events
    let scrollTimeout: NodeJS.Timeout;
    const throttledScroll = () => {
      if (scrollTimeout) clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(handleScroll, 100);
    };

    window.addEventListener('scroll', throttledScroll);
    return () => {
      window.removeEventListener('scroll', throttledScroll);
      if (scrollTimeout) clearTimeout(scrollTimeout);
    };
  }, [cacheKey, saveScrollPosition]);

  return (
    <div ref={containerRef} className={className}>
      {children}

      {/* Loading trigger element */}
      <div ref={observerTarget} className="h-4" />

      {/* Loading states */}
      {isLoadingMore && hasMore && (
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-6 w-6 animate-spin text-primary" />
          <span className="ml-2 text-sm text-muted-foreground">
            Učitava još sadržaja...
          </span>
        </div>
      )}

      {/* End message */}
      {!hasMore && data.length > 0 && (
        <div className="text-center py-8">
          <p className="text-sm text-muted-foreground">
            To je sve! Prikazano je {data.length} rezultata.
          </p>
        </div>
      )}

      {/* Initial loading skeleton */}
      {isLoading && data.length === 0 && (
        <div className="space-y-4">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="bg-gray-200 rounded-lg h-32"></div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

// Cache utility funkcije
export const InfiniteScrollCache = {
  // Čuva filtere u sessionStorage
  saveFilters: (cacheKey: string, filters: any) => {
    sessionStorage.setItem(`filters-${cacheKey}`, JSON.stringify(filters));
  },

  // Učitava filtere iz sessionStorage
  loadFilters: (cacheKey: string) => {
    const saved = sessionStorage.getItem(`filters-${cacheKey}`);
    return saved ? JSON.parse(saved) : null;
  },

  // Čuva podatke u sessionStorage (za back navigaciju)
  saveData: (cacheKey: string, data: any[]) => {
    // Limitiramo na poslednih 100 items da ne zatrpamo memoriju
    const limitedData = data.slice(-100);
    sessionStorage.setItem(`data-${cacheKey}`, JSON.stringify(limitedData));
    sessionStorage.setItem(`timestamp-${cacheKey}`, Date.now().toString());
  },

  // Učitava podatke iz sessionStorage
  loadData: (cacheKey: string) => {
    const saved = sessionStorage.getItem(`data-${cacheKey}`);
    const timestamp = sessionStorage.getItem(`timestamp-${cacheKey}`);

    if (saved && timestamp) {
      const cacheAge = Date.now() - parseInt(timestamp);
      // Cache expires after 5 minutes for marketplace data
      if (cacheAge > 5 * 60 * 1000) {
        InfiniteScrollCache.clearCache(cacheKey);
        return [];
      }
      return JSON.parse(saved);
    }
    return [];
  },

  // Čisti cache (npr. kada se promene filteri)
  clearCache: (cacheKey: string) => {
    sessionStorage.removeItem(`scroll-${cacheKey}`);
    sessionStorage.removeItem(`filters-${cacheKey}`);
    sessionStorage.removeItem(`data-${cacheKey}`);
    sessionStorage.removeItem(`timestamp-${cacheKey}`);
  },
};
