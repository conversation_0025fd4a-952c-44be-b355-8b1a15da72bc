'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Star, User, Send } from 'lucide-react';
import { createJobReview } from '@/lib/job-reviews';
import { toast } from 'sonner';

interface CreateReviewFormProps {
  jobCompletionId: string;
  revieweeId: string;
  revieweeName: string;
  revieweeUsername: string;
  revieweeAvatar?: string | null;
  reviewType: 'influencer_to_business' | 'business_to_influencer';
  campaignTitle?: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function CreateReviewForm({
  jobCompletionId,
  revieweeId,
  revieweeName,
  revieweeUsername,
  revieweeAvatar,
  reviewType,
  campaignTitle,
  onSuccess,
  onCancel,
}: CreateReviewFormProps) {
  const [rating, setRating] = useState<number>(0);
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hoveredStar, setHoveredStar] = useState<number>(0);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (rating === 0) {
      toast.error('Please select a rating');
      return;
    }

    if (!comment.trim()) {
      toast.error('Please add a comment');
      return;
    }

    setIsSubmitting(true);
    try {
      const { error } = await createJobReview(
        jobCompletionId,
        revieweeId,
        rating,
        comment.trim(),
        reviewType
      );

      if (error) {
        toast.error('Failed to submit review');
        return;
      }

      toast.success('Review submitted successfully');
      onSuccess?.();
    } catch (error) {
      toast.error('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getReviewTypeLabel = () => {
    switch (reviewType) {
      case 'influencer_to_business':
        return 'Review Business';
      case 'business_to_influencer':
        return 'Review Influencer';
      default:
        return 'Write Review';
    }
  };

  const renderStarRating = () => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map(star => (
          <button
            key={star}
            type="button"
            className="focus:outline-none"
            onMouseEnter={() => setHoveredStar(star)}
            onMouseLeave={() => setHoveredStar(0)}
            onClick={() => setRating(star)}
          >
            <Star
              className={`h-6 w-6 transition-colors ${
                star <= (hoveredStar || rating)
                  ? 'fill-yellow-400 text-yellow-400'
                  : 'text-gray-300 hover:text-yellow-200'
              }`}
            />
          </button>
        ))}
        {rating > 0 && (
          <span className="ml-2 text-sm font-medium">{rating}/5</span>
        )}
      </div>
    );
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-lg">{getReviewTypeLabel()}</CardTitle>
        {campaignTitle && (
          <p className="text-sm text-muted-foreground">
            Campaign: {campaignTitle}
          </p>
        )}
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Reviewee Info */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Reviewing</Label>
            <div className="flex items-center gap-3 p-3 bg-muted rounded-md">
              <Avatar className="h-10 w-10">
                <AvatarImage src={revieweeAvatar || ''} />
                <AvatarFallback>
                  <User className="h-5 w-5" />
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium text-sm">{revieweeName}</p>
                <p className="text-xs text-muted-foreground">
                  @{revieweeUsername}
                </p>
              </div>
            </div>
          </div>

          {/* Rating */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Rating *</Label>
            <div className="flex items-center gap-2">{renderStarRating()}</div>
            <p className="text-xs text-muted-foreground">
              Click on the stars to rate your experience
            </p>
          </div>

          {/* Comment */}
          <div className="space-y-2">
            <Label htmlFor="comment" className="text-sm font-medium">
              Comment *
            </Label>
            <Textarea
              id="comment"
              placeholder="Share your experience working together..."
              value={comment}
              onChange={e => setComment(e.target.value)}
              rows={4}
              className="resize-none"
            />
            <p className="text-xs text-muted-foreground">
              {comment.length}/500 characters
            </p>
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              disabled={isSubmitting || rating === 0 || !comment.trim()}
              className="flex-1"
            >
              <Send className="h-4 w-4 mr-2" />
              {isSubmitting ? 'Submitting...' : 'Submit Review'}
            </Button>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
