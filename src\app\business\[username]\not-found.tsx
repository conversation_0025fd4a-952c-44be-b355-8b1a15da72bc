import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Building, Home } from 'lucide-react';
import Link from 'next/link';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
      <Card className="max-w-md w-full">
        <CardContent className="p-8 text-center">
          <Building className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold mb-2">Biznis nije pronađen</h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Traženi biznis profil ne postoji ili nije dostupan.
          </p>
          <Button asChild>
            <Link href="/dashboard">
              <Home className="h-4 w-4 mr-2" />
              Povratak na početnu
            </Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
