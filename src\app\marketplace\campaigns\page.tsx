'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import {
  searchCampaignsCards,
  getFeaturedCampaigns,
} from '@/lib/campaigns';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  Loader2,
  Star,
  TrendingUp,
} from 'lucide-react';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import MarketplaceCampaignCard from '@/components/campaigns/MarketplaceCampaignCard';
import { InfiniteScroll } from '@/components/ui/infinite-scroll';
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll';

interface Platform {
  name: string;
  icon: string;
  content_types: string[];
}

interface Campaign {
  id: string;
  title: string;
  description: string;
  budget: number | null;
  status: string;
  location: string | null;
  application_deadline: string | null;
  min_followers: number | null;
  max_followers: number | null;
  age_range_min: number | null;
  age_range_max: number | null;
  gender: string | null;
  is_featured: boolean;
  created_at: string;
  business_id: string;
  content_types: string[] | null;
  platforms?: Platform[];
  businesses?: {
    company_name: string;
    industry: string;
  };
}

const CACHE_KEY = 'marketplace-campaigns';
const ITEMS_PER_PAGE = 15;

export default function CampaignsMarketplacePage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();

  // Featured campaigns (loaded once)
  const [featuredCampaigns, setFeaturedCampaigns] = useState<Campaign[]>([]);
  const [loadingFeatured, setLoadingFeatured] = useState(true);

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/prijava');
      return;
    }
  }, [user, authLoading, router]);

  // Function to fetch campaigns for infinite scroll
  const fetchCampaigns = useCallback(
    async (offset: number, limit: number) => {
      try {
        const { data, error } = await searchCampaignsCards({
          limit,
          offset,
        });

        if (error) {
          console.error('Error loading campaigns:', error);
          throw error;
        }

        const campaigns = data || [];
        const hasMore = campaigns.length === limit;

        return {
          data: campaigns,
          hasMore,
          error: null,
        };
      } catch (error) {
        return {
          data: [],
          hasMore: false,
          error,
        };
      }
    },
    []
  );

  // Infinite scroll hook
  const {
    data: campaigns,
    isLoading,
    isLoadingMore,
    hasMore,
    error,
    actions,
  } = useInfiniteScroll<Campaign>({
    fetchData: fetchCampaigns,
    limit: ITEMS_PER_PAGE,
    cacheKey: CACHE_KEY,
    dependencies: [],
  });

  // Load featured campaigns once
  useEffect(() => {
    if (user) {
      loadFeaturedCampaigns();
    }
  }, [user]);

  const loadFeaturedCampaigns = async () => {
    try {
      setLoadingFeatured(true);
      const { data, error } = await getFeaturedCampaigns(6);

      if (error) {
        console.error('Error loading featured campaigns:', error);
        return;
      }

      setFeaturedCampaigns(data || []);
    } catch (error) {
      console.error('Error loading featured campaigns:', error);
    } finally {
      setLoadingFeatured(false);
    }
  };



  if (authLoading) {
    return (
      <DashboardLayout requiredUserType="influencer">
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <DashboardLayout requiredUserType="influencer">
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl md:text-3xl font-bold text-foreground">
            Kampanje
          </h1>
          <p className="text-muted-foreground mt-1">
            Pronađite savršene kampanje za vašu publiku
          </p>
        </div>

        {/* Main Content */}
        <div className="space-y-8">
          {/* Featured Campaigns */}
          {!loadingFeatured && featuredCampaigns.length > 0 && (
            <div>
              <div className="flex items-center gap-2 mb-4">
                <Star className="h-5 w-5 text-yellow-500" />
                <h2 className="text-xl font-semibold">Premium kampanje</h2>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4" style={{gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))'}}>
                {featuredCampaigns.map(campaign => (
                  <MarketplaceCampaignCard
                    key={campaign.id}
                    campaign={campaign}
                    isPremium={true}
                  />
                ))}
              </div>
              <Separator className="my-8" />
            </div>
          )}

          {/* Error state */}
          {error && !isLoading && (
            <Card className="border-destructive">
              <CardContent className="p-6">
                <p className="text-destructive">
                  Greška pri učitavanju kampanja. Pokušajte ponovo.
                </p>
                <button
                  onClick={actions.refresh}
                  className="mt-2 text-sm text-primary hover:underline"
                >
                  Pokušaj ponovo
                </button>
              </CardContent>
            </Card>
          )}

          {/* Infinite Scroll Campaigns */}
          <div>
            <InfiniteScroll
              data={campaigns}
              hasMore={hasMore}
              isLoading={isLoading}
              isLoadingMore={isLoadingMore}
              loadMore={actions.loadMore}
              cacheKey={CACHE_KEY}
              threshold={300}
              className="space-y-6"
            >
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4" style={{gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))'}}>
                {campaigns.map((campaign, index) => (
                  <MarketplaceCampaignCard
                    key={`${campaign.id}-${index}`}
                    campaign={campaign}
                  />
                ))}
              </div>
            </InfiniteScroll>
          </div>

          {/* Empty state */}
          {!isLoading && campaigns.length === 0 && !error && (
            <Card>
              <CardContent className="py-12 text-center">
                <TrendingUp className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Nema kampanja</h3>
                <p className="text-muted-foreground mb-4">
                  Trenutno nema kampanja koje odgovaraju vašim kriterijima.
                </p>
                <button
                  onClick={actions.refresh}
                  className="text-primary hover:underline"
                >
                  Osvježi stranicu
                </button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
