'use client';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';

interface DropdownOption {
  value: string;
  label: string;
  icon?: string;
}

interface InfluencerDropdownProps {
  label: string;
  placeholder: string;
  value: string;
  onValueChange: (value: string) => void;
  options: DropdownOption[];
  disabled?: boolean;
  error?: string;
  maxHeight?: string;
  className?: string;
}

export function InfluencerDropdown({
  label,
  placeholder,
  value,
  onValueChange,
  options,
  disabled = false,
  error,
  maxHeight = 'max-h-60',
  className = '',
}: InfluencerDropdownProps) {
  return (
    <div className={`space-y-2 ${className}`}>
      <Label className="text-white">{label}</Label>
      <Select value={value} onValueChange={onValueChange} disabled={disabled}>
        <SelectTrigger
          className={`bg-white/10 border-white/30 text-white focus:border-white/50 focus:ring-white/20 ${error ? 'border-red-400' : ''}`}
        >
          <SelectValue placeholder={placeholder} className="text-white/50" />
        </SelectTrigger>
        <SelectContent className={`${maxHeight} glass-instagram shadow-2xl`}>
          {options.map(option => (
            <SelectItem
              key={option.value}
              value={option.value}
              className="text-white hover:bg-white/20 focus:bg-white/20 cursor-pointer"
            >
              <div className="flex items-center space-x-2">
                {option.icon && <span>{option.icon}</span>}
                <span>{option.label}</span>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {error && <p className="text-sm text-red-300">{error}</p>}
    </div>
  );
}
