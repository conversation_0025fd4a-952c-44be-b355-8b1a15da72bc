// Test script za kampanje RPC funkciju
// Run: node test-campaigns-rpc.js

import { createClient } from '@supabase/supabase-js';

// Supabase konfiguracija
const supabaseUrl = 'https://oodvnbbcahdxfhrcutmt.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9vZHZuYmJjYWhkeGZocmN1dG10Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjU0NDY4ODQsImV4cCI6MjA0MTAyMjg4NH0.KAg2mHa8cD6CiJlLYVD8bxmr2UDkMU9dKqSdFYJ0CeQ';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testCampaignsRPC() {
  console.log('🧪 Testiram kampanje RPC funkciju...\n');

  try {
    // Test 1: Osnovni poziv
    console.log('📋 Test 1: Osnovni poziv za sve kampanje');
    const { data: basicTest, error: basicError } = await supabase.rpc(
      'get_campaigns_paginated',
      {
        p_search_query: null,
        p_categories: null,
        p_platforms: null,
        p_min_budget: null,
        p_max_budget: null,
        p_location: null,
        p_min_followers: null,
        p_max_followers: null,
        p_gender: null,
        p_deadline_before: null,
        p_sort_by: 'created_at',
        p_sort_order: 'desc',
        p_limit: 5,
        p_offset: 0,
        p_featured_only: false,
      }
    );

    if (basicError) {
      console.error('❌ Greška:', basicError);
    } else {
      console.log(`✅ Pronaađeno ${basicTest?.length || 0} kampanja`);
      if (basicTest && basicTest.length > 0) {
        const first = basicTest[0];
        console.log(`   - Primera: "${first.title}" (Budget: $${first.budget})`);
        console.log(`   - Platforme: ${JSON.stringify(first.platforms, null, 2)}`);
        console.log(`   - Kategorije: ${JSON.stringify(first.categories, null, 2)}`);
      }
    }

    console.log('');

    // Test 2: Search
    console.log('🔍 Test 2: Pretraga po ključnoj reči');
    const { data: searchTest, error: searchError } = await supabase.rpc(
      'get_campaigns_paginated',
      {
        p_search_query: 'marketing',
        p_categories: null,
        p_platforms: null,
        p_min_budget: null,
        p_max_budget: null,
        p_location: null,
        p_min_followers: null,
        p_max_followers: null,
        p_gender: null,
        p_deadline_before: null,
        p_sort_by: 'created_at',
        p_sort_order: 'desc',
        p_limit: 5,
        p_offset: 0,
        p_featured_only: false,
      }
    );

    if (searchError) {
      console.error('❌ Greška:', searchError);
    } else {
      console.log(`✅ Pronaađeno ${searchTest?.length || 0} kampanja za "marketing"`);
    }

    console.log('');

    // Test 3: Featured kampanje
    console.log('⭐ Test 3: Featured kampanje');
    const { data: featuredTest, error: featuredError } = await supabase.rpc(
      'get_campaigns_paginated',
      {
        p_search_query: null,
        p_categories: null,
        p_platforms: null,
        p_min_budget: null,
        p_max_budget: null,
        p_location: null,
        p_min_followers: null,
        p_max_followers: null,
        p_gender: null,
        p_deadline_before: null,
        p_sort_by: 'created_at',
        p_sort_order: 'desc',
        p_limit: 6,
        p_offset: 0,
        p_featured_only: true,
      }
    );

    if (featuredError) {
      console.error('❌ Greška:', featuredError);
    } else {
      console.log(`✅ Pronaađeno ${featuredTest?.length || 0} featured kampanja`);
    }

    console.log('');

    // Test 4: Filteri po budžetu
    console.log('💰 Test 4: Filteri po budžetu (100-500)');
    const { data: budgetTest, error: budgetError } = await supabase.rpc(
      'get_campaigns_paginated',
      {
        p_search_query: null,
        p_categories: null,
        p_platforms: null,
        p_min_budget: 100,
        p_max_budget: 500,
        p_location: null,
        p_min_followers: null,
        p_max_followers: null,
        p_gender: null,
        p_deadline_before: null,
        p_sort_by: 'budget',
        p_sort_order: 'desc',
        p_limit: 5,
        p_offset: 0,
        p_featured_only: false,
      }
    );

    if (budgetError) {
      console.error('❌ Greška:', budgetError);
    } else {
      console.log(`✅ Pronaađeno ${budgetTest?.length || 0} kampanja u rangu $100-$500`);
      budgetTest?.forEach(camp => {
        console.log(`   - ${camp.title}: $${camp.budget}`);
      });
    }

    console.log('');

    // Test performansi
    console.log('⚡ Test 5: Test performansi');
    const startTime = Date.now();
    
    const { data: perfTest, error: perfError } = await supabase.rpc(
      'get_campaigns_paginated',
      {
        p_search_query: null,
        p_categories: null,
        p_platforms: null,
        p_min_budget: null,
        p_max_budget: null,
        p_location: null,
        p_min_followers: null,
        p_max_followers: null,
        p_gender: null,
        p_deadline_before: null,
        p_sort_by: 'created_at',
        p_sort_order: 'desc',
        p_limit: 20,
        p_offset: 0,
        p_featured_only: false,
      }
    );
    
    const endTime = Date.now();
    const duration = endTime - startTime;

    if (perfError) {
      console.error('❌ Greška:', perfError);
    } else {
      console.log(`✅ Učitano ${perfTest?.length || 0} kampanja za ${duration}ms`);
      console.log(`⚡ Performance: ~${Math.round(duration / (perfTest?.length || 1))}ms po kampanji`);
    }

  } catch (error) {
    console.error('💥 Fatalna greška:', error);
  }

  console.log('\n🏁 Test završen!');
}

// Pokreni test
testCampaignsRPC();