'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { CreditCard, Loader2 } from 'lucide-react';
import { createApplicationPaymentSession } from '@/lib/campaigns';
import { toast } from 'sonner';

interface ApplicationPaymentButtonProps {
  applicationId: string;
  proposedRate: number;
  disabled?: boolean;
  className?: string;
}

export function ApplicationPaymentButton({
  applicationId,
  proposedRate,
  disabled = false,
  className = '',
}: ApplicationPaymentButtonProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handlePayment = async () => {
    try {
      setIsLoading(true);
      
      const { url } = await createApplicationPaymentSession(applicationId, proposedRate);
      
      if (url) {
        // Redirect to Stripe Checkout
        window.location.href = url;
      } else {
        throw new Error('No payment URL received');
      }
    } catch (error) {
      console.error('Payment error:', error);
      toast.error('<PERSON><PERSON><PERSON><PERSON> pri kreiranju plaćanja. Pokušajte ponovo.');
    } finally {
      setIsLoading(false);
    }
  };

  const platformFee = Math.round(proposedRate * 0.10);
  const totalAmount = proposedRate + platformFee;

  return (
    <div className="space-y-4">
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-lg p-4 border border-blue-200/50 dark:border-blue-800/30">
        <h4 className="font-medium mb-3 text-gray-800 dark:text-gray-200">
          Pregled plaćanja:
        </h4>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Budžet za influencera:</span>
            <span className="font-semibold">{proposedRate.toLocaleString()} €</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Platforma naknada (10%):</span>
            <span className="font-semibold">{platformFee.toLocaleString()} €</span>
          </div>
          <div className="border-t border-gray-200 dark:border-gray-700 pt-2 mt-2">
            <div className="flex justify-between">
              <span className="font-medium">Ukupno za plaćanje:</span>
              <span className="font-bold text-lg">{totalAmount.toLocaleString()} €</span>
            </div>
          </div>
        </div>
      </div>

      <Button
        onClick={handlePayment}
        disabled={disabled || isLoading}
        className={`w-full flex items-center justify-center gap-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium py-3 ${className}`}
      >
        {isLoading ? (
          <Loader2 className="h-5 w-5 animate-spin" />
        ) : (
          <CreditCard className="h-5 w-5" />
        )}
        {isLoading ? 'Kreiranje plaćanja...' : `Plati ${totalAmount.toLocaleString()} €`}
      </Button>

      <p className="text-xs text-center text-gray-500 dark:text-gray-400">
        Sigurno plaćanje putem Stripe. Vaš novac će biti osiguran do završetka posla.
      </p>
    </div>
  );
}