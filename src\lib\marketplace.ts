import { supabase } from './supabase';

// Tipovi za marketplace
export interface SearchFilters {
  search?: string;
  categories?: number[];
  platforms?: number[];
  contentTypes?: number[];
  minPrice?: number;
  maxPrice?: number;
  minFollowers?: number;
  maxFollowers?: number;
  location?: string;
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';
  minAge?: number;
  maxAge?: number;
  verifiedOnly?: boolean;
  sortBy?:
    | 'relevance'
    | 'price_asc'
    | 'price_desc'
    | 'followers_desc'
    | 'newest';
  limit?: number;
  offset?: number;
}

export interface InfluencerSearchResult {
  id: string;
  username: string;
  full_name: string;
  avatar_url: string;
  navbar_avatar_url?: string;
  card_avatar_url?: string;
  profile_avatar_url?: string;
  preview_avatar_url?: string;
  bio: string;
  location: string;
  gender: string;
  age: number;
  subscription_type: 'free' | 'premium';
  custom_offers_enabled: boolean;
  categories: string[];
  platforms: Array<{
    platform_id: number;
    platform_name: string;
    platform_icon: string;
    handle: string;
    followers_count: number;
    is_verified: boolean;
  }>;
  pricing: Array<{
    platform_id: number;
    platform_name: string;
    content_type_id: number;
    content_type_name: string;
    price: number;
    currency: string;
  }>;
  min_price: number;
  max_price: number;
  total_followers: number;
  relevance_score: number;
  average_rating: number;
  total_reviews: number;
}

export interface PublicInfluencerProfile {
  id: string;
  username: string;
  full_name: string;
  avatar_url: string;
  bio: string;
  location: string;
  gender: string;
  age: number;
  subscription_type: 'free' | 'premium';
  custom_offers_enabled: boolean;
  average_rating: number;
  total_reviews: number;
  categories: Array<{
    id: number;
    name: string;
    icon: string;
    is_primary: boolean;
  }>;
  platforms: Array<{
    platform_id: number;
    platform_name: string;
    platform_icon: string;
    handle: string;
    followers_count: number;
    is_verified: boolean;
  }>;
  pricing: Array<{
    platform_id: number;
    platform_name: string;
    content_type_id: number;
    content_type_name: string;
    price: number;
    currency: string;
  }>;
  portfolio_urls: string[];
  total_followers: number;
  created_at: string;
}

// Extended interface za pagination rezultate
export interface SearchInfluencersResult {
  data: InfluencerSearchResult[];
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
    limit: number;
    offset: number;
  };
  error?: any;
}

/**
 * OPTIMIZOVANA pretraga influencera sa RPC funkcijom
 * Rešava N+1 query problem iz stare implementacije
 */
export async function searchInfluencers(
  filters: SearchFilters = {}
): Promise<SearchInfluencersResult> {
  const startTime = performance.now();

  try {
    // Parametri za pagination
    const page = Math.max(
      1,
      Math.floor((filters.offset || 0) / (filters.limit || 20)) + 1
    );
    const limit = Math.min(50, Math.max(5, filters.limit || 20));
    const offset = filters.offset || 0;

    console.log(`[PERF] searchInfluencers starting - RPC mode`);

    // Poziv optimizovane RPC funkcije
    const { data: rpcData, error: rpcError } = await supabase.rpc(
      'get_influencers_paginated',
      {
        p_search: filters.search || null,
        p_categories: filters.categories || null,
        p_platforms: filters.platforms || null,
        p_min_age: filters.minAge || null,
        p_max_age: filters.maxAge || null,
        p_gender: filters.gender || null,
        p_min_followers: filters.minFollowers || null,
        p_max_followers: filters.maxFollowers || null,
        p_min_price: filters.minPrice || null,
        p_max_price: filters.maxPrice || null,
        p_location: filters.location || null,
        p_sort_by: filters.sortBy || 'created_at',
        p_sort_order: filters.sortBy === 'price_asc' ? 'asc' : 'desc',
        p_limit: limit,
        p_offset: offset,
      }
    );

    const endTime = performance.now();
    console.log(
      `[PERF] RPC call completed in ${(endTime - startTime).toFixed(2)}ms`
    );

    if (rpcError) {
      console.warn(
        'RPC function failed, falling back to legacy implementation:',
        rpcError
      );
      return await searchInfluencersLegacy(filters);
    }

    if (!rpcData || rpcData.length === 0) {
      console.log('[PERF] No data returned from RPC');
      return { data: [], error: null };
    }

    // Transform RPC rezultata u InfluencerSearchResult format
    // Note: Premium-first sorting is now handled in the RPC function itself
    const influencers: InfluencerSearchResult[] = rpcData.map((item: any) => ({
      id: item.id,
      username: item.username || '',
      full_name: item.full_name || item.username || '',
      avatar_url: item.avatar_url || '',
      navbar_avatar_url: item.avatar_url || '', // fallback to avatar_url
      card_avatar_url: item.avatar_url || '', // fallback to avatar_url
      profile_avatar_url: item.avatar_url || '', // fallback to avatar_url
      preview_avatar_url: item.avatar_url || '', // fallback to avatar_url
      bio: item.bio || '',
      location: item.location || '',
      gender: item.gender || 'prefer_not_to_say',
      age: item.age || 0,
      subscription_type: item.subscription_type || 'free', // RPC already determines this correctly
      custom_offers_enabled: item.subscription_type === 'premium', // Premium users can receive custom offers
      categories: [], // TODO: Implementirati u RPC funkciji
      platforms: item.platforms || [],
      pricing: [], // Uklonjen pricing - ne povlačimo ga više u osnovnom pozivu
      min_price: 0, // Uklonjen min_price - ne povlačimo ga više
      max_price: 0, // Uklonjen max_price - ne povlačimo ga više
      total_followers: parseInt(item.total_followers) || 0,
      relevance_score: 1.0, // TODO: Implementirati relevance scoring
      average_rating: parseFloat(item.avg_rating) || 0,
      total_reviews: parseInt(item.total_reviews) || 0,
    }));

    const finalTime = performance.now();
    console.log(
      `[PERF] searchInfluencers completed in ${(finalTime - startTime).toFixed(2)}ms - returned ${influencers.length} items`
    );

    return {
      data: influencers,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(influencers.length / limit), // TODO: Implementirati count RPC
        totalItems: influencers.length, // TODO: Implementirati count RPC - privremeno koristimo length
        hasNextPage: influencers.length === limit,
        hasPrevPage: page > 1,
        limit,
        offset,
      },
      error: null,
    };
  } catch (error) {
    console.error(
      'Error in optimized searchInfluencers, falling back to legacy:',
      error
    );
    return await searchInfluencersLegacy(filters);
  }
}

/**
 * LEGACY implementacija - fallback ako RPC ne radi
 * @deprecated Koristiti samo kao fallback
 */
export async function searchInfluencersLegacy(
  filters: SearchFilters = {}
): Promise<SearchInfluencersResult> {
  const startTime = performance.now();
  console.log(`[PERF] searchInfluencersLegacy starting - LEGACY mode`);

  try {
    // Build the query directly to get real data from new tables
    let query = supabase
      .from('profiles')
      .select(
        `
        id,
        username,
        full_name,
        avatar_url,
        navbar_avatar_url,
        card_avatar_url,
        profile_avatar_url,
        preview_avatar_url,
        bio,
        city,
        country,
        age,
        gender,
        average_rating,
        total_reviews,
        created_at,
        influencers!inner(
          subscription_type,
          portfolio_urls
        )
      `
      )
      .eq('user_type', 'influencer')
      .eq('profile_completed', true)
      .limit(filters.limit || 12);

    // Add search filter
    if (filters.search) {
      query = query.or(
        `username.ilike.%${filters.search}%,full_name.ilike.%${filters.search}%,bio.ilike.%${filters.search}%`
      );
    }

    // Add age filters
    if (filters.minAge) {
      query = query.gte('age', filters.minAge);
    }
    if (filters.maxAge) {
      query = query.lte('age', filters.maxAge);
    }

    // Add gender filter
    if (filters.gender) {
      query = query.eq('gender', filters.gender);
    }

    const { data: profiles, error } = await query;

    if (error) {
      console.error('Supabase query error:', error);
      throw error;
    }

    const queryTime = performance.now();
    console.log(
      `[PERF] Legacy profiles query: ${(queryTime - startTime).toFixed(2)}ms - ${profiles?.length || 0} profiles`
    );

    // Load platforms and pricing data for each influencer (N+1 PROBLEM!)
    const influencersWithData = await Promise.all(
      (profiles || []).map(async profile => {
        // Load platforms
        const { data: platformsData } = await supabase
          .from('influencer_platforms')
          .select(
            `
            platform_id,
            handle,
            followers_count,
            is_verified,
            platforms!inner(name, icon)
          `
          )
          .eq('influencer_id', profile.id)
          .eq('is_active', true);

        // Load pricing
        const { data: pricingData } = await supabase
          .from('influencer_platform_pricing')
          .select(
            `
            platform_id,
            content_type_id,
            price,
            currency,
            platforms!inner(name, icon),
            content_types!inner(name)
          `
          )
          .eq('influencer_id', profile.id)
          .eq('is_available', true);

        // Check new subscription system for premium status
        let isPremium = false;
        try {
          const { data: subscription } = await supabase
            .from('user_subscriptions')
            .select('status')
            .eq('user_id', profile.id)
            .eq('user_type', 'influencer')
            .eq('status', 'active')
            .single();

          isPremium = !!subscription;
        } catch (error) {
          // No fallback - only use new subscription system
          isPremium = false;
        }

        // Transform platforms data
        const platforms = (platformsData || []).map(p => ({
          platform_id: p.platform_id,
          platform_name: p.platforms.name,
          platform_icon: p.platforms.icon,
          handle: p.handle,
          followers_count: p.followers_count,
          is_verified: p.is_verified,
        }));

        // Transform pricing data
        const pricing = (pricingData || []).map(p => ({
          platform_id: p.platform_id,
          platform_name: p.platforms.name,
          content_type_id: p.content_type_id,
          content_type_name: p.content_types.name,
          price: parseFloat(p.price),
          currency: p.currency,
        }));

        // Calculate total followers and min/max prices
        const totalFollowers = platforms.reduce(
          (sum, p) => sum + p.followers_count,
          0
        );
        const prices = pricing.map(p => p.price);
        const minPrice = prices.length > 0 ? Math.min(...prices) : 0;
        const maxPrice = prices.length > 0 ? Math.max(...prices) : 0;

        return {
          id: profile.id,
          username: profile.username || '',
          full_name: profile.full_name || profile.username || '',
          avatar_url: profile.avatar_url || '',
          navbar_avatar_url: profile.navbar_avatar_url || '',
          card_avatar_url: profile.card_avatar_url || '',
          profile_avatar_url: profile.profile_avatar_url || '',
          preview_avatar_url: profile.preview_avatar_url || '',
          bio: profile.bio || '',
          location:
            `${profile.city || ''}${profile.city && profile.country ? ', ' : ''}${profile.country || ''}`.trim(),
          gender: profile.gender || 'prefer_not_to_say',
          age: profile.age || 0,
          subscription_type: isPremium ? 'premium' : 'free',
          custom_offers_enabled: isPremium, // Premium users can receive custom offers
          categories: [], // TODO: Add categories when implemented
          platforms,
          pricing,
          min_price: minPrice,
          max_price: maxPrice,
          total_followers: totalFollowers,
          relevance_score: 1.0,
          average_rating: profile.average_rating || 0,
          total_reviews: profile.total_reviews || 0,
        };
      })
    );

    const endTime = performance.now();
    console.log(
      `[PERF] searchInfluencersLegacy completed in ${(endTime - startTime).toFixed(2)}ms - returned ${influencersWithData.length} items`
    );

    // Transform data to match InfluencerSearchResult interface
    const influencers: InfluencerSearchResult[] = influencersWithData;

    return { data: influencers, error: null };
  } catch (error) {
    console.error('Error in searchInfluencersLegacy:', error);
    return { data: [], error };
  }
}

/**
 * Dobijanje ukupnog broja influencera koji odgovaraju filterima
 */
export async function countInfluencers(
  filters: SearchFilters = {}
): Promise<{ data: number; error?: any }> {
  try {
    console.log('[PERF] countInfluencers starting - RPC mode');
    const startTime = performance.now();

    const { data: count, error } = await supabase.rpc(
      'count_influencers_paginated',
      {
        p_search: filters.search || null,
        p_categories: filters.categories || null,
        p_platforms: filters.platforms || null,
        p_min_age: filters.minAge || null,
        p_max_age: filters.maxAge || null,
        p_gender: filters.gender || null,
        p_min_followers: filters.minFollowers || null,
        p_max_followers: filters.maxFollowers || null,
        p_min_price: filters.minPrice || null,
        p_max_price: filters.maxPrice || null,
        p_location: filters.location || null,
      }
    );

    const endTime = performance.now();
    console.log(
      `[PERF] countInfluencers completed in ${(endTime - startTime).toFixed(2)}ms - count: ${count}`
    );

    if (error) {
      console.error('Count RPC function failed:', error);
      return { data: 0, error };
    }

    return { data: count || 0, error: null };
  } catch (error) {
    console.error('Error in countInfluencers:', error);
    return { data: 0, error };
  }
}

/**
 * Dobijanje javnog profila influencera po username-u
 */
export async function getPublicInfluencerProfile(username: string) {
  try {
    // Get profile data
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('username', username)
      .eq('user_type', 'influencer')
      .single();

    if (profileError || !profileData) {
      return {
        data: null,
        error: profileError || { message: 'Profile not found' },
      };
    }

    // Get influencer data
    const { data: influencerData, error: influencerError } = await supabase
      .from('influencers')
      .select('*')
      .eq('id', profileData.id)
      .single();

    // Get platforms
    const { data: platformsData, error: platformsError } = await supabase
      .from('influencer_platforms')
      .select(
        `
        platform_id,
        handle,
        followers_count,
        is_verified,
        platforms (
          id,
          name,
          icon
        )
      `
      )
      .eq('influencer_id', profileData.id);

    // Get categories
    const { data: categoriesData, error: categoriesError } = await supabase
      .from('influencer_categories')
      .select(
        `
        is_primary,
        categories (
          id,
          name,
          icon
        )
      `
      )
      .eq('influencer_id', profileData.id);

    // Get packages
    const { data: packagesData, error: packagesError } = await supabase
      .from('pricing_packages')
      .select('*')
      .eq('influencer_id', profileData.id)
      .eq('is_active', true);

    // Map platforms
    const platforms =
      platformsData?.map(p => ({
        platform_id: p.platform_id,
        platform_name: p.platforms?.name || '',
        platform_icon: p.platforms?.icon || '',
        handle: p.handle || '',
        followers_count: p.followers_count || 0,
        is_verified: p.is_verified || false,
      })) || [];

    // Map categories
    const categories =
      categoriesData?.map(c => ({
        id: c.categories?.id || 0,
        name: c.categories?.name || '',
        icon: c.categories?.icon || '',
        is_primary: c.is_primary || false,
      })) || [];

    // Calculate total followers
    const total_followers = platforms.reduce(
      (sum, platform) => sum + (platform.followers_count || 0),
      0
    );

    // Check new subscription system for premium status
    let isPremium = false;
    try {
      const { data: subscription } = await supabase
        .from('user_subscriptions')
        .select('status')
        .eq('user_id', profileData.id)
        .eq('user_type', 'influencer')
        .eq('status', 'active')
        .single();
      
      isPremium = !!subscription;
    } catch (error) {
      // If no subscription found or error, fall back to old system
      isPremium = influencerData?.subscription_type === 'premium';
    }

    // Calculate average rating (placeholder for now)
    const average_rating = 0; // TODO: Calculate from reviews
    const total_reviews = 0; // TODO: Count from reviews

    // Build profile object
    const profile: PublicInfluencerProfile = {
      id: profileData.id,
      username: profileData.username || '',
      full_name:
        profileData.public_display_name ||
        profileData.full_name ||
        profileData.username ||
        '',
      avatar_url: profileData.avatar_url || '',
      bio: profileData.bio || '',
      location: [profileData.city, profileData.country]
        .filter(Boolean)
        .join(', '),
      gender: influencerData?.gender || profileData.gender || '',
      age: influencerData?.age || profileData.age || 0,
      subscription_type: isPremium ? 'premium' : 'free',
      custom_offers_enabled: isPremium, // Premium users can receive custom offers
      categories,
      platforms,
      pricing: [], // TODO: Add pricing data
      portfolio_urls: [], // TODO: Add portfolio URLs
      total_followers,
      average_rating,
      total_reviews,
      created_at: profileData.created_at || new Date().toISOString(),
    };

    return { data: profile, error: null };
  } catch (error) {
    console.error('Error in getPublicInfluencerProfile:', error);
    return { data: null, error };
  }
}

/**
 * Dobijanje svih kategorija za filter
 */
export async function getCategories() {
  try {
    // Mock kategorije
    const mockCategories = [
      { id: 1, name: 'Fitness', slug: 'fitness', icon: '💪' },
      { id: 2, name: 'Moda', slug: 'moda', icon: '👗' },
      { id: 3, name: 'Tehnologija', slug: 'tehnologija', icon: '📱' },
      { id: 4, name: 'Hrana', slug: 'hrana', icon: '🍕' },
      { id: 5, name: 'Putovanja', slug: 'putovanja', icon: '✈️' },
      { id: 6, name: 'Gaming', slug: 'gaming', icon: '🎮' },
      { id: 7, name: 'Ljepota', slug: 'ljepota', icon: '💄' },
      { id: 8, name: 'Zdravlje', slug: 'zdravlje', icon: '🏥' },
    ];

    return { data: mockCategories, error: null };
  } catch (error) {
    console.error('Error in getCategories:', error);
    return { data: null, error };
  }
}

/**
 * Dobijanje svih platformi za filter
 */
export async function getPlatforms() {
  try {
    // TODO: Zameniti sa pravim pozivom kada se kreira platforms tabela
    // const { data, error } = await supabase.from('platforms').select('id, name, slug, icon').eq('is_active', true).order('name');

    // Mock platforme
    const mockPlatforms = [
      { id: 1, name: 'Instagram', slug: 'instagram', icon: '📷' },
      { id: 2, name: 'YouTube', slug: 'youtube', icon: '📺' },
      { id: 3, name: 'TikTok', slug: 'tiktok', icon: '🎵' },
      { id: 4, name: 'Facebook', slug: 'facebook', icon: '📘' },
      { id: 5, name: 'Twitter', slug: 'twitter', icon: '🐦' },
      { id: 6, name: 'LinkedIn', slug: 'linkedin', icon: '💼' },
    ];

    return { data: mockPlatforms, error: null };
  } catch (error) {
    console.error('Error in getPlatforms:', error);
    return { data: null, error };
  }
}

/**
 * Dobijanje content tipova za određene platforme
 */
export async function getContentTypes(platformIds?: number[]) {
  try {
    // TODO: Zameniti sa pravim pozivom kada se kreira content_types tabela

    // Mock content tipovi
    const mockContentTypes = [
      {
        id: 1,
        platform_id: 1,
        name: 'Post',
        slug: 'post',
        description: 'Obična objava',
        platforms: { name: 'Instagram', icon: '📷' },
      },
      {
        id: 2,
        platform_id: 1,
        name: 'Story',
        slug: 'story',
        description: 'Instagram story',
        platforms: { name: 'Instagram', icon: '📷' },
      },
      {
        id: 3,
        platform_id: 1,
        name: 'Reel',
        slug: 'reel',
        description: 'Instagram reel',
        platforms: { name: 'Instagram', icon: '📷' },
      },
      {
        id: 4,
        platform_id: 2,
        name: 'Video',
        slug: 'video',
        description: 'YouTube video',
        platforms: { name: 'YouTube', icon: '📺' },
      },
      {
        id: 5,
        platform_id: 2,
        name: 'Short',
        slug: 'short',
        description: 'YouTube short',
        platforms: { name: 'YouTube', icon: '📺' },
      },
      {
        id: 6,
        platform_id: 3,
        name: 'Video',
        slug: 'tiktok-video',
        description: 'TikTok video',
        platforms: { name: 'TikTok', icon: '🎵' },
      },
    ];

    let filteredContentTypes = mockContentTypes;
    if (platformIds && platformIds.length > 0) {
      filteredContentTypes = mockContentTypes.filter(ct =>
        platformIds.includes(ct.platform_id)
      );
    }

    return { data: filteredContentTypes, error: null };
  } catch (error) {
    console.error('Error in getContentTypes:', error);
    return { data: null, error };
  }
}

/**
 * Refresh materialized view (admin funkcija)
 */
export async function refreshSearchView() {
  try {
    // NOTE: This function is disabled for security reasons.
    // Materialized view refresh should be done manually from Supabase dashboard
    // or through scheduled jobs, not from client-side code.
    console.warn('refreshSearchView is disabled for security reasons');
    return { data: null, error: { message: 'Function disabled for security' } };
  } catch (error) {
    console.error('Error in refreshSearchView:', error);
    return { data: null, error };
  }
}

/**
 * Dobijanje statistika za marketplace (broj influencera, kategorija, itd.)
 */
export async function getMarketplaceStats() {
  try {
    // Broj influencera
    const { count: influencersCount } = await supabase
      .from('influencer_search_view')
      .select('*', { count: 'exact', head: true });

    // Broj kategorija
    const { count: categoriesCount } = await supabase
      .from('categories')
      .select('*', { count: 'exact', head: true });

    // Broj platformi
    const { count: platformsCount } = await supabase
      .from('platforms')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true);

    // Prosečna cijena
    const { data: avgPriceData } = await supabase
      .from('influencer_platform_pricing')
      .select('price')
      .eq('is_available', true);

    const avgPrice =
      avgPriceData && avgPriceData.length > 0
        ? avgPriceData.reduce((sum, item) => sum + (item.price || 0), 0) /
          avgPriceData.length
        : 0;

    return {
      data: {
        influencersCount: influencersCount || 0,
        categoriesCount: categoriesCount || 0,
        platformsCount: platformsCount || 0,
        averagePrice: Math.round(avgPrice),
      },
      error: null,
    };
  } catch (error) {
    console.error('Error in getMarketplaceStats:', error);
    return { data: null, error };
  }
}
