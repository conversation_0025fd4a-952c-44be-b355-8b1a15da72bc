'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { InfiniteScrollCache } from '@/components/ui/infinite-scroll';

interface UseInfiniteScrollOptions<T> {
  fetchData: (
    offset: number,
    limit: number
  ) => Promise<{ data: T[]; hasMore: boolean; error?: any }>;
  limit?: number;
  cacheKey?: string;
  dependencies?: any[];
  initialData?: T[];
}

export function useInfiniteScroll<T>({
  fetchData,
  limit = 20,
  cacheKey,
  dependencies = [],
  initialData = [],
}: UseInfiniteScrollOptions<T>) {
  const [data, setData] = useState<T[]>(initialData);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<any>(null);
  const offsetRef = useRef(0);
  const isInitialLoad = useRef(true);

  // Reset funkcija kada se promene filters/dependencies
  const reset = useCallback(async () => {
    // Obriši cache ako se menjaju filteri
    if (cacheKey) {
      InfiniteScrollCache.clearCache(cacheKey);
    }

    setData([]);
    setHasMore(true);
    setError(null);
    offsetRef.current = 0;
    isInitialLoad.current = true;

    // Učitaj prva data
    await loadInitialData();
  }, [cacheKey]);

  // Učitaj prva data
  const loadInitialData = useCallback(async () => {
    if (isLoading) return;

    try {
      setIsLoading(true);
      setError(null);

      // Proverava cache ako postoji cacheKey
      let cachedData: T[] = [];
      if (cacheKey && isInitialLoad.current) {
        cachedData = InfiniteScrollCache.loadData(cacheKey);
        if (cachedData.length > 0) {
          console.log(`[Cache] Restored ${cachedData.length} items from cache`);
          setData(cachedData);
          offsetRef.current = cachedData.length;
          // Pretpostavka da ima više ako je cache pun
          setHasMore(cachedData.length >= limit);
          setIsLoading(false);
          isInitialLoad.current = false;
          return;
        }
      }

      console.log('[Cache] No cache found, fetching fresh data');
      const result = await fetchData(0, limit);

      if (result.error) {
        setError(result.error);
        return;
      }

      const newData = result.data || [];
      setData(newData);
      setHasMore(result.hasMore);
      offsetRef.current = newData.length;

      // Cache nova data
      if (cacheKey) {
        InfiniteScrollCache.saveData(cacheKey, newData);
      }
    } catch (err) {
      console.error('Error loading initial data:', err);
      setError(err);
    } finally {
      setIsLoading(false);
      isInitialLoad.current = false;
    }
  }, [fetchData, limit, cacheKey]);

  // Load more data
  const loadMore = useCallback(async () => {
    if (!hasMore || isLoadingMore || isLoading) return;

    try {
      setIsLoadingMore(true);
      setError(null);

      const result = await fetchData(offsetRef.current, limit);

      if (result.error) {
        setError(result.error);
        return;
      }

      const newData = result.data || [];

      setData(prevData => {
        const updatedData = [...prevData, ...newData];

        // Cache updated data
        if (cacheKey) {
          InfiniteScrollCache.saveData(cacheKey, updatedData);
        }

        return updatedData;
      });

      setHasMore(result.hasMore);
      offsetRef.current += newData.length;
    } catch (err) {
      console.error('Error loading more data:', err);
      setError(err);
    } finally {
      setIsLoadingMore(false);
    }
  }, [fetchData, limit, hasMore, isLoadingMore, isLoading, cacheKey]);

  // Refresh - učitaj podatke ponovo
  const refresh = useCallback(async () => {
    if (cacheKey) {
      InfiniteScrollCache.clearCache(cacheKey);
    }

    setData([]);
    offsetRef.current = 0;
    setHasMore(true);
    setError(null);
    isInitialLoad.current = true;

    await loadInitialData();
  }, [loadInitialData, cacheKey]);

  // Auto-load na promenu dependencies (samo ako se stvarno promene, ne na mount)
  const prevDepsRef = useRef<any[]>(dependencies);
  useEffect(() => {
    // Proveri da li su se dependencies stvarno promenile
    const depsChanged =
      dependencies.some((dep, index) => dep !== prevDepsRef.current[index]) ||
      dependencies.length !== prevDepsRef.current.length;

    if (depsChanged && !isInitialLoad.current) {
      prevDepsRef.current = dependencies;
      reset();
    } else {
      prevDepsRef.current = dependencies;
    }
  }, dependencies);

  // Initial load - poboljšana logika za cache restore
  useEffect(() => {
    if (isInitialLoad.current) {
      loadInitialData();
    }
  }, []); // Samo na mount, ne na svaku promenu

  return {
    data,
    isLoading,
    isLoadingMore,
    hasMore,
    error,
    actions: {
      loadMore,
      reset,
      refresh,
    },
  };
}

// Hook za cache-iranje filtera
export function useCachedFilters<T>(cacheKey: string, initialFilters: T) {
  const [filters, setFilters] = useState<T>(() => {
    if (typeof window === 'undefined') return initialFilters;

    const cached = InfiniteScrollCache.loadFilters(cacheKey);
    return cached || initialFilters;
  });

  const updateFilters = useCallback(
    (newFilters: T) => {
      setFilters(newFilters);
      if (cacheKey) {
        InfiniteScrollCache.saveFilters(cacheKey, newFilters);
      }
    },
    [cacheKey]
  );

  return [filters, updateFilters] as const;
}
