'use client';

import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import {
  LayoutDashboard,
  FileText,
  Inbox,
  MessageCircle,
  User,
  Send,
} from 'lucide-react';
import { FiTarget, FiMessageSquare } from 'react-icons/fi';
import { RiContractLine } from 'react-icons/ri';

interface MobileBottomNavigationProps {
  userType: 'influencer' | 'business';
}

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
}

export function MobileBottomNavigation({
  userType,
}: MobileBottomNavigationProps) {
  const pathname = usePathname();

  // Glavne 5 ikonice za influencere (dodana "Moje aplikacije")
  const influencerMainNav: NavItem[] = [
    {
      name: 'Dashboard',
      href: '/dashboard/influencer',
      icon: LayoutDashboard,
      label: 'Home',
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      href: '/dashboard/influencer/offers',
      icon: Inbox,
      label: '<PERSON>nu<PERSON>',
    },
    {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      href: '/marketplace/campaigns',
      icon: FileText,
      label: 'Kampanje',
    },
    {
      name: 'Moje aplikacije',
      href: '/dashboard/influencer/applications',
      icon: FileText,
      label: 'Aplikacije',
    },
    {
      name: 'Poruke',
      href: '/dashboard/chat',
      icon: MessageCircle,
      label: 'Poruke',
    },
  ];

  // Glavne 6 ikonice za biznis korisnike (dodani "Moje ponude")
  const businessMainNav: NavItem[] = [
    {
      name: 'Dashboard',
      href: '/dashboard/biznis',
      icon: LayoutDashboard,
      label: 'Home',
    },
    {
      name: 'Kampanje',
      href: '/dashboard/campaigns',
      icon: FiTarget,
      label: 'Kampanje',
    },
    {
      name: 'Aplikacije',
      href: '/dashboard/biznis/applications',
      icon: RiContractLine,
      label: 'Aplikacije',
    },
    {
      name: 'Influenceri',
      href: '/marketplace/influencers',
      icon: User,
      label: 'Influenceri',
    },
    {
      name: 'Moje ponude',
      href: '/dashboard/biznis/offers',
      icon: Send,
      label: 'Ponude',
    },
    {
      name: 'Poruke',
      href: '/dashboard/chat',
      icon: FiMessageSquare,
      label: 'Poruke',
    },
  ];

  const mainNavigation =
    userType === 'influencer' ? influencerMainNav : businessMainNav;

  const isActive = (href: string) => {
    return pathname === href || pathname.startsWith(href + '/');
  };

  return (
    <>
      {/* Mobile Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 z-50 bg-card border-t border-border md:hidden">
        <div className="flex items-center justify-around py-2">
          {/* Glavne 5 ikonice */}
          {mainNavigation.map(item => (
            <Link key={item.name} href={item.href}>
              <div
                className={cn(
                  'flex flex-col items-center justify-center p-1 min-w-[50px] rounded-lg transition-colors',
                  isActive(item.href)
                    ? 'text-primary bg-primary/10'
                    : 'text-muted-foreground hover:text-foreground hover:bg-accent'
                )}
              >
                <item.icon className="h-4 w-4 mb-1" />
                <span className="text-xs font-medium">{item.label}</span>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </>
  );
}
