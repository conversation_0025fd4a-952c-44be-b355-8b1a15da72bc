'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Label } from '@/components/ui/label';
import {
  CheckCircle,
  XCircle,
  Clock,
  FileText,
  User,
  Calendar,
  Euro,
  Star,
  ExternalLink,
  MessageCircle,
} from 'lucide-react';
import { JobCompletionWithDetails } from '@/lib/job-completions';
import { ApproveJobModal } from './ApproveJobModal';
import { RejectJobModal } from './RejectJobModal';
import { formatDate as formatDateUtil } from '@/lib/date-utils';
import { getDisplayName } from '@/lib/utils';

interface BusinessJobCompletionCardProps {
  jobCompletion: JobCompletionWithDetails;
  onUpdate?: () => void;
}

const statusConfig = {
  pending: {
    label: 'Na čekanju',
    color: 'bg-yellow-100 text-yellow-800',
    icon: Clock,
  },
  submitted: {
    label: 'Poslano na pregled',
    color: 'bg-blue-100 text-blue-800',
    icon: FileText,
  },
  approved: {
    label: 'Odobreno',
    color: 'bg-green-100 text-green-800',
    icon: CheckCircle,
  },
  rejected: {
    label: 'Odbačeno',
    color: 'bg-red-100 text-red-800',
    icon: XCircle,
  },
  completed: {
    label: 'Završeno',
    color: 'bg-green-100 text-green-800',
    icon: CheckCircle,
  },
};

export function BusinessJobCompletionCard({
  jobCompletion,
  onUpdate,
}: BusinessJobCompletionCardProps) {
  const [showApproveModal, setShowApproveModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);

  const status = jobCompletion.status || 'pending';
  const config = statusConfig[status as keyof typeof statusConfig];
  const StatusIcon = config?.icon || Clock;

  const formatDate = (dateString: string | null) => {
    return formatDateUtil(dateString);
  };

  const renderStars = (rating: number | null) => {
    if (!rating) return null;

    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map(star => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating
                ? 'fill-yellow-400 text-yellow-400'
                : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-2 text-sm font-medium">{rating}/5</span>
      </div>
    );
  };

  const canReview = status === 'submitted';

  return (
    <>
      <Card className="w-full relative overflow-hidden bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
        <div className="relative">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="space-y-2">
                <CardTitle className="text-lg">
                  {jobCompletion.direct_offer?.title ||
                    jobCompletion.campaign_application?.campaign?.title ||
                    'Posao'}
                </CardTitle>
                <div className="flex items-center gap-2">
                  <StatusIcon className="h-4 w-4" />
                  <Badge className={config?.color}>{config?.label}</Badge>
                </div>
              </div>
              <div className="text-right">
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <Euro className="h-4 w-4" />
                  {jobCompletion.direct_offer?.budget ||
                    jobCompletion.campaign_application?.proposed_rate ||
                    0}
                </div>
              </div>
            </div>
          </CardHeader>

          <CardContent className="space-y-4">
            {/* Job Details */}
            <div className="space-y-2">
              <h4 className="font-medium text-sm">
                {jobCompletion.direct_offer
                  ? 'Detalji ponude'
                  : 'Detalji kampanje'}
              </h4>
              <p className="text-sm text-muted-foreground">
                {jobCompletion.direct_offer?.description ||
                  jobCompletion.campaign_application?.campaign?.description ||
                  'Nema opisa'}
              </p>
            </div>

            {/* Influencer Info */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Influencer</Label>
              <div className="flex items-center gap-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage
                    src={jobCompletion.influencer_profile?.avatar_url || ''}
                  />
                  <AvatarFallback>
                    <User className="h-4 w-4" />
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="text-sm font-medium">
                    {getDisplayName(jobCompletion.influencer_profile)}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    @{jobCompletion.influencer_profile?.username}
                  </p>
                </div>
              </div>
            </div>

            {/* Submission Details */}
            {jobCompletion.submission_notes && (
              <div className="space-y-4">
                {(() => {
                  try {
                    const submissionData = JSON.parse(
                      jobCompletion.submission_notes
                    );
                    return (
                      <>
                        {/* Post Links */}
                        {submissionData.post_links &&
                          submissionData.post_links.length > 0 && (
                            <div className="space-y-3">
                              <div className="flex items-center gap-2">
                                <ExternalLink className="h-4 w-4 text-blue-600" />
                                <Label className="text-sm font-medium">
                                  Objavljeni postovi
                                </Label>
                              </div>
                              <div className="space-y-2">
                                {submissionData.post_links.map(
                                  (link: string, index: number) => (
                                    <div
                                      key={index}
                                      className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 p-3 rounded-lg border border-blue-200/50 dark:border-blue-800/30"
                                    >
                                      <a
                                        href={link}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium flex items-center gap-2 hover:underline"
                                      >
                                        <ExternalLink className="h-3 w-3" />
                                        {link}
                                      </a>
                                    </div>
                                  )
                                )}
                              </div>
                            </div>
                          )}

                        {/* Optional Message */}
                        {submissionData.message && (
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <MessageCircle className="h-4 w-4 text-green-600" />
                              <Label className="text-sm font-medium">
                                Poruka influencera
                              </Label>
                            </div>
                            <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 p-3 rounded-lg border border-green-200/50 dark:border-green-800/30">
                              <p className="text-sm text-gray-700 dark:text-gray-300">
                                {submissionData.message}
                              </p>
                            </div>
                          </div>
                        )}
                      </>
                    );
                  } catch {
                    // Fallback for old format
                    return (
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">
                          Napomene o predaji
                        </Label>
                        <div className="bg-white/60 dark:bg-gray-800/40 p-3 rounded-lg border border-purple-100/50 dark:border-purple-800/30">
                          <p className="text-sm text-gray-700 dark:text-gray-300">
                            {jobCompletion.submission_notes}
                          </p>
                        </div>
                      </div>
                    );
                  }
                })()}
              </div>
            )}

            {/* Review Notes */}
            {jobCompletion.review_notes && (
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  Napomene o pregledu
                </Label>
                <div className="bg-white/60 dark:bg-gray-800/40 p-3 rounded-lg border border-purple-100/50 dark:border-purple-800/30">
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    {jobCompletion.review_notes}
                  </p>
                </div>
              </div>
            )}

            {/* Timestamps */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span>Kreiran: {formatDate(jobCompletion.created_at)}</span>
              </div>
              {jobCompletion.submitted_at && (
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  <span>Poslan: {formatDate(jobCompletion.submitted_at)}</span>
                </div>
              )}
              {jobCompletion.reviewed_at && (
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  <span>
                    Pregledan: {formatDate(jobCompletion.reviewed_at)}
                  </span>
                </div>
              )}
            </div>

            {/* Ocjene */}
            {(jobCompletion.business_to_influencer_review ||
              jobCompletion.influencer_to_business_review) && (
              <div className="space-y-4 pt-4 border-t">
                <h4 className="font-medium text-sm">Ocjene</h4>

                {/* Ocjena biznisa za influencera */}
                {jobCompletion.business_to_influencer_review && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label className="text-sm font-medium">
                        Ocjena biznisa za influencera
                      </Label>
                      {renderStars(
                        jobCompletion.business_to_influencer_review.rating
                      )}
                    </div>
                    {jobCompletion.business_to_influencer_review.comment && (
                      <div className="bg-white/60 dark:bg-gray-800/40 p-3 rounded-lg border border-purple-100/50 dark:border-purple-800/30">
                        <p className="text-sm text-gray-700 dark:text-gray-300">
                          {jobCompletion.business_to_influencer_review.comment}
                        </p>
                      </div>
                    )}
                    <p className="text-xs text-muted-foreground">
                      {formatDate(
                        jobCompletion.business_to_influencer_review.created_at
                      )}
                    </p>
                  </div>
                )}

                {/* Ocjena influencera za biznis */}
                {jobCompletion.influencer_to_business_review && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label className="text-sm font-medium">
                        Ocjena influencera za biznis
                      </Label>
                      {renderStars(
                        jobCompletion.influencer_to_business_review.rating
                      )}
                    </div>
                    {jobCompletion.influencer_to_business_review.comment && (
                      <div className="bg-white/60 dark:bg-gray-800/40 p-3 rounded-lg border border-purple-100/50 dark:border-purple-800/30">
                        <p className="text-sm text-gray-700 dark:text-gray-300">
                          {jobCompletion.influencer_to_business_review.comment}
                        </p>
                      </div>
                    )}
                    <p className="text-xs text-muted-foreground">
                      {formatDate(
                        jobCompletion.influencer_to_business_review.created_at
                      )}
                    </p>
                  </div>
                )}
              </div>
            )}

            {/* Direct Action Buttons */}
            {canReview && (
              <div className="flex gap-2 pt-4 border-t">
                <button
                  onClick={() => setShowApproveModal(true)}
                  className="flex-1 flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-green-400 via-emerald-400 to-green-500 hover:from-green-500 hover:via-emerald-500 hover:to-green-600 rounded-lg transition-all duration-200 hover:shadow-lg"
                >
                  <CheckCircle className="h-4 w-4" />
                  Odobri
                </button>
                <button
                  onClick={() => setShowRejectModal(true)}
                  className="flex-1 flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-red-400 via-pink-400 to-red-500 hover:from-red-500 hover:via-pink-500 hover:to-red-600 rounded-lg transition-all duration-200 hover:shadow-lg"
                >
                  <XCircle className="h-4 w-4" />
                  Odbaci
                </button>
              </div>
            )}
          </CardContent>
        </div>
      </Card>

      {/* Modals */}
      <ApproveJobModal
        isOpen={showApproveModal}
        onClose={() => setShowApproveModal(false)}
        jobCompletionId={jobCompletion.id}
        influencerName={
          getDisplayName(jobCompletion.influencer_profile) !==
          'Ime i prezime skriveno'
            ? getDisplayName(jobCompletion.influencer_profile)
            : jobCompletion.influencer_profile?.username || 'Influencer'
        }
        onSuccess={() => {
          setShowApproveModal(false);
          onUpdate?.();
        }}
      />
      <RejectJobModal
        isOpen={showRejectModal}
        onClose={() => setShowRejectModal(false)}
        jobCompletionId={jobCompletion.id}
        influencerName={
          getDisplayName(jobCompletion.influencer_profile) !==
          'Ime i prezime skriveno'
            ? getDisplayName(jobCompletion.influencer_profile)
            : jobCompletion.influencer_profile?.username || 'Influencer'
        }
        onSuccess={() => {
          setShowRejectModal(false);
          onUpdate?.();
        }}
      />
    </>
  );
}
