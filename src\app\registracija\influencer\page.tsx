'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ArrowLeft, Eye, EyeOff, Loader2 } from 'lucide-react';
import Image from 'next/image';
import { useAuth } from '@/contexts/AuthContext';
import { safeSignUp } from '@/lib/auth-helpers';

const registrationSchema = z
  .object({
    email: z.string().email('Unesite validnu email adresu'),
    password: z.string().min(6, 'Lozinka mora imati najmanje 6 karaktera'),
    confirmPassword: z.string(),
    acceptTerms: z
      .boolean()
      .refine(val => val === true, 'Morate prihvatiti uslove korišćenja'),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: 'Lozinke se ne poklapaju',
    path: ['confirmPassword'],
  });

type RegistrationForm = z.infer<typeof registrationSchema>;

export default function InfluencerRegistracijaPage() {
  const router = useRouter();
  const { signUp } = useAuth();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showExistingUserDialog, setShowExistingUserDialog] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<RegistrationForm>({
    resolver: zodResolver(registrationSchema),
  });

  const onSubmit = async (data: RegistrationForm) => {
    setIsLoading(true);

    try {
      const { data: authData, error } = await safeSignUp(
        data.email,
        data.password,
        {
          user_type: 'influencer',
        }
      );

      if (error) {
        // Provjeri različite načine kako Supabase označava postojanje korisnika
        if (
          error.message.includes('already registered') ||
          error.message.includes('already been registered') ||
          error.message.includes('User already registered') ||
          (error as any).status === 422
        ) {
          setShowExistingUserDialog(true);
          return;
        } else {
          setError('root', { message: error.message });
        }
        return;
      }

      if (authData.user && !authData.session) {
        // Email confirmation required
        router.push('/registracija/potvrda-email');
      } else {
        // Auto-confirmed, redirect to influencer onboarding
        router.push('/profil/kreiranje/influencer/onboarding');
      }
    } catch (error) {
      setError('root', { message: 'Došlo je do greške. Pokušajte ponovo.' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoToLogin = () => {
    router.push('/prijava');
  };

  return (
    <div className="min-h-screen bg-instagram-story relative overflow-hidden flex flex-col">
      {/* Background decorative elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/5"></div>
      <div className="absolute top-10 right-10 w-64 h-64 bg-white/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-10 left-10 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>

      {/* Header */}
      <header className="relative z-10 border-b border-white/20 backdrop-blur-sm bg-white/10">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link
            href="/registracija"
            className="flex items-center space-x-2 text-white/80 hover:text-white transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Nazad</span>
          </Link>
          <div className="flex items-center space-x-3">
            <Image
              src="/images/influexus_logo_white.webp"
              alt="Influexus Logo"
              width={32}
              height={32}
              className="rounded-lg"
            />
            <span className="text-xl font-bold gradient-text-auth">Influexus</span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 flex-1 flex items-center justify-center px-4 py-12">
        <Card className="w-full max-w-md glass-instagram border-white/20 shadow-2xl">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl text-white">
              Registracija - Influencer
            </CardTitle>
            <CardDescription className="text-white/70">
              Kreirajte svoj nalog - dodatne informacije ćete uneti kasnije
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              {/* Email */}
              <div className="space-y-2">
                <Label htmlFor="email" className="text-white">
                  Email adresa
                </Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  {...register('email')}
                  className={`bg-white/10 border-white/30 text-white placeholder:text-white/50 focus:border-white/50 focus:ring-white/20 ${errors.email ? 'border-red-400' : ''}`}
                />
                {errors.email && (
                  <p className="text-sm text-red-300">{errors.email.message}</p>
                )}
              </div>

              {/* Password */}
              <div className="space-y-2">
                <Label htmlFor="password" className="text-white">
                  Lozinka
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Unesite lozinku"
                    {...register('password')}
                    className={`bg-white/10 border-white/30 text-white placeholder:text-white/50 focus:border-white/50 focus:ring-white/20 ${errors.password ? 'border-red-400' : ''}`}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-white/10 text-white/60 hover:text-white"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                {errors.password && (
                  <p className="text-sm text-red-300">
                    {errors.password.message}
                  </p>
                )}
              </div>

              {/* Confirm Password */}
              <div className="space-y-2">
                <Label htmlFor="confirmPassword" className="text-white">
                  Potvrdi lozinku
                </Label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    placeholder="Ponovite lozinku"
                    {...register('confirmPassword')}
                    className={`bg-white/10 border-white/30 text-white placeholder:text-white/50 focus:border-white/50 focus:ring-white/20 ${errors.confirmPassword ? 'border-red-400' : ''}`}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-white/10 text-white/60 hover:text-white"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                {errors.confirmPassword && (
                  <p className="text-sm text-red-300">
                    {errors.confirmPassword.message}
                  </p>
                )}
              </div>

              {/* Terms */}
              <div className="flex items-center space-x-2">
                <input
                  id="acceptTerms"
                  type="checkbox"
                  {...register('acceptTerms')}
                  className="rounded border-white/30 bg-white/10 text-instagram-purple focus:ring-white/20"
                />
                <Label htmlFor="acceptTerms" className="text-sm text-white">
                  Prihvatam{' '}
                  <Link
                    href="/uslovi"
                    className="text-white/80 hover:text-white hover:underline transition-colors"
                  >
                    uslove korišćenja
                  </Link>{' '}
                  i{' '}
                  <Link
                    href="/privatnost"
                    className="text-white/80 hover:text-white hover:underline transition-colors"
                  >
                    politiku privatnosti
                  </Link>
                </Label>
              </div>
              {errors.acceptTerms && (
                <p className="text-sm text-red-300">
                  {errors.acceptTerms.message}
                </p>
              )}

              {/* Submit Button */}
              <Button
                type="submit"
                className="w-full bg-white text-instagram-purple hover:bg-white/90 font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Kreiranje naloga...
                  </>
                ) : (
                  'Registruj se'
                )}
              </Button>

              {/* Root Error */}
              {errors.root && (
                <div className="text-sm text-red-300 text-center bg-red-500/20 p-3 rounded-lg border border-red-400/30">
                  {errors.root.message}
                </div>
              )}
            </form>

            {/* Login Link */}
            <div className="mt-6 text-center">
              <p className="text-sm text-white/70">
                Već imate nalog?{' '}
                <Link
                  href="/prijava"
                  className="text-white hover:underline font-medium transition-colors"
                >
                  Prijavite se
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>
      </main>

      {/* Existing User Modal */}
      {showExistingUserDialog && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <div
            className="absolute inset-0 bg-black/60 backdrop-blur-sm"
            onClick={() => setShowExistingUserDialog(false)}
          />

          {/* Modal Content */}
          <div className="relative bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-md border border-purple-400/40 rounded-2xl p-8 shadow-2xl max-w-md w-full mx-4 animate-in fade-in-0 zoom-in-95 duration-200">
            <div className="text-center space-y-6">
              <div className="w-16 h-16 mx-auto bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center shadow-lg">
                <span className="text-white text-2xl">⚠️</span>
              </div>

              <div className="space-y-3">
                <h3 className="text-white font-semibold text-xl">
                  Korisnik već postoji!
                </h3>
                <p className="text-white/80 text-base leading-relaxed">
                  Korisnik sa ovom email adresom već postoji u sistemu. Da li se
                  možda želite prijaviti umjesto registracije?
                </p>
              </div>

              <div className="flex gap-3 justify-center pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowExistingUserDialog(false)}
                  className="border-white/30 text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm transition-all duration-200 px-6"
                >
                  Otkaži
                </Button>
                <Button
                  type="button"
                  onClick={handleGoToLogin}
                  className="bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600 shadow-lg hover:shadow-xl transition-all duration-200 px-6"
                >
                  Idi na prijavu
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
