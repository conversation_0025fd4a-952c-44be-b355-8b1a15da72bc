'use client';

import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import * as TabsPrimitive from '@radix-ui/react-tabs';
import { cn } from '@/lib/utils';

export interface TabWithBadge {
  name: string;
  value: string;
  count?: number;
  icon?: React.ReactNode;
  content?: React.ReactNode;
}

interface TabsWithBadgeProps {
  tabs: TabWithBadge[];
  defaultValue?: string;
  value?: string;
  onValueChange?: (value: string) => void;
  className?: string;
  children?: React.ReactNode;
}

export function TabsWithBadge({
  tabs,
  defaultValue,
  value,
  onValueChange,
  className,
  children,
}: TabsWithBadgeProps) {
  return (
    <Tabs
      defaultValue={defaultValue || tabs[0]?.value}
      value={value}
      onValueChange={onValueChange}
      className={cn('w-full', className)}
    >
      <TabsPrimitive.List className="w-full p-0 bg-transparent justify-start border-b rounded-none gap-1 h-auto flex overflow-x-auto scrollbar-hide">
        {tabs.map(tab => (
          <TabsPrimitive.Trigger
            key={tab.value}
            value={tab.value}
            className="bg-transparent px-3 py-2 border-b-2 border-transparent data-[state=active]:border-primary text-muted-foreground data-[state=active]:text-foreground hover:text-foreground transition-colors cursor-pointer outline-none flex items-center whitespace-nowrap flex-shrink-0"
          >
            {tab.icon && <span className="mr-2">{tab.icon}</span>}
            <span className="text-[13px]">{tab.name}</span>
            {tab.count !== undefined && (
              <Badge
                variant="secondary"
                className="ml-2 px-1 py-0 text-xs rounded-full"
              >
                {tab.count}
              </Badge>
            )}
          </TabsPrimitive.Trigger>
        ))}
      </TabsPrimitive.List>

      {children ||
        tabs.map(tab => (
          <TabsContent key={tab.value} value={tab.value}>
            {tab.content}
          </TabsContent>
        ))}
    </Tabs>
  );
}
