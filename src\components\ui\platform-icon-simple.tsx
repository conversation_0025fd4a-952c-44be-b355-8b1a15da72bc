import React from 'react';
import {
  AiFillTikTok,
  AiOutlineInstagram,
  AiFillYoutube,
} from 'react-icons/ai';
import { FaFacebookF, FaTwitter, FaLinkedinIn } from 'react-icons/fa';
import { cn } from '@/lib/utils';

interface PlatformIconSimpleProps {
  platform: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  variant?: 'default' | 'brand' | 'monochrome';
  className?: string;
}

const PlatformIconSimple: React.FC<PlatformIconSimpleProps> = ({
  platform,
  size = 'md',
  variant = 'brand',
  className,
}) => {
  const normalizedName = platform.toLowerCase();

  // Size mapping for react-icons
  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-lg',
    lg: 'text-xl',
    xl: 'text-2xl',
    '2xl': 'text-3xl',
  };

  const sizeClass = sizeClasses[size];

  // Monochrome variant
  if (variant === 'monochrome') {
    const monoClass = cn(
      sizeClass,
      'text-gray-500 dark:text-gray-400',
      className
    );

    switch (normalizedName) {
      case 'instagram':
        return <AiOutlineInstagram className={monoClass} />;
      case 'tiktok':
        return <AiFillTikTok className={monoClass} />;
      case 'youtube':
        return <AiFillYoutube className={monoClass} />;
      case 'facebook':
        return <FaFacebookF className={monoClass} />;
      case 'twitter':
      case 'x':
        return <FaTwitter className={monoClass} />;
      case 'linkedin':
        return <FaLinkedinIn className={monoClass} />;
      default:
        return (
          <span className={cn('font-semibold', sizeClass, className)}>
            {platform.charAt(0).toUpperCase()}
          </span>
        );
    }
  }

  // Brand colors variant
  switch (normalizedName) {
    case 'instagram':
      return (
        <AiOutlineInstagram
          className={cn(sizeClass, 'text-pink-600', className)}
        />
      );

    case 'tiktok':
      return (
        <AiFillTikTok
          className={cn(sizeClass, 'text-black dark:text-white', className)}
        />
      );

    case 'youtube':
      return (
        <AiFillYoutube className={cn(sizeClass, 'text-red-600', className)} />
      );

    case 'facebook':
      return (
        <FaFacebookF className={cn(sizeClass, 'text-blue-600', className)} />
      );

    case 'twitter':
    case 'x':
      return <FaTwitter className={cn(sizeClass, 'text-sky-400', className)} />;

    case 'linkedin':
      return (
        <FaLinkedinIn className={cn(sizeClass, 'text-blue-700', className)} />
      );

    default:
      return (
        <span
          className={cn('font-semibold text-gray-500', sizeClass, className)}
        >
          {platform.charAt(0).toUpperCase()}
        </span>
      );
  }
};

export default PlatformIconSimple;
