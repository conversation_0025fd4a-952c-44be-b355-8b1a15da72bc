# =================================
# DEVELOPMENT ENVIRONMENT CONFIGURATION
# =================================
# This file contains development-specific environment variables
# Copy to .env.local and customize for your development setup

# =================================
# ENVIRONMENT
# =================================
NODE_ENV=development
DEBUG=true
DISABLE_EMAILS=false

# =================================
# APPLICATION CONFIGURATION
# =================================
NEXT_PUBLIC_APP_URL=http://localhost:3000

# =================================
# DEVELOPMENT SETTINGS
# =================================
# Enable detailed error messages
SHOW_DETAILED_ERRORS=true

# Disable SSL verification for local development (if needed)
# NODE_TLS_REJECT_UNAUTHORIZED=0

# Enable hot reloading and development features
# NEXT_DEV_MODE=true

# =================================
# LOGGING CONFIGURATION
# =================================
# Log level for development (debug, info, warn, error)
LOG_LEVEL=debug

# Enable request logging
LOG_REQUESTS=true

# =================================
# PERFORMANCE SETTINGS
# =================================
# Disable caching in development for hot reloading
DISABLE_CACHE=true

# Enable development tools
ENABLE_DEVTOOLS=true

# =================================
# SECURITY SETTINGS (RELAXED FOR DEVELOPMENT)
# =================================
# Allow localhost origins for CORS
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://127.0.0.1:3000

# Session settings for development
SESSION_SECURE=false
SESSION_SAME_SITE=lax

# =================================
# API SETTINGS
# =================================
# Relaxed rate limiting for development
DEV_RATE_LIMIT_DISABLED=true

# Mock external services in development (if needed)
# MOCK_STRIPE=true
# MOCK_EMAIL_SERVICE=true

# =================================
# DATABASE SETTINGS
# =================================
# Enable database query logging
LOG_DATABASE_QUERIES=true

# =================================
# TESTING
# =================================
# Configuration for running tests
# TEST_DATABASE_URL=postgresql://test:test@localhost:5433/influencer_platform_test