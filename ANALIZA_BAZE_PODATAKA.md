# 📊 ANALIZA SUPABASE BAZE PODATAKA

**Projekt:** awxxrkyommynqlcdwwon  
**URL:** https://awxxrkyommynqlcdwwon.supabase.co  
**Datum analize:** 26.07.2025

## 🗄️ PREGLED TABELA

### 1. **KORISNIČKE TABELE**

#### `profiles` (Glavni profili korisnika)
- **Tip:** <PERSON><PERSON><PERSON>na tabela za sve korisnike
- **Povezana sa:** `auth.users` (Supabase Auth)
- **Polja:**
  - `id` (UUID, PK) - Povezano sa auth.users
  - `user_type` (ENUM) - 'influencer' ili 'business'
  - `username` (VARCHAR 50) - <PERSON><PERSON><PERSON><PERSON><PERSON> ime
  - `full_name` (VARCHAR 100)
  - `avatar_url` (TEXT)
  - `bio` (TEXT)
  - `website_url` (TEXT)
  - `location` (VARCHAR 100)
  - `created_at`, `updated_at` (TIMESTAMP)

#### `influencers` (Dodatne informacije za influencere)
- **Povezana sa:** `profiles(id)`
- **Polja:**
  - `id` (UUID, PK, FK)
  - **Social Media Handles:**
    - `instagram_handle` (VARCHAR 100)
    - `tiktok_handle` (VARCHAR 100)
    - `youtube_handle` (VARCHAR 100)
  - **Follower Counts:**
    - `instagram_followers` (INTEGER)
    - `tiktok_followers` (INTEGER)
    - `youtube_subscribers` (INTEGER)
  - **Pricing:**
    - `price_per_post` (DECIMAL 10,2)
    - `price_per_story` (DECIMAL 10,2)
    - `price_per_reel` (DECIMAL 10,2)
  - **Ostalo:**
    - `engagement_rate` (DECIMAL 5,2)
    - `niche` (VARCHAR 100) - NAPOMENA: Uklonjen u novijoj verziji
    - `portfolio_urls` (TEXT[])
    - `is_verified` (BOOLEAN)
    - `gender` (VARCHAR 20) - Dodano u ažuriranju
    - `age` (INTEGER) - Dodano u ažuriranju

#### `businesses` (Informacije za biznise)
- **Povezana sa:** `profiles(id)`
- **Polja:**
  - `id` (UUID, PK, FK)
  - `company_name` (VARCHAR 200, NOT NULL)
  - `industry` (VARCHAR 100)
  - `company_size` (VARCHAR 50)
  - `budget_range` (VARCHAR 50)
  - `is_verified` (BOOLEAN)

### 2. **KAMPANJE I APLIKACIJE**

#### `campaigns` (Marketing kampanje)
- **Kreirana od:** Biznisi
- **Polja:**
  - `id` (UUID, PK)
  - `business_id` (UUID, FK → businesses)
  - `title` (VARCHAR 200, NOT NULL)
  - `description` (TEXT, NOT NULL)
  - `product_description` (TEXT)
  - `campaign_goal` (VARCHAR 100)
  - `content_types` (content_type[], ARRAY)
  - `target_audience` (JSONB)
  - `budget` (DECIMAL 10,2)
  - `start_date`, `end_date` (DATE)
  - `status` (campaign_status ENUM)
  - `requirements` (TEXT)
  - `deliverables` (TEXT)
  - `show_business_name` (BOOLEAN)

#### `campaign_applications` (Aplikacije influencera na kampanje)
- **Polja:**
  - `id` (UUID, PK)
  - `campaign_id` (UUID, FK → campaigns)
  - `influencer_id` (UUID, FK → influencers)
  - `proposed_price` (DECIMAL 10,2, NOT NULL)
  - `message` (TEXT)
  - `status` (application_status ENUM)
  - `applied_at` (TIMESTAMP)

### 3. **KATEGORIJE I PLATFORME**

#### `categories` (Kategorije sadržaja)
- **Predefinisane kategorije na bosanskom jeziku**
- **Polja:**
  - `id` (SERIAL, PK)
  - `name` (VARCHAR 100, UNIQUE)
  - `slug` (VARCHAR 100, UNIQUE)
  - `description` (TEXT)
  - `icon` (VARCHAR 50)

**Kategorije:**
1. 👗 Moda
2. 💄 Ljepota  
3. ✈️ Putovanja
4. 💪 Zdravlje i fitness
5. 🍽️ Hrana i piće
6. 😂 Komedija i zabava
7. 🎨 Umjetnost i fotografija
8. 🎵 Muzika i ples
9. 👨‍👩‍👧‍👦 Porodica i djeca
10. 💼 Preduzetništvo i biznis
11. 🐕 Životinje i kućni ljubimci
12. 📚 Edukacija
13. 🏔️ Avantura i priroda
14. ⚽ Sport i atletika
15. 💻 Tehnologija

#### `platforms` (Društvene mreže)
- **Polja:**
  - `id` (SERIAL, PK)
  - `name` (VARCHAR 50, UNIQUE)
  - `slug` (VARCHAR 50, UNIQUE)
  - `icon` (VARCHAR 10)
  - `is_active` (BOOLEAN)

**Platforme:**
1. 📷 Instagram
2. 🎵 TikTok
3. 📺 YouTube
4. 👥 Facebook
5. 🐦 Twitter/X

#### `content_types` (Tipovi sadržaja po platformama)
- **Povezana sa:** `platforms(id)`
- **Polja:**
  - `id` (SERIAL, PK)
  - `platform_id` (INTEGER, FK)
  - `name` (VARCHAR 100)
  - `slug` (VARCHAR 100)
  - `description` (TEXT)

### 4. **POVEZNE TABELE**

#### `influencer_platforms` (Koje platforme koristi influencer)
- **Many-to-Many veza između influencera i platformi**
- **Polja:**
  - `influencer_id` (UUID, FK)
  - `platform_id` (INTEGER, FK)
  - `handle` (VARCHAR 100)
  - `followers_count` (INTEGER)
  - `is_verified` (BOOLEAN)
  - `is_active` (BOOLEAN)

#### `influencer_categories` (Kategorije influencera)
- **Many-to-Many veza između influencera i kategorija**
- **Polja:**
  - `influencer_id` (UUID, FK)
  - `category_id` (INTEGER, FK)
  - `is_primary` (BOOLEAN)

## 🔧 ENUM TIPOVI

```sql
-- Tipovi korisnika
CREATE TYPE user_type AS ENUM ('influencer', 'business');

-- Status kampanje
CREATE TYPE campaign_status AS ENUM ('draft', 'active', 'paused', 'completed', 'cancelled');

-- Status aplikacije
CREATE TYPE application_status AS ENUM ('pending', 'accepted', 'rejected', 'completed');

-- Tipovi sadržaja
CREATE TYPE content_type AS ENUM ('post', 'story', 'reel', 'video', 'blog');

-- Prioritet kampanje
CREATE TYPE campaign_priority AS ENUM ('low', 'medium', 'high', 'urgent');
```

## 🔗 KLJUČNE VEZE

1. **Auth Flow:** `auth.users` → `profiles` → `influencers`/`businesses`
2. **Kampanje:** `businesses` → `campaigns` → `campaign_applications` ← `influencers`
3. **Kategorije:** `influencers` ↔ `categories` (many-to-many)
4. **Platforme:** `influencers` ↔ `platforms` (many-to-many)
5. **Content Types:** `platforms` → `content_types`

## 🛡️ SIGURNOST

- **RLS (Row Level Security)** - Implementiran za sve tabele
- **Auth Integration** - Povezano sa Supabase Auth
- **Triggers** - Automatsko kreiranje profila za nove korisnike

## 📈 STANJE BAZE

- **Ukupno tabela:** ~10-12 glavnih tabela
- **Jezik:** Bosanski (kategorije, opisi)
- **Struktura:** Dobro normalizovana
- **Indeksi:** Potrebno dodati za performanse
