import { NextRequest, NextResponse } from 'next/server';
import { healthCheck } from '@/lib/startup-config';
import { withPublicMiddleware, ApiContext } from '@/lib/api-middleware';

/**
 * Health check endpoint
 * Returns application health status and system information
 */
async function handleHealthCheck(context: ApiContext) {
  try {
    const health = await healthCheck();
    
    // Return appropriate status code based on health
    const statusCode = health.status === 'healthy' ? 200 : 503;
    
    return NextResponse.json(health, { status: statusCode });
    
  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json(
      {
        status: 'unhealthy',
        error: 'Health check failed',
        timestamp: new Date().toISOString(),
      },
      { status: 503 }
    );
  }
}

// Export with public middleware (no authentication required)
export const GET = withPublicMiddleware(handleHealthCheck, {
  rateLimit: {
    enabled: true,
    customLimit: { maxRequests: 60, windowMs: 60 * 1000 } // 60 requests per minute
  }
});