'use client';

import * as React from 'react';
import { Crown, ArrowRight, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface PaywallOverlayProps {
  isVisible: boolean;
  title: string;
  description: string;
  children: React.ReactNode;
  ctaText?: string;
  benefits?: string[];
}

const DEFAULT_BENEFITS = [
  'Pristup svim prijavama na kampanje',
  'Detaljni influencer profili',
  'Direktan chat sa influencerima',
  'Napredni analitički izvještaji',
];

export function PaywallOverlay({
  isVisible,
  title,
  description,
  children,
  ctaText = 'Upgrade na Premium',
  benefits = DEFAULT_BENEFITS,
}: PaywallOverlayProps) {
  const handleUpgrade = () => {
    // Redirect to business packages page
    window.location.href = '/dashboard/biznis/packages';
  };

  if (!isVisible) {
    return <>{children}</>;
  }

  return (
    <div className="relative">
      {/* Blurred content in background */}
      <div className="filter blur-sm opacity-50 pointer-events-none select-none">
        {children}
      </div>

      {/* Overlay */}
      <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center p-6">
        <div className="max-w-md w-full bg-white rounded-xl shadow-lg border p-6 text-center">
          {/* Icon */}
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-r from-yellow-100 to-orange-100">
            <Crown className="h-8 w-8 text-yellow-600" />
          </div>

          {/* Title and Description */}
          <h3 className="text-xl font-semibold text-gray-900 mb-2">{title}</h3>
          <p className="text-gray-600 mb-6">{description}</p>

          {/* Benefits List */}
          <div className="mb-6">
            <h4 className="text-sm font-medium text-gray-900 mb-3">
              Premium prednosti:
            </h4>
            <ul className="space-y-2 text-left">
              {benefits.map((benefit, index) => (
                <li
                  key={index}
                  className="flex items-center text-sm text-gray-600"
                >
                  <CheckCircle className="mr-2 h-4 w-4 text-green-500 flex-shrink-0" />
                  <span>{benefit}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* CTA Button */}
          <Button
            onClick={handleUpgrade}
            className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2"
          >
            {ctaText}
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
