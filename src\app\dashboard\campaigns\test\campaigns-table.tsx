"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { ChevronDown, ChevronRight, Eye, Edit, Play, Instagram, Music, Youtube } from "lucide-react"

interface Campaign {
  id: number
  name: string
  description: string
  budget: number
  createdAt: string
  platforms: Array<{
    name: string
    contentTypes: string[]
  }>
  status: "active" | "draft" | "completed"
}

interface CampaignsTableProps {
  campaigns: Campaign[]
}

const getStatusColor = (status: string) => {
  switch (status) {
    case "active":
      return "bg-green-500"
    case "draft":
      return "bg-gray-500"
    case "completed":
      return "bg-blue-500"
    default:
      return "bg-gray-500"
  }
}

const getStatusLabel = (status: string) => {
  switch (status) {
    case "active":
      return "Aktivna"
    case "draft":
      return "Nacrt"
    case "completed":
      return "<PERSON>av<PERSON><PERSON><PERSON>"
    default:
      return status
  }
}

const getPlatformIcon = (platform: string) => {
  switch (platform.toLowerCase()) {
    case "instagram":
      return <Instagram className="w-4 h-4 text-pink-500" />
    case "tiktok":
      return <Music className="w-4 h-4 text-black" />
    case "youtube":
      return <Youtube className="w-4 h-4 text-red-500" />
    default:
      return null
  }
}

export function CampaignsTable({ campaigns }: CampaignsTableProps) {
  const [expandedRows, setExpandedRows] = useState<Set<number>>(new Set())

  const toggleRow = (id: number) => {
    const newExpanded = new Set(expandedRows)
    if (newExpanded.has(id)) {
      newExpanded.delete(id)
    } else {
      newExpanded.add(id)
    }
    setExpandedRows(newExpanded)
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border">
      <Table>
        <TableHeader className="hidden md:table-header-group">
          <TableRow>
            <TableHead className="w-8"></TableHead>
            <TableHead>Naziv kampanje</TableHead>
            <TableHead>Datum kreiranja</TableHead>
            <TableHead>Budžet</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Akcije</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {campaigns.map((campaign) => (
            <>
              <TableRow
                key={campaign.id}
                className="cursor-pointer hover:bg-gray-50"
                onClick={() => toggleRow(campaign.id)}
              >
                <TableCell>
                  {expandedRows.has(campaign.id) ? (
                    <ChevronDown className="w-4 h-4" />
                  ) : (
                    <ChevronRight className="w-4 h-4" />
                  )}
                </TableCell>
                <TableCell className="font-medium">{campaign.name}</TableCell>
                <TableCell className="hidden md:table-cell">{campaign.createdAt}</TableCell>
                <TableCell className="hidden md:table-cell">{campaign.budget} €</TableCell>
                <TableCell className="hidden md:table-cell">
                  <Badge className={`${getStatusColor(campaign.status)} text-white border-0`}>
                    {getStatusLabel(campaign.status)}
                  </Badge>
                </TableCell>
                <TableCell className="text-right hidden md:table-cell">
                  <div className="flex justify-end gap-2">
                    <Button variant="outline" size="sm" onClick={(e) => e.stopPropagation()}>
                      <Eye className="w-4 h-4 mr-1" />
                      Pregled
                    </Button>
                    <Button variant="outline" size="sm" onClick={(e) => e.stopPropagation()}>
                      <Edit className="w-4 h-4 mr-1" />
                      Uredi
                    </Button>
                    <Button
                      className="bg-blue-600 hover:bg-blue-700 text-white border-0"
                      size="sm"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <Play className="w-4 h-4 mr-1" />
                      Aktiviraj
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
              {expandedRows.has(campaign.id) && (
                <TableRow>
                  <TableCell colSpan={6} className="bg-gray-50 p-6">
                    <div className="space-y-4">
                      <div className="md:hidden space-y-3 mb-4">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium text-gray-500">Datum kreiranja:</span>
                          <span className="text-sm text-gray-900">{campaign.createdAt}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium text-gray-500">Budžet:</span>
                          <span className="text-sm text-gray-900">{campaign.budget} €</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium text-gray-500">Status:</span>
                          <Badge className={`${getStatusColor(campaign.status)} text-white border-0`}>
                            {getStatusLabel(campaign.status)}
                          </Badge>
                        </div>
                        <div className="flex gap-2 pt-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-1 bg-transparent"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Eye className="w-4 h-4 mr-1" />
                            Pregled
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-1 bg-transparent"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Edit className="w-4 h-4 mr-1" />
                            Uredi
                          </Button>
                          <Button
                            className="bg-blue-600 hover:bg-blue-700 text-white border-0 flex-1"
                            size="sm"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Play className="w-4 h-4 mr-1" />
                            Aktiviraj
                          </Button>
                        </div>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-2">Opis kampanje</h4>
                        <p className="text-gray-600">{campaign.description}</p>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-3">Platforme i tipovi sadržaja</h4>
                        <div className="space-y-3">
                          {campaign.platforms.map((platform, index) => (
                            <div key={index} className="flex items-center gap-3">
                              <div className="flex items-center gap-2">
                                {getPlatformIcon(platform.name)}
                                <span className="font-medium text-gray-700">{platform.name}:</span>
                              </div>
                              <div className="flex gap-2">
                                {platform.contentTypes.map((type, typeIndex) => (
                                  <Badge key={typeIndex} variant="secondary" className="bg-gray-200 text-gray-700">
                                    {type}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
