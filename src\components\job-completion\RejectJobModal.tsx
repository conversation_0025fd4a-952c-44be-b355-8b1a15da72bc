'use client';

import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';
import { rejectJobCompletion } from '@/lib/job-completions';

interface RejectJobModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  jobCompletionId: string;
  influencerName: string;
}

export function RejectJobModal({
  isOpen,
  onClose,
  onSuccess,
  jobCompletionId,
  influencerName,
}: RejectJobModalProps) {
  const [businessNotes, setBusinessNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (!businessNotes.trim()) {
      toast.error('Molimo objasnite razlog odbacivanja');
      return;
    }

    setIsSubmitting(true);
    try {
      const { error } = await rejectJobCompletion(
        jobCompletionId,
        businessNotes.trim()
      );

      if (error) {
        toast.error('Greška pri odbacivanju rada');
        return;
      }

      toast.success('Rad je odbačen i influencer je obaviješten');
      onSuccess();
      onClose();

      // Reset form
      setBusinessNotes('');
    } catch (error) {
      toast.error('Dogodila se neočekivana greška');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px] relative overflow-hidden bg-gradient-to-br from-purple-50/95 via-pink-50/90 to-purple-100/95 dark:from-purple-950/95 dark:via-pink-950/90 dark:to-purple-900/95 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
        <div className="relative">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              Odbaci rad
            </DialogTitle>
            <DialogDescription className="text-gray-700 dark:text-gray-300">
              Odbacujete rad od <strong>{influencerName}</strong>. Molimo
              objasnite razlog odbacivanja.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label
                htmlFor="businessNotes"
                className="text-gray-900 dark:text-gray-100"
              >
                Razlog odbacivanja *
              </Label>
              <Textarea
                id="businessNotes"
                placeholder="Objasnite zašto odbacujete ovaj rad..."
                value={businessNotes}
                onChange={e => setBusinessNotes(e.target.value)}
                rows={4}
                required
                className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50 dark:border-purple-800/30"
              />
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Ovaj komentar će biti poslan influenceru.
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
              className="bg-white/70 dark:bg-gray-800/50 hover:bg-white dark:hover:bg-gray-800/70 border-purple-200/50 dark:border-purple-700/50"
            >
              Otkaži
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting || !businessNotes.trim()}
              className="bg-gradient-to-r from-red-500 via-pink-500 to-red-600 hover:from-red-600 hover:via-pink-600 hover:to-red-700 text-white border-0"
            >
              {isSubmitting ? 'Odbacuje se...' : 'Odbaci rad'}
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
}
