import React from 'react';
import { cn } from '@/lib/utils';

interface Tab {
  name: string;
  value: string;
  count: number;
}

interface GradientTabsProps {
  tabs: Tab[];
  activeTab: string;
  onTabChange: (value: string) => void;
  className?: string;
}

export function GradientTabs({
  tabs,
  activeTab,
  onTabChange,
  className,
}: GradientTabsProps) {
  return (
    <div
      className={cn(
        'flex gap-2 overflow-x-auto scrollbar-hide pb-2',
        className
      )}
    >
      {tabs.map(tab => (
        <button
          key={tab.value}
          onClick={() => onTabChange(tab.value)}
          className={cn(
            'flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 hover:scale-[1.02] whitespace-nowrap flex-shrink-0',
            activeTab === tab.value
              ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600'
              : 'bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 text-purple-700 hover:bg-gradient-to-r hover:from-purple-100 hover:to-pink-100 hover:border-purple-300'
          )}
        >
          <span>{tab.name}</span>
          <span
            className={cn(
              'px-2 py-1 rounded-full text-xs font-semibold',
              activeTab === tab.value
                ? 'bg-white/20 text-white'
                : 'bg-gradient-to-r from-violet-100 to-purple-100 text-violet-700'
            )}
          >
            {tab.count}
          </span>
        </button>
      ))}
    </div>
  );
}
