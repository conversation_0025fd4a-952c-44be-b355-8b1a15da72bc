import { NextRequest, NextResponse } from 'next/server';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get('userId');
    const userType = searchParams.get('userType') as 'business' | 'influencer';

    if (!userId || !userType) {
      return NextResponse.json({ error: 'Missing userId or userType' }, { status: 400 });
    }

    const { 
      canActivateCampaign, 
      canInfluencerApply, 
      canInfluencerReceiveOffers, 
      canBusinessSendCustomOffers 
    } = await import('@/lib/subscriptions');

    let results: any = { userId, userType };

    if (userType === 'business') {
      results.campaignActivation = await canActivateCampaign(userId);
      results.customOffers = await canBusinessSendCustomOffers(userId);
    } else {
      results.campaignApplication = await canInfluencerApply(userId);
      results.receiveOffers = await canInfluencerReceiveOffers(userId);
    }

    return NextResponse.json(results);
  } catch (error: any) {
    console.error('Test limits error:', error);
    return NextResponse.json({ 
      error: 'Test failed', 
      details: error.message 
    }, { status: 500 });
  }
}